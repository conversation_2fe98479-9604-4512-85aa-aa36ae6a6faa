[{"account": "***************", "account_string": "0230 0113 7100 502", "name": "ADIXXXXXXXXXXXXXXLTI", "currency": "Rp", "card_number": "5221XXXXXXXX7777", "card_number_string": "5221 XXXX XXXX 7777", "product_type": "BritA<PERSON>", "account_type": "SA", "sc_code": "TA", "default": 0, "alias": "", "minimum_balance": 50000, "limit": -1, "limit_string": "", "image_name": "BritAma.png", "image_path": "http://172.18.136.93:4010/brimo-asset/account_logo/BritAma.png"}, {"account": "***************", "account_string": "0230 0100 1674 308", "name": "Infinite", "currency": "Rp", "card_number": "5221XXXXXXXX7777", "card_number_string": "5221 XXXX XXXX 7777", "product_type": "Simpedes", "account_type": "SA", "sc_code": "SU", "default": 0, "alias": "", "minimum_balance": 25000, "limit": -1, "limit_string": "", "image_name": "Simpedes-Umum.png", "image_path": "http://172.18.136.93:4010/brimo-asset/account_logo/Simpedes-Umum.png"}, {"account": "***************", "account_string": "0230 0113 7104 506", "name": "ADIXXXXXXXXXXXXXXLTI", "currency": "Rp", "card_number": "5221XXXXXXXX7777", "card_number_string": "5221 XXXX XXXX 7777", "product_type": "BritA<PERSON>", "account_type": "SA", "sc_code": "TA", "default": 0, "alias": "", "minimum_balance": 50000, "limit": -1, "limit_string": "", "image_name": "BritAma.png", "image_path": "http://172.18.136.93:4010/brimo-asset/account_logo/BritAma.png"}]