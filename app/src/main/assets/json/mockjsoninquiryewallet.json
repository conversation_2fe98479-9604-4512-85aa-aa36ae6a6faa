{"account_list": [{"account": "***************", "account_string": "0230 0100 1674 308", "name": "Infinite", "currency": "Rp", "card_number": "5221XXXXXXXX7777", "card_number_string": "5221 XXXX XXXX 7777", "product_type": "Simpedes", "account_type": "SA", "sc_code": "SU", "default": 1, "alias": "", "minimum_balance": 25000, "limit": -1, "limit_string": "", "image_name": "Simpedes-Umum.png", "image_path": "http://*************:4010/brimo-asset/account_logo/Simpedes-Umum.png"}, {"account": "***************", "account_string": "0230 0113 7097 505", "name": "ADIXXXXXXXXXXXXXXLTI", "currency": "Rp", "card_number": "5221XXXXXXXX7777", "card_number_string": "5221 XXXX XXXX 7777", "product_type": "BritA<PERSON>", "account_type": "SA", "sc_code": "TA", "default": 0, "alias": "", "minimum_balance": 50000, "limit": -1, "limit_string": "", "image_name": "BritAma.png", "image_path": "http://*************:4010/brimo-asset/account_logo/BritAma.png"}], "billing_detail": [], "billing_detail_open": [{"list_type": "image", "icon_name": "ewallet_gopay.png", "icon_path": "http://*************:4010/brimo-asset/ewallet/ewallet_gopay.png", "title": "GP-***********", "subtitle": "Gopay Customer", "description": "***********"}], "billing_amount": [{"name": "Total", "value": "1000", "style": ""}], "billing_amount_detail": [{"name": "Nominal", "value": "0", "style": ""}, {"name": "<PERSON><PERSON><PERSON>", "value": "1000", "style": ""}], "billing_purchase_detail": [{"name": "<PERSON><PERSON><PERSON>", "style": "", "value": "GP-***********"}, {"name": "Nominal", "style": "", "value": ""}, {"name": "<PERSON><PERSON><PERSON>", "style": "", "value": "Rp1.000"}], "option_amount": [{"name": "Rp10.000", "style": "", "value": 10000}, {"name": "Rp30.000", "style": "", "value": 30000}, {"name": "Rp50.000", "style": "", "value": 50000}, {"name": "Rp100.000", "style": "", "value": 100000}, {"name": "Rp500.000", "style": "", "value": 500000}, {"name": "Rp1.000.000", "style": "", "value": 1000000}], "name_default": "GP-***********", "open_payment": true, "is_billing": false, "minimum_payment": false, "row_data_show": 0, "saved": "", "amount": 0, "amount_string": "Rp0", "minimum_amount": 0, "minimum_amount_string": "Rp0", "admin_fee": 1000, "admin_fee_string": "Rp1.000", "pay_amount": 1000, "pay_amount_string": "Rp1.000", "minimum_transaction": 10000, "minimum_transaction_string": "Rp10.000", "reference_number": "323528599459"}