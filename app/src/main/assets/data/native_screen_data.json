{"Transfer": {"className": "PaymentTransferViewController", "activityName": "id.co.bri.brimo.payment.app.PaymentActivity"}, "BRIVA": {"className": "PaymentBrivaViewController", "activityName": "id.co.bri.brimo.payment.app.PaymentActivity"}, "CreditAndData": {"routerName": "CreditAndDataViewRouter", "activityName": "id.co.bri.brimo.ui.activities.FormKreditActivity"}, "Asuransi": {"className": "AsuransiLandingViewController", "activityName": "id.co.bri.brimo.ui.activities.asuransirevamp.DashboardAsuransiActivity"}, "BayarPinjaman": {"routerName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activityName": "id.co.bri.brimo.ui.activities.AllPromoActivity"}, "BPJS": {"storyboard": "PaymentBPJS", "identifier": "FormPaymentBPJS", "className": "FormPaymentBPJSVC", "activityName": "id.co.bri.brimo.ui.activities.FormBpjsActivity"}, "Cicilan": {"storyboard": "PaymentCicilan", "identifier": "FormPaymentCicilan", "className": "FormPaymentCicilanVC", "activityName": "id.co.bri.brimo.ui.activities.FormCicilanActivity"}, "IPLAndProperty": {"className": "PropertyRecentAndSavedViewController", "activityName": "id.co.bri.brimo.ui.activities.property.FormPropertyActivity"}, "KAI": {"storyboard": "PaymentKAI", "identifier": "FormPaymentKAI", "className": "FormPaymentKAIVC", "activityName": "id.co.bri.brimo.ui.activities.FormKaiActivity"}, "BayarKartuKredit": {"storyboard": "CreditCard", "identifier": "FormCreditCard", "className": "FormCreditCardVC", "activityName": "id.co.bri.brimo.ui.activities.FormKreditActivity"}, "Listrik": {"className": "ListrikLandingViewController", "activityName": "id.co.bri.brimo.ui.activities.listrikrevamp.reskin.FormListrikReskinActivity"}, "PBB": {"storyboard": "PaymentPBB", "identifier": "FormPBB", "className": "FormPBBVC", "activityName": "id.co.bri.brimo.ui.activities.pbb.FormPbbActivity"}, "PDAM": {"className": "BillPdamViewController", "activityName": "id.co.bri.brimo.ui.activities.FormPdamReskinActivity"}, "Pendidikan": {"className": "PendidikanRecentAndSavedViewController", "activityName": "id.co.bri.brimo.ui.activities.pendidikanrevamp.FormPendidikanRevampActivity"}, "PenerimaanNegara": {"storyboard": "StateRevenue", "identifier": "FormMPN", "className": "FormMPNVC", "activityName": "id.co.bri.brimo.ui.activities.FormMpnActivity"}, "PascaBayar": {"storyboard": "PostpaidPulsa", "identifier": "NewFormPostpaidPulsa", "className": "NewFormPostpaidPulsaVC", "activityName": "id.co.bri.brimo.ui.activities.form.FormGeneralRevampActivity"}, "PajakDaerah": {"className": "TaxHorekaFormViewController", "activityName": "id.co.bri.brimo.ui.activities.pajakhoreka.FormPajakHorekaActivity"}, "SNPMB": {"activityName": "id.co.bri.brimo.ui.activities.FormLTMPTReskinActivity"}, "Telkom": {"activityName": "id.co.bri.brimo.ui.activities.telkomrevamp.FormTelkomRevampActivity"}, "TVKabelAndInternet": {"activityName": "id.co.bri.brimo.ui.activities.FormTelevisiReskinActivity"}, "Brizzi": {"activityName": "id.co.bri.brimo.payment.app.PaymentActivity"}, "EWallet": {"routerName": "EWalletRouter", "activityName": "id.co.bri.brimo.ui.activities.dompetdigitalreskin.FormDompetDigitalReskinActivity"}, "PulsaAndData": {"routerName": "CreditAndDataViewRouter", "activityName": "id.co.bri.brimo.ui.activities.pulsadata.reskin.FormPulsaDataReskinActivity"}, "SetorTunai": {"activityName": "id.co.bri.brimo.ui.activities.cardless.FormSetorTunaiActivity"}, "TarikTunai": {"activityName": "id.co.bri.brimo.ui.activities.tartun.FormTarikTunaiActivity"}, "Donasi": {"activityName": "id.co.bri.brimo.ui.activities.donasirevamp.FormDonasiRevampActivity"}, "TabunganValas": {"activityName": "id.co.bri.brimo.ui.activities.FormKonversiVallasActivity"}, "AccountSettings": {"className": "AccountSettingsBridgingViewController", "activityName": "id.co.bri.brimo.ui.activities."}, "Mutation": {"storyboard": "Dashboard", "identifier": "MutationTabBar", "className": "BrimoTabBarController", "activityName": "id.co.bri.brimo.ui.activities."}, "QRIS": {"className": "QrisScanViewController", "activityName": "id.co.bri.brimo.ui.activities.FormQrActivity"}, "OpenAccount": {"className": "OpenNewAccountVC", "activityName": "id.co.bri.brimo.ui.activities."}, "AddRekening": {"activityName": "id.co.bri.brimo.ui.activities.bukarekeningreskin.TabunganNewSkinActivity"}}