package id.co.bri.brimo.ui.activities.newskinonboarding.pin

import android.annotation.SuppressLint
import android.os.Bundle
import id.co.bri.brimo.databinding.ActivityEnterPinBinding
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.pin.PinEntryFragment
import id.co.bri.brimo.ui.fragments.pin.PinEntryListener


class EnterCreatePinConfirmActivity : NewSkinBaseActivity(), PinEntryListener {

    private lateinit var binding: ActivityEnterPinBinding
    private var createdPin: String? = null

    @SuppressLint("CommitTransaction")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEnterPinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        createdPin = intent.getStringExtra("EXTRA_CREATED_PIN")

        val pinFragment = PinEntryFragment().apply {
            headerTitle = "PIN"
            descriptionText = "Konfirmasi PIN baru"
            infoText = "Silakan konfirmasi PIN yang sudah kamu buat"
            isForgotPinVisible = false
            setPinEntryListener(this@EnterCreatePinConfirmActivity)
        }

        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainerPin.id, pinFragment)
            .commit()
    }

    override fun onPinComplete(pin: String) {
        if (pin == createdPin) {
            setResult(RESULT_OK)
            finish()
        } else {
            val fragment = supportFragmentManager.findFragmentById(binding.fragmentContainerPin.id)
            if (fragment is PinEntryFragment) {
                fragment.setErrorText("PIN tidak sama")
            }
        }
    }


    override fun onPinError(errorMessage: String) {

    }

    override fun onForgotPinClicked() {

    }
}