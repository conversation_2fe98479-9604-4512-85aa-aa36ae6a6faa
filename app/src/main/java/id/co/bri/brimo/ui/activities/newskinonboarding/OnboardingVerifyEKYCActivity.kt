package id.co.bri.brimo.ui.activities.newskinonboarding

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.newskinonboarding.IVerifyEKYCPresenter
import id.co.bri.brimo.contract.IView.newskinonboarding.IVerifyEKYCView
import id.co.bri.brimo.databinding.ActivityVerifyEkycBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.CheckStatusKycRes
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.InitializeZolozResponse
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.PresignUrlLivenessResponse
import id.co.bri.brimo.models.apimodel.response.newskinonboarding.ResultLiveness
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.changepassnewskin.ChangePassActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.pin.EnterCreatePinActivity
import id.co.bri.brimo.ui.activities.onboardingnewskin.BuatKataKunciActivity
import id.co.bri.brimo.ui.activities.onboardingnewskin.VerifikasiWajahActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.liveness.PrivyLiveness
//import id.co.bri.brimo.util.liveness.VidaLiveness
import id.co.bri.brimo.util.liveness.ZolozLiveness
//import id.vida.liveness.dto.VidaLivenessResponse
import javax.inject.Inject

class OnboardingVerifyEKYCActivity : NewSkinBaseActivity(),
    IVerifyEKYCView, ZolozLiveness.Callback {

    @Inject
    lateinit var presenter: IVerifyEKYCPresenter<IVerifyEKYCView>

    private lateinit var binding: ActivityVerifyEkycBinding
    private lateinit var zolozManager: ZolozLiveness
    private lateinit var preSignedResponse: PresignUrlLivenessResponse
    private var resultLiveness: ResultLiveness? = null

    companion object {
        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, OnboardingVerifyEKYCActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_UBAH_PIN)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityVerifyEkycBinding.inflate(layoutInflater)
        setContentView(binding.root)

        crashZolozHandler()
        injectDependencies()
        setupView()
        setupButton()
    }

    private fun injectDependencies() {
        activityComponent.inject(this)
        presenter.apply {
            view = this@OnboardingVerifyEKYCActivity
            start()
            setUrlPreSigned(GeneralHelper.getString(R.string.url_onboarding_brimo_get_presign_url_kyc_v4))
            setUrlInitializeZoloz(GeneralHelper.getString(R.string.url_onboarding_brimo_initialize_liveness_zoloz_v4))
            setUrlSendKyc(GeneralHelper.getString(R.string.url_onboarding_brimo_send_kyc_v4))
        }

        zolozManager = ZolozLiveness(this, this)
    }

    private fun setupView() {
        binding.apply {
            GeneralHelperNewSkin.setToolbarWithoutNav(
                toolbar.toolbar,
                GeneralHelper.getString(R.string.panduan_verifikasi_wajah)
            )
        }
    }

    private fun setupButton() {
        binding.btnLanjutkan.setOnClickListener{
            onSuccessStartZoloz()
        }
    }

    private fun crashZolozHandler() {
        Thread.setDefaultUncaughtExceptionHandler { thread: Thread?, throwable: Throwable? ->
            if (!GeneralHelper.isProd()) {
                Log.e("GlobalException", "Unhandled exception caught", throwable)
            }
        }
    }

    private var startActivityResult: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    override fun onSuccessGetPreSigned(preSignedResponse: PresignUrlLivenessResponse) {
        this.preSignedResponse = preSignedResponse
//        checkLiveness(preSignedResponse.partnerId)
        if (preSignedResponse.partnerId == Constant.LivenessType.ZOLOZ.type) {
            presenter.getInitializeZoloz(zolozManager.getMetaInfo())
        } else hideProgress()
    }

    override fun onAdditionalImages(additionalImages: ByteArray, number: Int) {
        //do nothing
    }

    override fun onSuccessStartZoloz(response: InitializeZolozResponse) {
        val transactionId = response.transactionId
        val clientCfg = response.clientCfg

        if (transactionId.isNullOrBlank() || clientCfg.isNullOrBlank()) {
            hideProgress()
            GeneralHelper.showSnackBar(
                binding.content,
                GeneralHelper.getString(R.string.error_zoloz)
            )
            return
        } else {
//            setEventAppsFlyer(
//                AppFlyerEvent.ONBOARDING_SDK_ZOLOZ_START.eventName,
//                presenter.getDeviceId()
//            )
            zolozManager.start(clientCfg)
        }
    }

    fun onSuccessStartZoloz() {
        val transactionId = "G000000006FFC20250710000000608513469350"
        val clientCfg = "{\"clientStartIndex\":1,\"flowId\":\"G000000006f026a71d3c967833d9282b9d0aab4ee3\",\"factorContext\":{\"GATEWAY_URL\":\"https://id-production-zmgs.zoloz.com/zmgs/v2/sec\",\"WORKSPACE_ID\":\"default\",\"APP_ID\":\"ZOLOZ_EKYC_GLOBAL\",\"faceZimId\":\"G000000006f026a71d3c967833d9282b9d0aab4ee3\",\"clientId\":\"2188444625436482\",\"merchantId\":\"40427\",\"keyMeta\":\"{\\\"resourceCode\\\":\\\"00000000\\\",\\\"mid\\\":\\\"40427\\\",\\\"keyVer\\\":\\\"v1\\\"}\",\"zimInitResp\":\"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\",\"NEARX_GATEWAY_URL\":\"https://nearx-id-sail-api.zoloz.com/mgw2.htm\",\"authType\":\"ZFACE\",\"ENV\":\"0\",\"REMOTELOG_URL\":\"https://id-production-zmgs.zoloz.com/loggw/logUpload.do\"},\"tasks\":[{\"inputParams\":{\"zimId\":{\"value\":\"faceZimId\",\"pipeType\":\"context\"},\"zimInitResp\":{\"value\":\"zimInitResp\",\"pipeType\":\"context\"},\"validateDelegate\":{\"value\":false,\"pipeType\":\"constant\"},\"useDefaultExit\":{\"value\":true,\"pipeType\":\"constant\"}},\"name\":\"ZFace\",\"index\":1,\"type\":\"nativeTask\"}]}"

        if (transactionId.isNullOrBlank() || clientCfg.isNullOrBlank()) {
            hideProgress()
            GeneralHelper.showSnackBar(
                binding.content,
                GeneralHelper.getString(R.string.error_zoloz)
            )
            return
        } else {
//            setEventAppsFlyer(
//                AppFlyerEvent.ONBOARDING_SDK_ZOLOZ_START.eventName,
//                presenter.getDeviceId()
//            )
            zolozManager.start(clientCfg)
        }
    }

    override fun onSuccessUploadMinio() {
        presenter.sendKyc(
            preSignedResponse.partnerId,
            Gson().toJson(resultLiveness)
        )
    }

    override fun onSuccessSendKyc(checkStatus: CheckStatusKycRes) {
        val intent = Intent(this, BuatKataKunciActivity::class.java)
        intent.putExtra(Constant.GENRES, checkStatus)
        startActivityResult.launch(intent)
    }

    private fun showDialogExceptionZoloz() {
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = "",
            imgName = "ic_rekam_wajah_gagal",
            titleTxt = GeneralHelper.getString(R.string.wajah_tidak_dikenali),
            subTitleTxt = GeneralHelper.getString(R.string.desc_verifikasi_ulang),
            btnFirstFunction = { presenter.getPreSigned() },
            isClickableOutside = false,
            firstBtnTxt = GeneralHelper.getString(R.string.verifikasi_ulang)
        )
    }

    override fun onExceptionZoloz() {
        showDialogExceptionZoloz()
    }

    override fun onZolozResult(result: ZolozLiveness.Result) {
        when (result) {
            is ZolozLiveness.Result.Success -> {
                showProgress()
//                presenter.sendKyc(partnerId, "")
            }

            is ZolozLiveness.Result.Error -> {
                // Handle error
                val intent = Intent(this@OnboardingVerifyEKYCActivity, EnterCreatePinActivity::class.java)
                startActivityResult.launch(intent)
            }
        }
    }
}