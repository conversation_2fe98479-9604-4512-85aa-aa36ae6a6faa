package id.co.bri.brimo.ui.activities.virtualdebitcard

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.fragment.app.FragmentTransaction
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import androidx.viewpager2.widget.CompositePageTransformer
import androidx.viewpager2.widget.MarginPageTransformer
import androidx.viewpager2.widget.ViewPager2
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.virtualdebitcard.CardAdapter
import id.co.bri.brimo.adapters.virtualdebitcard.OnBoardingVDCAdapter
import id.co.bri.brimo.adapters.virtualdebitcard.SpecificationVDCAdapter
import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.IOnBoardingVDCPresenter
import id.co.bri.brimo.contract.IView.virtualdebitcard.IOnBoardingVDCView
import id.co.bri.brimo.databinding.ActivityOnboardingVirtualCardBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.providers.CardType
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.GeneralResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.AccountResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.CheckStatusCreateVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.ListProductVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.ListVDCDataResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.VirtualDebitCard
import id.co.bri.brimo.ui.activities.ListRekeningCategoryActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity.Companion.launchIntent
import id.co.bri.brimo.ui.activities.pengelolaankartu.ChangePINDebitActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogInformation
import id.co.bri.brimo.ui.fragments.BottomDialogInformationAccountFragment
import id.co.bri.brimo.ui.fragments.BottomDialogSelectAccountFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import javax.inject.Inject


class OnBoardingVDCActivity : BaseActivity(), IOnBoardingVDCView,
    BottomDialogSelectAccountFragment.OnCallback,
    BottomDialogInformationAccountFragment.OnCallback, DialogInformation.OnActionClick {

    @Inject
    lateinit var presenter: IOnBoardingVDCPresenter<IOnBoardingVDCView>

    private lateinit var binding: ActivityOnboardingVirtualCardBinding

    private val onBoardingVDCAdapter = OnBoardingVDCAdapter()
    private lateinit var adapterCard: CardAdapter<VirtualDebitCard>
    private val specificationCardAdapter = SpecificationVDCAdapter()

    private lateinit var listVDC: List<VirtualDebitCard>
    var listAccount: MutableList<AccountModel> = mutableListOf()
    private lateinit var skeletonScreen: SkeletonScreen
    private val gson = Gson()

    private var mPosition = 0
    private var isRefresh = false

    companion object {
        const val CHANGE_PIN_KEY = "CHANGEPIN"
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityOnboardingVirtualCardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        getListVirtualDebitCard()
    }

    private fun getListVirtualDebitCard() {
        presenter.setUrlGetListVirtualCard(getString(R.string.url_v1_get_list_vdc))
        presenter.getListVirtualCard("")
    }

    private fun getListAccount() {
        presenter.setUrlGetListAccount(getString(R.string.url_v1_get_list_account_vdc))
        presenter.getListAccount("")
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbar.toolbar, getString(R.string.virtual_debit_card)
        )

        binding.btnSubmit.setOnClickListener {
            getListAccount()
        }

        binding.swipeRefresh.apply {
            setOnRefreshListener {
                isRefresh = true
                getListVirtualDebitCard()
            }
            isRefreshing = false
        }

        setupBackground(R.dimen.bg_upsize)
    }

    private fun setupBackground(mHeight: Int) {
        val newHeightInPixels = resources.getDimensionPixelSize(mHeight)
        val layoutParams = binding.view.layoutParams
        layoutParams.height = newHeightInPixels
        binding.view.layoutParams = layoutParams
    }

    private fun setUpViewPagerCard() {
        adapterCard = CardAdapter(listVDC,
            imageCardProvider = { card: VirtualDebitCard -> card.imageCardVertical },
            statusProvider = { card: VirtualDebitCard -> card.statusRequest }
        )
        binding.viewPager2.apply {
            adapter = adapterCard
            offscreenPageLimit = listVDC.size
            clipToPadding = false
            clipChildren = false
            getChildAt(0).overScrollMode = RecyclerView.OVER_SCROLL_NEVER
        }
    }

    private fun setUpTransformer() {
        val transformer = CompositePageTransformer()
        transformer.addTransformer(MarginPageTransformer(40))
        transformer.addTransformer { page, position ->
            val r = 1 - kotlin.math.abs(position)
            page.scaleY = 0.85f + r * 0.14f
        }

        binding.viewPager2.setPageTransformer(transformer)
    }

    private fun initiateDataViewPager() {
        binding.viewPager2.registerOnPageChangeCallback(object : ViewPager2.OnPageChangeCallback() {
            override fun onPageSelected(position: Int) {
                super.onPageSelected(position)
                initiateData(position)
                mPosition = position
            }
        })

        binding.dotsIndicator.setViewPager2(binding.viewPager2)
    }

    private fun initiateData(position: Int) {
        val vdc = listVDC[position]
        binding.cardNameTxt.text = vdc.cardType
        setupAdapterInformationCard(vdc)

        val isStatusProcess = (vdc.statusRequest == "0")
        binding.clCheckStatus.apply {
            visibility = if (isStatusProcess) View.VISIBLE else View.GONE
            if (isStatusProcess) {
                binding.btnCheckStatus.setOnClickListener { checkStatusCard(vdc.account) }
            }
        }

        binding.clSeeDetails.apply {
            visibility = if (!isStatusProcess) View.VISIBLE else View.GONE
            if (!isStatusProcess) {
                binding.btnSeeDetails.setOnClickListener { getDetailVDC(vdc.cardNumber) }
            }
        }
    }

    private fun setupAdapterInformationCard(vdc: VirtualDebitCard) {
        binding.rvListDetail.apply {
            if (vdc.detailCardDataList.isNullOrEmpty()) {
                View.GONE
            } else {
                View.VISIBLE
                layoutManager =
                    LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                adapter = specificationCardAdapter
                specificationCardAdapter.informationCard = vdc.detailCardDataList.toMutableList()
                specificationCardAdapter.notifyDataSetChanged()
            }
        }
        specificationCardAdapter.notifyDataSetChanged()
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stop()
    }

    private fun openVCHavingAccount() {
        val dialogFragment = BottomDialogSelectAccountFragment(listAccount, this)
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = true
    }

    override fun clickOpenNewSaving() {
        launchIntent(this)
    }

    override fun clickAccount(accountModel: AccountModel) {
        getListProductVDC(accountModel.acoount)
    }

    override fun updateAccount() {
        ListRekeningCategoryActivity.launchIntent(this)
    }

    override fun showSkeleton() {
        setupBackground(R.dimen.bg_upsize)
        binding.clSkeleton.visibility = View.VISIBLE
        binding.layoutButton.visibility = View.GONE
        skeletonScreen = Skeleton.bind(binding.clSkeleton)
            .shimmer(true)
            .angle(5)
            .duration(1200)
            .load(R.layout.item_skeleton_card)
            .show()
        binding.clOnboarding.visibility = View.GONE
        binding.clListVirtualCard.visibility = View.GONE
    }

    override fun onSuccessGetListVirtualCard(response: ListVDCDataResponse) {
        listVDC = response.virtualDebitCards
        setupBackground(R.dimen.bg_upsize)
        setUpViewPagerCard()
        setUpTransformer()
        if (!isRefresh) {
            initiateData(0)
        }
        initiateDataViewPager()
        binding.layoutButton.visibility = View.VISIBLE
        skeletonScreen.hide()
        binding.clSkeleton.visibility = View.GONE
        binding.swipeRefresh.isRefreshing = false
        binding.btnSubmit.text = getString(R.string.add_virtual_debit_card)
        binding.clOnboarding.visibility = View.GONE
        binding.clListVirtualCard.visibility = View.VISIBLE

        isRefresh = false

        var isAnyStatusProcess = false
        for (debitCard in response.virtualDebitCards) {
            if (debitCard.statusRequest == "0") {
                isAnyStatusProcess = true
                break // Exit the loop if "false" status is found
            }
        }

        if (isAnyStatusProcess) disableButton() else enableButton()
    }

    private fun getDetailVDC(cardNumber: String) {
        presenter.setUrlDetailVDC(getString(R.string.url_v1_detail_vdc))
        presenter.getDetailVDC(cardNumber = cardNumber)
    }

    private fun checkStatusCard(accountNumber: String) {
        presenter.setUrlCheckStatusCreateVDC(getString(R.string.url_v1_check_status_create_vdc))
        presenter.checkStatusCreateVDC(account = accountNumber)
    }

    override fun notFoundListVirtualCard(response: GeneralResponse) {
        setupBackground(R.dimen.bg_upsize)
        binding.layoutButton.visibility = View.VISIBLE
        skeletonScreen.hide()
        binding.clSkeleton.visibility = View.GONE
        binding.swipeRefresh.isRefreshing = false
        binding.btnSubmit.text = getString(R.string.open_now)
        binding.clListVirtualCard.visibility = View.GONE
        binding.clOnboarding.visibility = View.VISIBLE

        if (response.imageName.isNullOrEmpty()) {
            Glide.with(this)
                .load(response.imagePath)
                .diskCacheStrategy(DiskCacheStrategy.NONE)
                .skipMemoryCache(true)
                .placeholder(R.drawable.ic_regis_brimo)
                .into(binding.ivOnboarding)
        } else {
            binding.ivOnboarding.setImageResource(
                GeneralHelper.getImageId(
                    binding.root.context,
                    response.imageName
                )
            )
        }

        binding.tvDescription.text = response.description

        binding.rvInfo.apply {
            View.VISIBLE
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = onBoardingVDCAdapter
        }

        onBoardingVDCAdapter.notFoundInfoList = response.detail.toMutableList()
    }

    override fun onSuccessGetListVirtualCardAccount(response: AccountResponse) {
        listAccount = response.accounts.toMutableList()

        openVCHavingAccount()
    }

    override fun notFoundListVirtualCardAccount(response: GeneralResponse) {
        val dialogFragment = BottomDialogInformationAccountFragment(
            R.drawable.img_sorry_with_card, response.title,
            this
        )
        dialogFragment.show(supportFragmentManager, "")
        dialogFragment.isCancelable = true
    }

    override fun onSuccessGetListProductVDC(response: ListProductVDCResponse) {
        val responseJson = gson.toJson(response)

        val intent = Intent(this, SelectProductVDCActivity::class.java)
        intent.putExtra(Constant.TAG_CONTENT, responseJson)
        startActivityIntent.launch(intent)
    }

    override fun notFoundListProductVDC(response: GeneralResponse) {
        TODO("Not yet implemented")
    }

    override fun onSuccessCheckStatusVDC(response: CheckStatusCreateVDCResponse) {
        val responseJson = gson.toJson(response)

        val intent = Intent(this, SuccessPageVDCActivity::class.java)
        intent.putExtra(Constant.TAG_CONTENT, responseJson)
        startActivityIntent.launch(intent)
    }

    override fun onProcessCheckStatusVDC(response: GeneralResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = response.imagePath,
            imgName = response.imageName,
            titleTxt = response.title,
            subTitleTxt = response.description,
            btnFirstFunction = {},
            isClickableOutside = false,
            firstBtnTxt = getString(R.string.ok)
        )
    }

    override fun onMaxLimitCheckStatusVDC(response: GeneralResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
            fragmentManager = supportFragmentManager,
            imgPath = response.imagePath,
            imgName = response.imageName,
            titleTxt = response.title,
            subTitleTxt = response.description,
            btnFirstFunction = {},
            isClickableOutside = false,
            firstBtnTxt = getString(R.string.ok)
        )
    }

    override fun onSuccessGetDetailVDC(response: DetailVDCResponse) {
        val responseJson = gson.toJson(response)
        val isEnableChangePin = response.settingVirtualCardData.find {
            it.type == CHANGE_PIN_KEY }?.status ?: false

        val intent = Intent(this, DetailVDCActivity::class.java)
        intent.putExtra(Constant.TAG_TYPE, "fromList")
        intent.putExtra(Constant.TAG_CONTENT, responseJson)
        intent.putExtra(DetailVDCActivity.EXTRA_ENABLE_CHANGE_PIN, isEnableChangePin)
        startActivityIntent.launch(intent)
    }

    override fun showSafetyMode(response: GeneralResponse) {
        val dialog = DialogInformation(
            this, response.imageName,
            response.title,
            response.description, "Oke", this, true, false
        )
        val ft: FragmentTransaction = supportFragmentManager.beginTransaction()
        ft.add(dialog, null)
        ft.commitAllowingStateLoss()
    }

    private fun getListProductVDC(account: String) {
        presenter.setUrlGetListProductVDC(getString(R.string.url_v1_get_list_product_vdc))
        presenter.getListProductVDC(account)
    }

    override fun onClickCreateAccount() {
        launchIntent(this)
    }

    override fun onClickUpdateAccount() {
        ListRekeningCategoryActivity.launchIntent(this)
    }

    fun enableButton() {
        binding.btnSubmit.apply {
            isEnabled = true
            alpha = 1f
        }
    }

    fun disableButton() {
        binding.btnSubmit.apply {
            isEnabled = false
            alpha = 0.3f
        }
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // There are no request codes
            // val data: Intent? = result.data
        } else {
            if (result.data != null) {
                setResult(RESULT_CANCELED, result.data)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (resultCode != RESULT_OK) {
            if (data != null) {
                val value = data.getStringExtra(Constant.TAG_TYPE)
                if (value == "fromSuccess") {
                    isRefresh = true
                    getListVirtualDebitCard()
                }
            }
        }
    }

    override fun onClickAction() {}
}