package id.co.bri.brimo.ui.fragments.bottomsheet

import android.content.Context
import android.view.LayoutInflater
import androidx.fragment.app.FragmentManager
import androidx.recyclerview.widget.DividerItemDecoration
import androidx.recyclerview.widget.LinearLayoutManager
import com.google.android.material.divider.MaterialDividerItemDecoration
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.onboardingnewskin.OTPChannelListAdapters
import id.co.bri.brimo.databinding.FragmentNewSkinOtpChannelBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.models.optionmodel.languages.OptionSelectLanguages
import java.util.Objects

class NewSkinOTPChannel(
    val context: Context,
    private val mFragementManager: FragmentManager,
    private val selectChannel: String,
    private val onSaveChangeLanguage: (String) -> Unit
) {
    private lateinit var bottomSheetBinding: FragmentNewSkinOtpChannelBinding
    private var bottomSheetSelectChannel: BottomSheetCustomViewGeneralFragment? = null
    private lateinit var otpChannelListAdapters: OTPChannelListAdapters
    private var selectedChannelInternal: String = selectChannel

    init {
        showBottomSheetOTP()
    }

    private fun showBottomSheetOTP() {
        bottomSheetBinding = FragmentNewSkinOtpChannelBinding.inflate(LayoutInflater.from(context))
        val bottomSheetSelectChannel = BottomSheetCustomViewGeneralFragment(
            bottomSheetBinding.getRoot(),
            true,
            true
        ) { }
        this.bottomSheetSelectChannel = bottomSheetSelectChannel

        val otpChannelListAdapters: OTPChannelListAdapters = getOTPChannelListAdapters()
        bottomSheetBinding.rvLanguagesList.setLayoutManager(LinearLayoutManager(context))
        bottomSheetBinding.rvLanguagesList.setHasFixedSize(true)
        bottomSheetBinding.rvLanguagesList.addItemDecoration(
            MaterialDividerItemDecoration(context, DividerItemDecoration.VERTICAL).apply {
                dividerColor = context.getColor(R.color.black_ns_200)
                dividerThickness = context.resources.getDimensionPixelSize(R.dimen.size_1dp)
                dividerInsetStart = context.resources.getDimensionPixelSize(R.dimen.size_16dp)
                dividerInsetEnd = context.resources.getDimensionPixelSize(R.dimen.size_16dp)
                isLastItemDecorated = false
            }
        )
        bottomSheetBinding.rvLanguagesList.setAdapter(otpChannelListAdapters)

        if (!mFragementManager.isStateSaved || Objects.requireNonNull(
                mFragementManager.findFragmentByTag("")
            )?.isAdded == true
        ) {
            bottomSheetSelectChannel.show(mFragementManager, "")
        }

        bottomSheetBinding.btnPilih.setOnClickListener {
            val selected = otpChannelListAdapters.getSelectedChannel()
            onSaveChangeLanguage.invoke(selected)
            bottomSheetSelectChannel.dismiss()
        }

        bottomSheetBinding.ivTitleRight.setOnClickListener {
            bottomSheetSelectChannel.dismiss()
        }
    }

    private fun getOTPChannelListAdapters(): OTPChannelListAdapters {
        val optionSelectLanguagesList = listOf(
            OptionSelectLanguages(
                Constant.SMS, R.drawable.ic_sms_new_skin,
                context.getString(R.string.sms)
            ),
            OptionSelectLanguages(
                Constant.WHATSAPP, R.drawable.ic_whatsapp_logo_new,
                context.getString(R.string.whatsApp)
            )
        )

        otpChannelListAdapters = OTPChannelListAdapters(
            selectedChannel = selectChannel,
            onSelectionChanged = { selectedId ->
                selectedChannelInternal = selectedId
                bottomSheetBinding.btnPilih.isEnabled = true
            }
        )
        otpChannelListAdapters.submitList(optionSelectLanguagesList)
        return otpChannelListAdapters
    }
}