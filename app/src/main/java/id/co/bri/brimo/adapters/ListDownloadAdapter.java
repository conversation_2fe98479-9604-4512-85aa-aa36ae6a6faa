package id.co.bri.brimo.adapters;

import android.app.Activity;
import android.view.LayoutInflater;
import android.view.ViewGroup;

import java.util.ArrayList;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemDownloadMutationWithActionBinding;
import id.co.bri.brimo.databinding.ItemDownloadMutationWithoutActionBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.EStatementListModel;

public class ListDownloadAdapter extends RecyclerView.Adapter {

    private final Activity activity;
    private ArrayList<EStatementListModel> eStatementListModels;
    private DownloadInterface onClickDownload;


    public ListDownloadAdapter(Activity activity, ArrayList<EStatementListModel> mEStatementListModels, DownloadInterface mOnClickDownload) {
        this.activity = activity;
        this.eStatementListModels = mEStatementListModels;
        this.onClickDownload = mOnClickDownload;
    }

    @Override
    public int getItemViewType(int position) {
        switch (eStatementListModels.get(position).getStatus()) {
            case "2":
                return 0;
            case "1":
            case "3":
            case "5":
            default:
                return 1;
        }
    }

    class LayoutOneViewHolder extends RecyclerView.ViewHolder {
        ItemDownloadMutationWithActionBinding binding;

        public LayoutOneViewHolder(@NonNull ItemDownloadMutationWithActionBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        private void setView(String mCode, String mStatus, String mFileType, String mAccountNumber, String mDate, String mAction) {
            if (mCode.equalsIgnoreCase("2")) {
                binding.tvStatus.setBackground(activity.getResources().getDrawable(R.drawable.bg_success));
            }
            binding.tvStatus.setText(mStatus);
            binding.tvFileType.setText(mFileType);
            binding.tvAccountNumber.setText(mAccountNumber);
            binding.tvDate.setText(mDate);
            binding.btnAction.setText(mAction);
        }
    }

    class LayoutTwoViewHolder extends RecyclerView.ViewHolder {
        ItemDownloadMutationWithoutActionBinding binding;

        public LayoutTwoViewHolder(@NonNull ItemDownloadMutationWithoutActionBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }

        private void setView(String mCode, String mStatus, String mFileType, String mAccountNumber, String mDate) {
            if (mCode.equalsIgnoreCase("1")) {
                binding.tvStatus.setBackground(activity.getResources().getDrawable(R.drawable.bg_processed));
            } else if (mCode.equalsIgnoreCase("5")) {
                binding.tvStatus.setBackground(activity.getResources().getDrawable(R.drawable.bg_canceled));
            } else if (mCode.equalsIgnoreCase("3")) {
                binding.tvStatus.setBackground(activity.getResources().getDrawable(R.drawable.bg_unsuccess));
            }
            binding.tvStatus.setText(mStatus);
            binding.tvFileType.setText(mFileType);
            binding.tvAccountNumber.setText(mAccountNumber);
            binding.tvDate.setText(mDate);
        }
    }

    @NonNull
    @Override
    public RecyclerView.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        switch (viewType) {
            case 0:
                return new LayoutOneViewHolder(ItemDownloadMutationWithActionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
            case 1:
                return new LayoutTwoViewHolder(ItemDownloadMutationWithoutActionBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
            default:
                return null;
        }
    }

    @Override
    public void onBindViewHolder(@NonNull RecyclerView.ViewHolder holder, int position) {
        switch (eStatementListModels.get(position).getStatus()) {
            case "2":
                String code = eStatementListModels.get(position).getStatus();
                String status = eStatementListModels.get(position).getStatusString();
                String fileType = eStatementListModels.get(position).getFileTypeString();
                String accountNumber = eStatementListModels.get(position).getAccountNumberString();
                String date = eStatementListModels.get(position).getDateRangeEStatement();
                String action = GeneralHelper.getString(R.string.download);
                ((LayoutOneViewHolder) holder).setView(code, status, fileType, accountNumber, date, action);
                ((LayoutOneViewHolder) holder).binding.btnAction.setOnClickListener(view -> onClickDownload.clickDownload(eStatementListModels.get(position)));
                break;
            case "1":
            case "3":
            case "5":
                String mCode = eStatementListModels.get(position).getStatus();
                String mStatus = eStatementListModels.get(position).getStatusString();
                String mFileType = eStatementListModels.get(position).getFileTypeString();
                String mAccountNumber = eStatementListModels.get(position).getAccountNumberString();
                String mDate = eStatementListModels.get(position).getDateRangeEStatement();
                ((LayoutTwoViewHolder) holder).setView(mCode, mStatus, mFileType, mAccountNumber, mDate);
                break;
            default:
                return;
        }
    }

    @Override
    public int getItemCount() {
        return eStatementListModels.size();
    }

    public interface DownloadInterface {
        void clickDownload(EStatementListModel eStatementListModel);
    }
}