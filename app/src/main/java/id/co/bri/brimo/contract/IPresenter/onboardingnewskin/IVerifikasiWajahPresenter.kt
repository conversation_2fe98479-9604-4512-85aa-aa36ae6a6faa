package id.co.bri.brimo.contract.IPresenter.onboardingnewskin

import android.content.Context
import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IVerifikasiWajahPresenter<V : IMvpView> : IMvpPresenter<V> {
    fun setUrlPreSigned(url: String)

    fun setUrlInitializeZoloz(url: String)

    fun setUrlSendKyc(url: String)

    fun getPreSigned()

    fun putMinioData(
        transactionId: String,
        image1Url: String,
        image2Url: String,
        videoUrl: String,
        image1: String,
        image2: String,
        video: String,
        context: Context
    )

    /*fun checkAdditionalImages(
        context: Context,
        image1Url: String,
        image2Url: String,
        videoUrl: String,
        vidaResponse: VidaLivenessResponse
    )*/

    fun getInitializeZoloz(metaInfo: String)

    fun sendKyc(partnerId: String, result: String)
}