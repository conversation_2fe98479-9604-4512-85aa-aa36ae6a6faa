package id.co.bri.brimo.adapters.virtualdebitcard

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.core.content.ContextCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.ItemSettingActionVdcBinding
import id.co.bri.brimo.databinding.ItemSettingSwitchVdcBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.enums.CardSettingsVDCEnum
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.models.apimodel.response.pengelolaankartu.DetailKelolaKartuRes
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.SettingVirtualCardData
import id.co.bri.brimo.util.extension.setColor

class CardSettingVDCAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_SWITCH = 0
        private const val VIEW_TYPE_ACTION = 1
        private const val VIEW_TYPE_ACTION_BLOCK_CARD = 2
        const val CHANGE_PIN_ACTION_TYPE = "CHANGE_PIN"
        const val CHANGEPIN_ACTION_TYPE = "CHANGEPIN"
    }

    var settingVirtualCardList = mutableListOf<SettingVirtualCardData>()
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    var managementCardDetail: DetailKelolaKartuRes? = null
        set(value) {
            field = value
            notifyDataSetChanged()
        }

    private var clickListener: ((ClickAction) -> Unit)? = null
    private var isCardStatusTrue: Boolean = false

    fun setOnClickListener(listener: (ClickAction) -> Unit) {
        clickListener = listener
    }

    override fun getItemViewType(position: Int): Int {
        val settingVDC = settingVirtualCardList[position]
        return when (settingVDC.style) {
            CardSettingsVDCEnum.VIEW_TYPE_SWITCH.value -> VIEW_TYPE_SWITCH
            CardSettingsVDCEnum.VIEW_TYPE_ACTION.value -> VIEW_TYPE_ACTION
            CardSettingsVDCEnum.VIEW_TYPE_ACTION_BLOCK.value -> VIEW_TYPE_ACTION_BLOCK_CARD
            else -> VIEW_TYPE_ACTION
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_SWITCH -> {
                val binding = ItemSettingSwitchVdcBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SettingCardSwitchHolder(binding)
            }

            VIEW_TYPE_ACTION -> {
                val binding = ItemSettingActionVdcBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SettingCardActionHolder(binding)
            }

            VIEW_TYPE_ACTION_BLOCK_CARD -> SettingCardColorActionHolder(
                ItemSettingActionVdcBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            else -> {
                val binding = ItemSettingActionVdcBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SettingCardActionHolder(binding)
            }
        }
    }

    override fun getItemCount(): Int = settingVirtualCardList.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val settingVDC = settingVirtualCardList[position]
        when (holder) {
            is SettingCardSwitchHolder -> {
                holder.bindData(settingVDC)
                // If it's a Card Status, set isCardStatusTrue according to the Card status
                if (!settingVDC.cardIssue) {
                    if (settingVDC.type == "CARDSTATUS") {
                        isCardStatusTrue = settingVDC.status
                    }
                } else {
                    holder.binding.swAction
                }

                // Set whether the Online Transaction switch can be clicked
                if (settingVDC.type != "CARDSTATUS") {
                    setupOptionView(
                        settingVDC.type,
                        settingVDC.cardIssue,
                        holder.binding,
                        isCardStatusTrue
                    )
                }
            }

            is SettingCardActionHolder -> holder.bindData(settingVDC)
            is SettingCardColorActionHolder -> holder.bindData(settingVDC, managementCardDetail)
        }
    }

    private fun setupOptionView(
        vdcType: String,
        cardIssue: Boolean,
        itemView: ItemSettingSwitchVdcBinding,
        isCardStatusTrue: Boolean
    ) {
        if (!cardIssue) {
            itemView.swAction.alpha = 1F
            itemView.swAction.isEnabled = isCardStatusTrue
            if (vdcType != "ECOMM") {
                itemView.view.visibility = View.GONE
            }
        } else {
            itemView.swAction.alpha = 0.3F
        }
    }

    inner class SettingCardSwitchHolder(val binding: ItemSettingSwitchVdcBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bindData(settingVDC: SettingVirtualCardData) {
            binding.tvName.text = settingVDC.title
            val (subtitleText, subtitleColor, subtitleImage) = if (settingVDC.status) {
                Triple(settingVDC.subTitle, R.color.semanticGreen80, "tab_indicator_green")
            } else {
                Triple(settingVDC.subTitle, R.color.semanticRed80, "tab_indicator_red")
            }

            binding.tvSubTitle.apply {
                text = subtitleText
                setTextColor(ContextCompat.getColor(binding.root.context, subtitleColor))
            }

            binding.ivStatus.setImageResource(
                GeneralHelper.getImageId(
                    binding.root.context,
                    subtitleImage
                )
            )

            binding.swAction.isChecked = settingVDC.status
            // Atur apakah switch dapat diklik berdasarkan status Card
            if (!settingVDC.cardIssue) {
                binding.swAction.isEnabled = settingVDC.type == Constant.CARD_STATUS
                binding.swAction.alpha = 1F
            } else {
                binding.swAction.isEnabled = false
                binding.swAction.alpha = 0.3F
            }

            binding.swAction.setOnCheckedChangeListener { _, isChecked ->
                binding.swAction.isChecked = settingVDC.status
                if (settingVDC.type == Constant.CARD_STATUS) {
                    val cardStatusPosition =
                        settingVirtualCardList.indexOfFirst { it.type == Constant.CARD_STATUS }
                    if (cardStatusPosition != -1) {
                        val cardStatusItem = settingVirtualCardList[cardStatusPosition]
                        val updatedCardStatus = cardStatusItem.copy(status = isChecked)
                        settingVirtualCardList[cardStatusPosition] = updatedCardStatus
                        isCardStatusTrue = isChecked
                    }
                }

                clickListener?.invoke(
                    ClickAction.Transaction(settingVDC.copy(status = !isChecked))
                )
            }
        }
    }

    inner class SettingCardActionHolder(val binding: ItemSettingActionVdcBinding) :
        RecyclerView.ViewHolder(binding.root), TextArrowViewBinding {

        override val tvName: TextView get() = binding.tvName
        override val ivArrow: ImageView get() = binding.ivArrow

        fun bindData(settingVDC: SettingVirtualCardData) {
            val context = binding.root.context

            val isEnabled = if (managementCardDetail == null) settingVDC.status
            else managementCardDetail?.isEnabledChangePin ?: false

            adjustTextArrowColor(
                binding = this,
                context = context,
                condition = isEnabled,
                text = settingVDC.title,
                showIcon = true,
                textColorTrue = R.color.neutral_dark40,
                textColorFalse = R.color.color_8A8A8A,
                iconColorTrue = R.color.primaryBlue80,
                iconColorFalse = R.color.color_8A8A8A
            )

            binding.clAction.setOnClickListener {
                if (!isEnabled) return@setOnClickListener
                when (settingVDC.type) {
                    CHANGE_PIN_ACTION_TYPE, CHANGEPIN_ACTION_TYPE -> clickListener?.invoke(ClickAction.ChangePin { /* No-op */ })
                    else -> clickListener?.invoke(ClickAction.Action(settingVDC.title))
                }
            }
        }
    }

    inner class SettingCardColorActionHolder(val binding: ItemSettingActionVdcBinding) :
        RecyclerView.ViewHolder(binding.root), TextArrowViewBinding {

        override val tvName: TextView get() = binding.tvName
        override val ivArrow: ImageView get() = binding.ivArrow

        fun bindData(
            settingVDC: SettingVirtualCardData,
            managementCardDetail: DetailKelolaKartuRes?
        ) {
            binding.apply {
                val context = binding.root.context

                if (managementCardDetail?.enableBlockCard == null) {
                    view.isVisible = false
                    ivArrow.isVisible = false
                    return
                } else {
                    adjustTextArrowColor(
                        binding = this@SettingCardColorActionHolder,
                        context = context,
                        condition = managementCardDetail.enableBlockCard ?: false,
                        text = settingVDC.title,
                        showIcon = false,
                        textColorTrue = R.color.semanticRed80,
                        textColorFalse = R.color.color_8A8A8A,
                        iconColorTrue = 0,
                        iconColorFalse = 0
                    )

                    if (settingVDC.title == GeneralHelper.getString(R.string.action_block_card_permanently)) {
                        showBubbleBlockCard(itemView)
                    }

                    if (managementCardDetail.enableBlockCard) {
                        clAction.setOnClickListener { clickListener?.invoke(ClickAction.Action(settingVDC.title)) }
                    } else {
                        clAction.setOnClickListener(null)
                    }
                }
            }
        }
    }

    private fun showBubbleBlockCard(targetView: View) {
        val brimoPrefRepository = BRImoPrefRepository(targetView.context)
        if (!brimoPrefRepository.blockCardBubble) {
            try {
                BubbleShowCaseBuilder(targetView.context as Activity)
                    .title(GeneralHelper.getString(R.string.txt_block_card_feature))
                    .description(GeneralHelper.getString(R.string.txt_block_card_desc))
                    .buttonTitle(GeneralHelper.getString(R.string.mengerti))
                    .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
                    .backgroundColor(Color.WHITE)
                    .textColor(Color.BLACK)
                    .titleTextSize(20)
                    .descriptionTextSize(14)
                    .targetView(targetView)
                    .show()

                brimoPrefRepository.saveBlockCardBubble(true)
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    Log.e("BubbleShowBlockCard", "BubbleShowBlockCard: ", e)
                }
            }
        }
    }

    private fun <T : TextArrowViewBinding> adjustTextArrowColor(
        binding: T,
        context: Context,
        text: String? = null,
        showIcon: Boolean = true,
        textColorTrue: Int = 0,
        textColorFalse: Int = 0,
        iconColorTrue: Int = 0,
        iconColorFalse: Int = 0,
        condition: Boolean = false
    ) {
        binding.apply {
            text?.let { tvName.text = it }
            ivArrow.isVisible = showIcon
            tvName.setColor(context, if (condition) textColorTrue else textColorFalse)
            if (showIcon) ivArrow.setColor(context, if (condition) iconColorTrue else iconColorFalse)
            else return
        }
    }

    interface TextArrowViewBinding {
        val tvName: TextView
        val ivArrow: ImageView
    }
}

sealed class ClickAction {
    data class Transaction(val setting: SettingVirtualCardData) : ClickAction()
    data class Action(val action: String) : ClickAction()
    data class ChangePin(val onChangePin: () -> Unit = {}) : ClickAction()
}