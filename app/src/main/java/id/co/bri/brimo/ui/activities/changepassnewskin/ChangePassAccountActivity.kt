package id.co.bri.brimo.ui.activities.changepassnewskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.activity.OnBackPressedCallback
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.ubahpassword.IUbahPasswordPresenter
import id.co.bri.brimo.contract.IView.ubahpassword.IUbahPasswordView
import id.co.bri.brimo.databinding.ActivityChangePassNewskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.ui.activities.LoginActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation
import id.co.bri.brimo.util.extension.view.addMinLengthCharValidation
import id.co.bri.brimo.util.extension.view.disableCopyPaste
import id.co.bri.brimo.util.extension.view.onTextChanged
import id.co.bri.brimo.util.extension.view.preventSpaceInput
import id.co.bri.brimo.util.extension.view.togglePasswordVisibility
import javax.inject.Inject

class ChangePassAccountActivity : NewSkinBaseActivity(), IUbahPasswordView {

    private lateinit var binding: ActivityChangePassNewskinBinding
    private var isPasswordVisible = false

    @Inject
    lateinit var ubahPasswordPresenter: IUbahPasswordPresenter<IUbahPasswordView>

    companion object {
        var mDescError: String? = null
        val TAG: String = "UbahPasswordActivity"

        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, ChangePassAccountActivity::class.java)
            caller.startActivityForResult(intent, Constant.REQ_UBAH_KATA_KUNCI)
        }

        fun launchIntentError(caller: Activity, desc: String?) {
            val intent = Intent(caller, ChangePassAccountActivity::class.java)
            caller.startActivity(intent)
            mDescError = desc
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityChangePassNewskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        ubahPasswordPresenter.setView(this)
        ubahPasswordPresenter.setUrlValidatePassKunci(GeneralHelper.getString(R.string.v5_check_password))
        ubahPasswordPresenter.start()
    }

    private fun setupView() = with(binding) {
        inputPassword.preventSpaceInput()
        inputPassword.disableCopyPaste()
        setupTogglePasswordVisibility()
        observePassword()
        GeneralHelperNewSkin.setToolbar(
            this@ChangePassAccountActivity,
            toolbar.toolbar,
            GeneralHelper.getString(R.string.ubah_password)
        )
        tvLupaPass.setOnClickListener {
            OnboardingInputNumberForgetPassActivity.launchIntent(this@ChangePassAccountActivity, Constant.REQ_UBAH_KATA_KUNCI)
        }
    }

    private fun observePassword() = with(binding) {
        inputPassword.onTextChanged { s ->
            val input = s?.toString() ?: ""
            val isValidChar = input.length >= 8
            val noSpace = input.isNotEmpty() && !input.contains(" ")

            val allValid = isValidChar && noSpace
            btnNext.isEnabled = allValid
            if (allValid) {
                btnNext.setOnClickListener {
                    ChangePassActivity.launchIntent(this@ChangePassAccountActivity, "")
//                    ubahPasswordPresenter.onUbahPasswordSubmit()
                }
            }
        }

        inputPassword.addMinLengthCharValidation(
            minLength = 8,
            errorText = "Minimal harus 8 digit angka",
            debounceDelayOnError = 3000L
        )
    }

    private fun setupTogglePasswordVisibility() = with(binding) {
        inputPassword.inputLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            inputPassword.togglePasswordVisibility(isPasswordVisible)
        }
    }

//    override fun isScreenshotDisabled() = true

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_UBAH_KATA_KUNCI && resultCode == Activity.RESULT_OK) {
            setResult(Activity.RESULT_OK, data)
            finish()
        }
    }


    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onDestroy() {
        ubahPasswordPresenter.stop()
        super.onDestroy()
    }

    override fun getPasswordLama(): String {
        return binding.inputPassword.getText()
    }

    override fun getPasswordBaru(): String {
        return ""
    }

    override fun onSubmitSuccess(data: String?) {
        if (data != null) {
//            ChangePassActivity.launchIntent(this, data)
        }
    }

    override fun onException50(msg: String?) {
        //do nothing
    }

    override fun onException12() {
//        binding.tvError.visibility = View.VISIBLE
//        binding.rlNewPassword.background = resources.getDrawable(R.drawable.rounded_dialog_grey_newskin_off)
    }

    override fun onErrorA1(msg: String?) {
        binding.inputPassword.showError(msg ?: "")
    }

    override fun onMaxPass(msg: String?) {
        var firstBtnFunction: Runnable = Runnable {
            val intent = Intent(
                this@ChangePassAccountActivity,
                OnboardingInputNumberForgetPassActivity::class.java
            )
            startActivity(intent)        }
        val secBtnFunction = Runnable {
            LoginActivity.launchIntentWithDialogNewSkin(this, "", true)
        }
        showDialogConfirmation(
            supportFragmentManager,
            R.drawable.ic_fail_newskin,
            "ic_account_saved",
            "Percobaan Login Kamu Sudah Habis",
            "Akun kamu dibatasi karena kesalahan login. Atur ulang password kamu terlebih dahulu.",
            createKotlinFunction0(firstBtnFunction),
            createKotlinFunction0(secBtnFunction),
            false,
            "Lupa Password",
            "Kembali ke Login",
            false
        )
    }
}