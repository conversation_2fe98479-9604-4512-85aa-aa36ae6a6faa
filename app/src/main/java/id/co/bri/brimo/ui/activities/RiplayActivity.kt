package id.co.bri.brimo.ui.activities

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.view.View
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityRiplayBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.PdfWebViewClient
import id.co.bri.brimo.ui.activities.base.BaseActivity


class RiplayActivity : BaseActivity(), View.OnClickListener,
    PdfWebViewClient.onReceivedFeedback {
    private lateinit var binding: ActivityRiplayBinding
    private var riplayUrl: String = ""

    companion object {
        @JvmStatic
        fun launchIntent(caller: Activity, mUrl: String) {
            val intent = Intent(caller, RiplayActivity::class.java).apply {
                putExtra(Constant.TAG_PRODUCT_URL, mUrl)
            }
            caller.startActivityForResult(intent, Constant.REQ_RIPLAY)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityRiplayBinding.inflate(layoutInflater)
        setContentView(binding.root)

        handleIntent()
    }

    private fun handleIntent() {
        riplayUrl = intent.getStringExtra(Constant.TAG_PRODUCT_URL).toString()
        initView()
    }

    @SuppressLint("SetJavaScriptEnabled")
    private fun initView() {
        GeneralHelper.setToolbar(
            this,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.pedoman_riplay)
        )
        binding.wvContent.settings.apply {
            javaScriptEnabled = true
            loadWithOverviewMode = true
            useWideViewPort = true
            builtInZoomControls = true
            displayZoomControls = false
        }
        if (riplayUrl.isNotEmpty()) {
            showProgress()
            val pdfWebViewClient = PdfWebViewClient(this, binding.wvContent, this, 60)
            pdfWebViewClient.loadPdfUrl(riplayUrl)
        }

        binding.imgSkip.setOnClickListener(this)
        binding.btnSetuju.setOnClickListener(this)
        binding.tvBatal.setOnClickListener(this)
    }

    override fun onClick(view: View?) {
        when (view?.id) {
            binding.imgSkip.id -> {
                
            }

            binding.btnSetuju.id -> {
                val resultIntent = Intent()
                resultIntent.putExtra("checkbox_riplay", true.toString())
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }

            binding.tvBatal.id -> {
                val resultIntent = Intent()
                resultIntent.putExtra("checkbox_riplay", false.toString())
                setResult(Activity.RESULT_OK, resultIntent)
                finish()
            }
        }
    }

    override fun onFinished() {
        binding.wvContent.visibility = View.VISIBLE
        hideProgress()
    }

    override fun onStarted() {
        binding.wvContent.visibility = View.INVISIBLE
    }

    override fun onError() {
        failedLoadPdf()
    }

    private fun failedLoadPdf() {
        val resultIntentOk = Intent()
        setResult(Constant.REQ_PETTUNJUK2, resultIntentOk)
        finish()
    }
}