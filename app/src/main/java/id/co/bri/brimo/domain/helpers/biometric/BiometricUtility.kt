@file:Suppress("DEPRECATION")

package id.co.bri.brimo.domain.helpers.biometric

import android.annotation.SuppressLint
import android.content.Context
import android.content.pm.PackageManager
import android.os.AsyncTask
import android.os.Build
import android.security.keystore.KeyGenParameterSpec
import android.security.keystore.KeyProperties.*
import android.util.Base64
import android.util.Log
import androidx.annotation.RequiresApi
import androidx.biometric.BiometricManager
import androidx.biometric.BiometricPrompt
import androidx.core.content.ContextCompat
import androidx.fragment.app.FragmentActivity
import id.co.bri.brimo.R
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import java.security.InvalidKeyException
import java.security.KeyStore
import java.security.UnrecoverableKeyException
import javax.crypto.Cipher
import javax.crypto.Cipher.DECRYPT_MODE
import javax.crypto.KeyGenerator
import javax.crypto.SecretKey
import javax.crypto.spec.IvParameterSpec

object BiometricUtility {
    private lateinit var mBiometricPrompt: BiometricPrompt
    private lateinit var mBiometricPromptInfo: BiometricPrompt.PromptInfo
    private lateinit var keyStore: KeyStore
    private lateinit var biometricCallback: BiometricCallback

    fun setBiometricType(context: Context) {
        val brImoPrefRepository = BRImoPrefRepository(context)
        if (context.packageManager.hasSystemFeature(PackageManager.FEATURE_FACE)) {
            brImoPrefRepository.saveFaceAvailable(true)
            brImoPrefRepository.saveBiometricType(Constant.FACE_ID)
        }

        if (context.packageManager.hasSystemFeature(PackageManager.FEATURE_FINGERPRINT)) {
            brImoPrefRepository.saveFingerAvailable(true)
            brImoPrefRepository.saveBiometricType(Constant.FINGERPRINT_FACE_ID)
        }
    }

    fun displayPromptForEnroll(activity: FragmentActivity, enrollBio: Boolean) {
        invokeAsyncTask(activity, enrollBio)
    }

    fun displayPromptForLogin(activity: FragmentActivity) {

        val mExecutor = ContextCompat.getMainExecutor(activity)
        if (activity is BiometricCallback) biometricCallback = activity

        mBiometricPrompt =
            BiometricPrompt(activity, mExecutor, object : BiometricPrompt.AuthenticationCallback() {
                override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
                    if (!GeneralHelper.isProd()) {
                        Log.d("testdebug222", "BioFailed :$errString")
                        Log.d("testdebug8363", "onAuthenticationError")
                    }
                    biometricCallback.onAuthenticationError(errorCode, errString)
                }

                override fun onAuthenticationSucceeded(result: BiometricPrompt.AuthenticationResult) {
                    super.onAuthenticationSucceeded(result)
                    if (!GeneralHelper.isProd()) {
                        Log.d("testdebug735", "onAuthenticationSucceeded")
                    }
                    biometricCallback.onAuthenticationSuccess()
                }

                override fun onAuthenticationFailed() {
                    super.onAuthenticationFailed()
                    if (!GeneralHelper.isProd()) {
                        Log.d("testdebug9363", "onAuthenticationFailed ")
                    }
                    biometricCallback.onAuthenticationFailed()
                }

            })
        generateSecretKey()
        val secretKey = getSecretKey()
        if (!GeneralHelper.isProd()) {
            Log.d("testdebug3635", "keyyyyyy: $secretKey")
        }
        if (retrieveSecretKey(activity, secretKey)) {
            val brImoPrefRepository = BRImoPrefRepository(activity)
            val biometricType = brImoPrefRepository.biometricType.toString()
            if (!GeneralHelper.isProd()) {
                Log.d("testdebug335", "sddsfsd: $biometricType")
            }
            val titleBio = String.format(GeneralHelper.getString(R.string.title_prompt_login), biometricType)
            mBiometricPromptInfo =
                BiometricPrompt.PromptInfo.Builder()
                    .setTitle(titleBio)
                    .setSubtitle(GeneralHelper.getString(R.string.subtitle_prompt_login))
                    .setNegativeButtonText(GeneralHelper.getString(R.string.batal))
                    .setConfirmationRequired(true)
                    .build()
            mBiometricPrompt.authenticate(mBiometricPromptInfo)

        }
    }

    private fun retrieveSecretKey(activity: FragmentActivity, secretKey: SecretKey?): Boolean {
        val brImoPrefRepository = BRImoPrefRepository(activity)
        val iv = Base64.decode(brImoPrefRepository.valueKeyBiometric, Base64.NO_WRAP)
        val ivParameterSpec = IvParameterSpec(iv)
        try {
            getCipher()?.init(DECRYPT_MODE, secretKey, ivParameterSpec)

        } catch (e: InvalidKeyException) {
            if (!GeneralHelper.isProd()) {
                Log.d("testdebug0383", "biometric changed")
            }
            biometricCallback.onBiometricChanged()
            brImoPrefRepository.saveStatusBioChange(true)
            brImoPrefRepository.saveStatusAktivasi(false)

            return false
        }
        if (!GeneralHelper.isProd()) {
            Log.d("testdebug03737", "biometric not changed")
        }

        return true
    }

    @RequiresApi(Build.VERSION_CODES.N)
    private fun generateSecretKey() {
        keyStore = KeyStore.getInstance("AndroidKeyStore")
        keyStore.load(null)
        if (!keyStore.containsAlias(Constant.KEY_BIOMERIC)) {
            createKey()
        }

    }

    private fun getCipher(): Cipher? {

        return Cipher.getInstance(
            KEY_ALGORITHM_AES +
                    "/" + BLOCK_MODE_CBC +
                    "/" + ENCRYPTION_PADDING_PKCS7
        )
    }

    @RequiresApi(Build.VERSION_CODES.N)
    private fun createKey() {
        val keyGenParameter = getKeyGenParameter()
        val keyGenerator = KeyGenerator.getInstance(KEY_ALGORITHM_AES, "AndroidKeyStore")
        keyGenerator.init(keyGenParameter)
        keyGenerator.generateKey()
    }

    private fun getSecretKey(): SecretKey? {
        return try {
            keyStore = KeyStore.getInstance("AndroidKeyStore")
            keyStore.load(null)
            keyStore.getKey(Constant.KEY_BIOMERIC, null) as SecretKey?
        } catch (e: UnrecoverableKeyException) {
            if (!GeneralHelper.isProd()) {
                Log.d("BiometricUtility", "User changed or deleted their auth credentials")
            }
             null
        }
    }

    @RequiresApi(Build.VERSION_CODES.N)
    private fun getKeyGenParameter(): KeyGenParameterSpec {
        return KeyGenParameterSpec.Builder(
            Constant.KEY_BIOMERIC,
            PURPOSE_ENCRYPT or PURPOSE_DECRYPT)
            .setBlockModes(BLOCK_MODE_CBC)
            .setEncryptionPaddings(ENCRYPTION_PADDING_PKCS7)
            .setUserAuthenticationRequired(true)
            .setInvalidatedByBiometricEnrollment(true)
            .build()
    }

    private fun invokeAsyncTask(activity: FragmentActivity, enrollBio: Boolean) {
        val async = BiometricRegistrationAsync(activity, enrollBio)
        async.run {
            execute(null, null, null)
        }
    }

    class BiometricRegistrationAsync(
        @field:SuppressLint("StaticFieldLeak") private val activity: FragmentActivity, private val enrollBio: Boolean) :
        AsyncTask<Void, Void, Void>() {

        private val getBioPrompt = CreateAndGenerateKey()

        @Deprecated("Deprecated in Java")
        @RequiresApi(Build.VERSION_CODES.N)
        override fun doInBackground(vararg params: Void?): Void? {
            getBioPrompt.createKeyAndEncryptPinForBiometric(activity)
            return null
        }


        @Deprecated("Deprecated in Java")
        override fun onPostExecute(result: Void?) {
            getBioPrompt.generateBioPrompt(activity, enrollBio)
        }
    }

}