package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class PulsaList {

    @SerializedName("amount_string")
    @Expose
    private String text;
    @SerializedName("amount")
    @Expose
    private Integer value;
    @SerializedName("admin_fee_string")
    @Expose
    private String adminFeeString;
    @SerializedName("admin_fee")
    @Expose
    private Integer adminFee;
    @SerializedName("total")
    @Expose
    private Integer total;
    @SerializedName("total_string")
    @Expose
    private String totalString;

    private boolean isSelected;

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public String getAdminFeeString() {
        return adminFeeString;
    }

    public void setAdminFeeString(String adminFeeString) {
        this.adminFeeString = adminFeeString;
    }

    public Integer getAdminFee() {
        return adminFee;
    }

    public void setAdminFee(Integer adminFee) {
        this.adminFee = adminFee;
    }

    public Integer getTotal() {
        return total;
    }

    public void setTotal(Integer total) {
        this.total = total;
    }

    public String getTotalString() {
        return totalString;
    }

    public void setTotalString(String totalString) {
        this.totalString = totalString;
    }
}