package id.co.bri.brimo.ui.fragments.bottomsheet

import android.app.Dialog
import android.content.DialogInterface
import android.graphics.Color
import android.graphics.drawable.ColorDrawable
import android.graphics.drawable.Drawable
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.FrameLayout
import androidx.core.content.ContextCompat
import androidx.core.content.res.ResourcesCompat
import androidx.core.view.isVisible
import com.bumptech.glide.Glide
import com.bumptech.glide.request.target.Target
import com.google.android.material.bottomsheet.BottomSheetBehavior
import com.google.android.material.bottomsheet.BottomSheetDialog
import com.google.android.material.bottomsheet.BottomSheetDialogFragment
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.FragmentBottomSheetGeneralNewSkinBinding
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.helpers.GeneralHelper
import androidx.core.graphics.drawable.toDrawable

class BottomSheetGeneralNewSkinFragment : BottomSheetDialogFragment() {

    enum class DialogType {
        INFORMATION,
        MESSAGE,
        CONFIRMATION,
        ACTIVEINACTIVENFC,
        INFORMATIONWITHOUTIMAGE,
        MESSAGEWITHOUTIMAGE,
        INFORMATIONNOOUTLINEBTN,
        INFORMATIONHTML,
        INFORMATIONWITHTOPTEXT
    }

    private lateinit var binding: FragmentBottomSheetGeneralNewSkinBinding

    private var dismissListener: () -> Unit = {}
    private var btnFirstListener: () -> Unit = {}
    private var btnSecondListener: () -> Unit = {}
    private var btnThirdListener: () -> Unit = {}
    private var dialogType: DialogType = DialogType.INFORMATION
    private var isSecondBtnBordered: Boolean = false
    var imagePath: String = ""
    var imageName: String = ""
    var titleTopText: String = ""
    var titleTopDesc: String = ""
    var titleText: String = ""
    var subtitleText: String = ""
    var firstBtnText: String = ""
    var secondBtnText: String = ""
    var thirdBtnText: String = ""
    var isClickable: Boolean = true
    var withBackgroundSecondBtn: Boolean = true
    var imageDrawable: Int? = null

    var closeButtonVisible: Boolean = false
        set(value) {
            field = value
            if (::binding.isInitialized) {
                binding.ivClose.visibility = if (value) View.VISIBLE else View.GONE
            }
        }

    fun setOnDismiss(listener: () -> Unit) {
        dismissListener = listener
    }

    fun setOnBtnFirst(listener: () -> Unit) {
        btnFirstListener = listener
    }

    fun setOnBtnSecond(listener: () -> Unit) {
        btnSecondListener = listener
    }

    fun setBtnSecondBorder(isShow: Boolean) {
        isSecondBtnBordered = isShow
    }

    fun setOnBtnThird(listener: () -> Unit) {
        btnThirdListener = listener
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?, savedInstanceState: Bundle?
    ): View {
        binding = FragmentBottomSheetGeneralNewSkinBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onStart() {
        super.onStart()
        dialog?.window?.apply {
            clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            statusBarColor = Color.TRANSPARENT
        }
    }


    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        val dialog = super.onCreateDialog(savedInstanceState) as BottomSheetDialog
        dialog.window?.setBackgroundDrawable(Color.TRANSPARENT.toDrawable())
        return dialog
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        dialog?.setOnShowListener {
            val bottomSheet = dialog?.findViewById(R.id.design_bottom_sheet) as FrameLayout
            bottomSheet.background = ContextCompat.getDrawable(requireContext(), R.drawable.rounded_dialog_newskin)
            val behavior = BottomSheetBehavior.from(bottomSheet)
            behavior.state = BottomSheetBehavior.STATE_EXPANDED
        }

        dialog?.setCancelable(isClickable)
        dialog?.setCanceledOnTouchOutside(isClickable)

        binding.tvTitle.text = titleText
        binding.tvDesc.text = subtitleText
        binding.firstBtn.text = firstBtnText
        binding.secondBtn.text = secondBtnText
        binding.thirdBtn.text = thirdBtnText

        showSecondButtonBorder()

        binding.firstBtn.setOnClickListener {
            btnFirstListener()
            dismissListener()
        }

        binding.secondBtn.setOnClickListener {
            btnSecondListener()
            dismissListener()
        }

        binding.thirdBtn.setOnClickListener {
            btnThirdListener()
            dismissListener()
        }

        binding.ivClose.visibility = if (closeButtonVisible) View.VISIBLE else View.GONE
        if (closeButtonVisible) {
            binding.ivClose.setOnClickListener {
                binding.ivClose.isEnabled = false
                dismissListener()
            }

        }

        setDialogType(dialogType)
    }

    private fun setDialogType(dialogType: DialogType) {
        when (dialogType) {
            DialogType.INFORMATION -> {
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.setImageResource(
                            GeneralHelper.getImageId(
                                    binding.root.context, imageName
                            )
                    )
                } else {
                    GeneralHelper.loadImageUrl(
                            context, imagePath, binding.ivCenter, R.drawable.img_sorry_with_card, 0
                    )
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
                if (thirdBtnText.isEmpty()) {
                    binding.thirdBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
            }

            DialogType.INFORMATIONWITHOUTIMAGE -> {
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.gone()
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.gone()
                    binding.firstBtn.text = firstBtnText
                }
                if (thirdBtnText.isEmpty()) {
                    binding.thirdBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }

                if (thirdBtnText.isNullOrEmpty()){
                    binding.thirdBtn.gone()
                }

            }

            DialogType.MESSAGE -> {
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.setImageResource(
                        GeneralHelper.getImageId(
                            binding.root.context, imageName
                        )
                    )
                } else {
                    GeneralHelper.loadImageUrl(
                        context, imagePath, binding.ivCenter, R.drawable.img_sorry_with_card, 0
                    )
                }
                if (secondBtnText.isNotEmpty()) {
                    binding.secondBtn.visibility = View.VISIBLE
                    binding.secondBtn.text = secondBtnText
                }
                if (firstBtnText.isNotEmpty()) {
                    binding.firstBtn.visibility = View.VISIBLE
                    binding.firstBtn.text = firstBtnText
                }
                if (thirdBtnText.isEmpty()) {
                    binding.thirdBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
            }

            DialogType.INFORMATIONNOOUTLINEBTN -> {
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.setImageResource(
                        GeneralHelper.getImageId(
                            binding.root.context,
                            imageName
                        )
                    )
                } else if (imagePath.isNotEmpty()) {
                    GeneralHelper.loadImageUrl(
                        context,
                        imagePath,
                        binding.ivCenter,
                        R.drawable.img_sorry_with_card,
                        0
                    )
                } else {
                    binding.ivCenter.gone()
                }
                if (imagePath.isNullOrEmpty()){
                    binding.ivCenter.isVisible = false
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
                if (thirdBtnText.isEmpty()) {
                    binding.thirdBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
            }

            DialogType.CONFIRMATION -> {
                activity?.let {
                    Glide.with(it).load(imageDrawable)
                        .into(binding.ivCenter)
                }
                if (firstBtnText.isNotEmpty()) {
                    binding.firstBtn.visibility = View.VISIBLE
                    binding.firstBtn.text = firstBtnText
                }
                if (secondBtnText.isNotEmpty()) {
                    binding.secondBtn.visibility = View.VISIBLE
                    binding.secondBtn.text = secondBtnText
                } else {
                    binding.secondBtn.visibility = View.GONE
                }

                if (withBackgroundSecondBtn) {
                    binding.secondBtn.setTextAppearance(R.style.ButtonPrimary_Outline)
                } else {
                    binding.secondBtn.setTextAppearance(R.style.ButtonPrimaryRevamp_OutlineTransparent)
                }
            }

            DialogType.MESSAGEWITHOUTIMAGE -> {
                binding.ivCenter.visibility = View.GONE
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
            }

            DialogType.INFORMATIONHTML -> {
                binding.tvDesc.text = Html.fromHtml(subtitleText).trim()
                if (imageName.isNotEmpty()) {
                    binding.ivCenter.setImageResource(
                        GeneralHelper.getImageId(
                            binding.root.context,
                            imageName
                        )
                    )
                } else if (imagePath.isNotEmpty()) {
                    GeneralHelper.loadImageUrl(
                        context,
                        imagePath,
                        binding.ivCenter,
                        R.drawable.img_sorry_with_card,
                        0
                    )
                } else {
                    binding.ivCenter.gone()
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.gone()
                    binding.firstBtn.text = firstBtnText
                }
                if (thirdBtnText.isEmpty()) {
                    binding.thirdBtn.gone()
                    binding.firstBtn.text = firstBtnText
                }
            }

            DialogType.ACTIVEINACTIVENFC -> {
                if (titleText.isNullOrEmpty()) {
                    binding.tvTitle.gone()
                }
                if (subtitleText.isNullOrEmpty()) {
                    binding.tvDesc.gone()
                }
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.setImageResource(
                            GeneralHelper.getImageId(
                                    binding.root.context, imageName
                            )
                    )
                } else {
                    GeneralHelper.loadImageUrl(
                            context, imagePath, binding.ivCenter, R.drawable.img_sorry_with_card, 0
                    )
                }

                if (firstBtnText.isEmpty()) {
                    binding.firstBtn.gone()
                    binding.secondBtn.text = secondBtnText
                }

                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.gone()
                    binding.firstBtn.text = firstBtnText
                }
                binding.thirdBtn.gone()

            }

            DialogType.INFORMATIONWITHTOPTEXT -> {
                if (imagePath.isNullOrEmpty()) {
                    binding.ivCenter.setImageResource(
                        GeneralHelper.getImageId(
                            binding.root.context, imageName
                        )
                    )
                } else {
                    GeneralHelper.loadImageUrl(
                        context, imagePath, binding.ivCenter, R.drawable.img_sorry_with_card, 0
                    )
                }
                if (secondBtnText.isEmpty()) {
                    binding.secondBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
                if (thirdBtnText.isEmpty()) {
                    binding.thirdBtn.visibility = View.GONE
                    binding.firstBtn.text = firstBtnText
                }
            }
        }
    }

    private fun showSecondButtonBorder() {
        if (isSecondBtnBordered) {
            context?.resources?.let { res ->
                binding.secondBtn.background = ResourcesCompat.getDrawable(
                    res,
                    R.drawable.button_primary_outline_bg,
                    null
                )
            }

        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        if (isClickable) {
            dismissListener()
        }
    }

    fun setFieldType(type: DialogType) {
        dialogType = type
    }

    override fun onCancel(dialog: DialogInterface) {
        super.onCancel(dialog)
        if (isClickable) {
            dialog.dismiss()
        }
    }
}