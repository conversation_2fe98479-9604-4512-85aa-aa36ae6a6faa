package id.co.bri.brimo.presenters.britamarencanarevamp

import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IKonfrimasiTabunganRevPresenter
import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IKonfirmasiTabunganRevView
import id.co.bri.brimo.data.api.ApiSource
import id.co.bri.brimo.data.api.observer.ApiObserverKonfirmasi
import id.co.bri.brimo.data.preference.BRImoPrefSource
import id.co.bri.brimo.data.repository.anggaran.AnggaranPfmSource
import id.co.bri.brimo.data.repository.category.CategoryPfmSource
import id.co.bri.brimo.data.repository.transaksi.TransaksiPfmSource
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.rx.SchedulerProvider
import id.co.bri.brimo.models.apimodel.request.PaymentRevampRequest
import id.co.bri.brimo.models.apimodel.request.emas.PaymentOnboardEmasRequest
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptGagalEmasResponse
import id.co.bri.brimo.presenters.MvpPresenter
import io.reactivex.android.schedulers.AndroidSchedulers
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers

class KonfirmasiTabunganRevPresenter<V>(schedulerProvider: SchedulerProvider,
                                        compositeDisposable: CompositeDisposable,
                                        mBRImoPrefRepository: BRImoPrefSource, apiSource: ApiSource,
                                        categoryPfmSource: CategoryPfmSource,
                                        transaksiPfmSource: TransaksiPfmSource,
                                        anggaranPfmSource: AnggaranPfmSource) : MvpPresenter<V>(
    schedulerProvider, compositeDisposable, mBRImoPrefRepository, apiSource, transaksiPfmSource
),
        IKonfrimasiTabunganRevPresenter<V> where V : IMvpView, V : IKonfirmasiTabunganRevView {

    lateinit var mUrlPayment : String
    private var brivaResponse = ReceiptRevampResponse()
    private var responseEmas= ReceiptRevampResponse()
    private var responseEmasFailed= ReceiptGagalEmasResponse()
    private val TAG = "KonfirmasiTabunganRevPresenter"

    lateinit var paymentRequest: Any
    public var isGeneral = false

    override fun getDataPayment(
            pin: String?,
            note: String?,
            generalConfirmationResponse: GeneralConfirmationResponse?,
            fromFast: Boolean
    ) {
        if (!isViewAttached) {
            return
        }

        if (isViewAttached) {
            //set flag Loading
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            paymentRequest = PaymentRevampRequest(
                    generalConfirmationResponse!!.referenceNumber, pin, generalConfirmationResponse.pfmCategory.toString()
            )
            val disposable: Disposable =
                    apiSource.getData(mUrlPayment, paymentRequest, seqNum) //function(param)
                            .subscribeOn(Schedulers.io())
                            .observeOn(AndroidSchedulers.mainThread())
                            .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView().onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                        if (note.equals(GeneralHelper.getString(R.string.txt_emas))){
                                            responseEmas = response.getData(ReceiptRevampResponse::class.java)
                                            getView().onSuccessGetPaymentEmas(responseEmas)
                                        }else{
                                            brivaResponse = response.getData(
                                                ReceiptRevampResponse::class.java
                                            )

                                            getView().onSuccessGetPayment(brivaResponse)
                                        }
                                    }

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                     if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                                        getView().onException93(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true))
                                        getView().onException01(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_FL.value, ignoreCase = true))
                                        getView().onSuccessGetPaymentEmasFailed(restResponse.getData(ReceiptGagalEmasResponse::class.java))
                                    else onApiError(restResponse)
                                }
                            })
            compositeDisposable.add(disposable)
        }
    }

    override fun getDataPaymentEmas(request: PaymentOnboardEmasRequest) {
        if (isViewAttached) {
            //set flag Loading
            view.showProgress()
            val seqNum = brImoPrefRepository.seqNumber
            val disposable: Disposable =
                    apiSource.getData(mUrlPayment, request, seqNum) //function(param)
                            .subscribeOn(schedulerProvider.io())
                            .observeOn(schedulerProvider.mainThread())
                            .subscribeWith(object : ApiObserverKonfirmasi(view, seqNum) {
                                override fun onFailureHttp(errorMessage: String) {
                                    getView().onException(errorMessage)
                                }

                                override fun onApiCallSuccess(response: RestResponse) {
                                    getView().hideProgress()
                                    if (response.code.equals(RestResponse.ResponseCodeEnum.RC_SUCCESS.value, ignoreCase = true)){
                                        responseEmas = response.getData(ReceiptRevampResponse::class.java)
                                        getView().onSuccessGetPaymentEmas(responseEmas)
                                    }else if (response.code.equals(RestResponse.ResponseCodeEnum.RC_FL.value, ignoreCase = true)){
                                        responseEmasFailed = response.getData(ReceiptGagalEmasResponse::class.java)
                                        getView().onSuccessGetPaymentEmasFailed(responseEmasFailed)
                                    }

                                }

                                override fun onApiCallError(restResponse: RestResponse) {
                                    getView().hideProgress()
                                     if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_TRX_EXPIRED.value, ignoreCase = true))
                                        getView().onException93(restResponse.desc)
                                    else if (restResponse.code.equals(RestResponse.ResponseCodeEnum.RC_01.value, ignoreCase = true))
                                        getView().onException01(restResponse.desc)
                                    else onApiError(restResponse)
                                }
                            })
            compositeDisposable.add(disposable)
        }
    }

    override fun setUrlPayment(urlPayment: String) {
        this.mUrlPayment = urlPayment
    }


}