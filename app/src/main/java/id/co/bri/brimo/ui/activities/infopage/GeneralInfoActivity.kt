package id.co.bri.brimo.ui.activities.infopage

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.CountDownTimer
import androidx.activity.OnBackPressedCallback
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityGeneralInfoBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity

class GeneralInfoActivity : NewSkinBaseActivity() {
    private lateinit var binding: ActivityGeneralInfoBinding
    private val duration: Long = 3000

    companion object {
        var imagePath: String = ""
        var imageName: String = ""
        var titleText: String = ""
        var descText: String = ""
        var source: Int = 0

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            imagePath: String,
            imageName: String,
            titleText: String,
            descText: String,
            source: Int,
        ) {
            val intent = Intent(caller, GeneralInfoActivity::class.java)
            this.imagePath = imagePath
            this.imageName = imageName
            this.titleText = titleText
            this.descText = descText
            this.source = source
            caller.startActivityForResult(intent, Constant.REQ_INFO_PAGE)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityGeneralInfoBinding.inflate(layoutInflater)
        setContentView(binding.root)
        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)
        object : CountDownTimer(duration, 1000) {
            override fun onTick(millisUntilFinished: Long) {
                //do nothing
            }

            override fun onFinish() {
                onBackPressedCallback.handleOnBackPressed()
            }
        }.start()

        setupView()
    }

    private fun setupView() {
        if (imagePath.isEmpty()) {
            binding.ivIcon.setImageResource(
                GeneralHelper.getImageId(
                    binding.root.context, imageName
                )
            )
        } else {
            GeneralHelper.loadImageUrl(
                this, imagePath, binding.ivIcon, R.drawable.ic_block_ns, 0
            )
        }
        binding.tvTitle.text = titleText
        binding.tvDesc.text = descText
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.REQ_INFO_PAGE_SOURCE, source)
                setResult(Activity.RESULT_OK, intent)
                finish()
            }
        }
}