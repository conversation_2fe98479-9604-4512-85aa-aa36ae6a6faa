package id.co.bri.brimo.ui.activities.newskinonboarding.pass

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ActivityChangePassNewskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.OnboardingInputNumberForgetPassActivity
import id.co.bri.brimo.util.extension.view.addMinLengthCharValidation
import id.co.bri.brimo.util.extension.view.disableCopyPaste
import id.co.bri.brimo.util.extension.view.onTextChanged
import id.co.bri.brimo.util.extension.view.preventSpaceInput
import id.co.bri.brimo.util.extension.view.togglePasswordVisibility

class EnterPassActivity : NewSkinBaseActivity() {
    private lateinit var binding: ActivityChangePassNewskinBinding
    private var isPasswordVisible = false

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityChangePassNewskinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        injectDependency()
        setupView()
    }


    private fun injectDependency() {

    }

    private fun setupView() = with(binding) {
        inputPassword.preventSpaceInput()
        inputPassword.disableCopyPaste()
        setupTogglePasswordVisibility()
        observePassword()
        GeneralHelperNewSkin.setToolbar(
            this@EnterPassActivity,
            toolbar.toolbar,
            GeneralHelper.getString(R.string.ubah_password)
        )
        tvLupaPass.setOnClickListener {
            OnboardingInputNumberForgetPassActivity.launchIntent(this@EnterPassActivity, Constant.REQ_UBAH_KATA_KUNCI)
        }
    }

    private fun observePassword() = with(binding) {
        inputPassword.onTextChanged { s ->
            val input = s?.toString() ?: ""
            val isValidChar = input.length >= 8
            val noSpace = input.isNotEmpty() && !input.contains(" ")

            val allValid = isValidChar && noSpace
            btnNext.isEnabled = allValid
            if (allValid) {
                btnNext.setOnClickListener {
                    val intent = OnboardingInputNumberForgetPassActivity.launchIntent(
                        this@EnterPassActivity,
                        Constant.REQ_UBAH_PIN
                    )
                    launcher.launch(intent)
                }
            }
        }

        inputPassword.addMinLengthCharValidation(
            minLength = 8,
            errorText = "Minimal harus 8 digit angka",
            debounceDelayOnError = 3000L
        )
    }

    private fun setupTogglePasswordVisibility() = with(binding) {
        inputPassword.inputLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            inputPassword.togglePasswordVisibility(isPasswordVisible)
        }
    }

    companion object {
        fun launchIntent(caller: Context): Intent {
            return Intent(caller, EnterPassActivity::class.java)
        }
    }

}