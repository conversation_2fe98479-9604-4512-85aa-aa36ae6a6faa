package id.co.bri.brimo.ui.widget.custom_numpad

import android.app.Activity
import android.content.Context
import android.graphics.Color
import android.graphics.Typeface
import android.view.Gravity
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.view.animation.AccelerateInterpolator
import android.view.animation.DecelerateInterpolator
import android.widget.EditText
import android.widget.FrameLayout
import android.widget.GridLayout
import android.widget.ImageView
import android.widget.Space
import androidx.appcompat.widget.AppCompatButton
import androidx.appcompat.widget.AppCompatImageButton
import androidx.core.content.ContextCompat
import id.co.bri.brimo.R

class CustomNumpadHelper(
    private val activity: Activity,
    private val editText: EditText,
    private val inputType: NumpadType,
    private val onPinEntered: (String) -> Unit
) {
    private var keyboardView: View? = null
    private var pin: String = ""

    fun showKeyboard() {
        if (keyboardView == null) {
            keyboardView = LayoutInflater.from(activity).inflate(R.layout.layout_custom_keyboard, null)
            val params = FrameLayout.LayoutParams(
                FrameLayout.LayoutParams.MATCH_PARENT,
                FrameLayout.LayoutParams.WRAP_CONTENT,
                Gravity.BOTTOM
            )
            keyboardView!!.layoutParams = params

            val rootView = activity.findViewById<ViewGroup>(android.R.id.content)
            rootView.addView(keyboardView)

            keyboardView!!.translationY = keyboardView!!.height.toFloat()
            keyboardView!!.alpha = 0f

            setupKeyboard(keyboardView!!)
        }

        keyboardView?.apply {
            visibility = View.VISIBLE

            animate()
                ?.translationY(0f)
                ?.alpha(1f)
                ?.setDuration(250)
                ?.setInterpolator(DecelerateInterpolator())
                ?.start()
        }
    }

    fun hideKeyboard() {
        keyboardView?.animate()
            ?.translationY(keyboardView!!.height.toFloat())
            ?.alpha(0f)
            ?.setDuration(200)
            ?.setInterpolator(AccelerateInterpolator())
            ?.withEndAction {
                keyboardView?.visibility = View.GONE
            }
            ?.start()
    }

    private fun setupKeyboard(view: View) {
        val gridLayout = view.findViewById<GridLayout>(R.id.number_pad)
        val context = gridLayout.context
        val keys = if(inputType == NumpadType.PHONE) phoneKeys() else nominalKeys()

        gridLayout.removeAllViews()

        for (key in keys) {
            val layoutParams = GridLayout.LayoutParams().apply {
                width = 0
                height = dpToPx(context, 52)
                columnSpec = GridLayout.spec(GridLayout.UNDEFINED, 1f)
                setMargins(dpToPx(context, 6), dpToPx(context, 6), dpToPx(context, 6), dpToPx(context, 6))
            }

            val viewToAdd: View = when (key) {
                "←" -> {
                    AppCompatImageButton(context).apply {
                        setImageResource(R.drawable.ic_backspace)
                        layoutParams.setGravity(Gravity.FILL)
                        scaleType = ImageView.ScaleType.CENTER_INSIDE
                        this.layoutParams = layoutParams
                        setBackgroundColor(Color.TRANSPARENT)

                        setOnClickListener {
                            pin = editText.text.toString()
                            if (pin.isNotEmpty()) {
                                pin = pin.dropLast(1)
                                editText.setText(pin)
                                editText.setSelection(pin.length)
                                editText.requestFocus()
                            }
                        }
                    }
                }

                "" -> {
                    Space(context).apply {
                        minimumHeight = dpToPx(context, 52)
                        this.layoutParams = layoutParams
                    }
                }

                else -> {
                    AppCompatButton(context).apply {
                        text = key
                        textSize = 20f
                        setTypeface(typeface, Typeface.BOLD)
                        gravity = Gravity.CENTER
                        background = ContextCompat.getDrawable(context, R.drawable.rounded_keypad_button)
                        this.layoutParams = layoutParams

                        setOnClickListener {
                            pin = editText.text.toString()
                            pin += key
                            editText.setText(pin)
                            editText.setSelection(pin.length)
                        }
                    }
                }
            }

            gridLayout.addView(viewToAdd)
        }
    }

    private fun dpToPx(context: Context, dp: Int): Int {
        return (dp * context.resources.displayMetrics.density).toInt()
    }

    private fun nominalKeys() = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "0", "000", "←")
    private fun phoneKeys() = listOf("1", "2", "3", "4", "5", "6", "7", "8", "9", "", "0", "←")

    fun isTouchInsideNumpad(ev: MotionEvent): Boolean {
        val view = keyboardView ?: return false
        val location = IntArray(2)
        view.getLocationOnScreen(location)
        val x = ev.rawX.toInt()
        val y = ev.rawY.toInt()
        return x >= location[0] && x <= location[0] + view.width &&
                y >= location[1] && y <= location[1] + view.height
    }
}

enum class NumpadType {
    NOMINAL, PHONE
}