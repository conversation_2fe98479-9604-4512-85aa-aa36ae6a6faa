package id.co.bri.brimo.ui.activities.form

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.view.View
import androidx.fragment.app.FragmentTransaction
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.generalform.IFormGeneralRevampPresenter
import id.co.bri.brimo.contract.IView.generalform.IFormGeneralRevampView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.enumconfig.EditOption
import id.co.bri.brimo.domain.config.enumconfig.JourneyType
import id.co.bri.brimo.domain.config.enumconfig.toEditOption
import id.co.bri.brimo.domain.config.enumconfig.toJourneyType
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.ParameterModel
import id.co.bri.brimo.models.ProductListModel
import id.co.bri.brimo.models.apimodel.request.CicilanFinanceRequest
import id.co.bri.brimo.models.apimodel.request.pascabayar.InquiryPascabayarRevampRequest
import id.co.bri.brimo.models.apimodel.response.DataPascaBayarResponse
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse
import id.co.bri.brimo.models.apimodel.response.HistoryResponse
import id.co.bri.brimo.models.apimodel.response.InquiryBrivaRevampResponse
import id.co.bri.brimo.models.apimodel.response.RestResponse
import id.co.bri.brimo.models.apimodel.response.SavedResponse
import id.co.bri.brimo.models.apimodel.response.cicilanrevamp.DataCicilanRevampRes
import id.co.bri.brimo.models.optionmodel.OptionSearchRevampModel
import id.co.bri.brimo.ui.activities.FormBpjsActivity
import id.co.bri.brimo.ui.activities.FormWalletActivity
import id.co.bri.brimo.ui.activities.InquiryKonfirmasiBrivaRevampCloseActivity
import id.co.bri.brimo.ui.activities.base.BaseFormRevampActivity
import id.co.bri.brimo.ui.activities.transferrevamp.FormEditSavedRevampActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogNotice
import id.co.bri.brimo.ui.customviews.dialog.DialogSetDefaultRevamp
import id.co.bri.brimo.ui.fragments.FragmentBottomDialog
import id.co.bri.brimo.ui.fragments.SearchRevampFragment
import id.co.bri.brimo.util.extension.changePage
import id.co.bri.brimo.util.extension.getStringExtraDefault
import id.co.bri.brimo.util.extension.gone
import id.co.bri.brimo.util.extension.onExceptionFeatureOffRevamp
import id.co.bri.brimo.util.extension.visible
import javax.inject.Inject

/**
 * <AUTHOR> on 09/11/24.
 * Silahkan Gunakan Form ini untuk membuat form revamp baru, dan buatkanlah validasi berdasarkan JourneyType nya
 */
class FormGeneralRevampActivity : BaseFormRevampActivity(), IFormGeneralRevampView {

    private var journeyType: JourneyType? = null
    private val revampEndpointSettings by lazy(LazyThreadSafetyMode.NONE) { GeneralTransactionRevampEndpointSettings(journeyType) }
    private var tempRestResponse: RestResponse? = null

    @Inject
    lateinit var presenter: IFormGeneralRevampPresenter<IFormGeneralRevampView>

    companion object {
        private const val JOURNEY_TYPE = "JOURNEY_TYPE"
        private const val IS_FROM_FAST_MENU = "IS_FROM_FAST_MENU"

        @JvmStatic
        fun launchIntent(
            caller: Activity,
            isFromFastMenus: Boolean,
            journeyType: JourneyType,
        ) {
            caller.changePage<FormGeneralRevampActivity>(Constant.REQ_PAYMENT) {
                putExtra(IS_FROM_FAST_MENU, isFromFastMenus)
                putExtra(JOURNEY_TYPE, journeyType.type)
            }
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
    }

    @SuppressLint("NotifyDataSetChanged")
    @Suppress("DEPRECATION")
    @Deprecated("Deprecated in Java")
    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when {
            requestCode == Constant.REQ_EDIT_SAVED && data != null -> {
                if (resultCode == Activity.RESULT_OK) {
                    showSnackbarErrorMessageRevamp(
                        /* message = */ GeneralHelper.getString(R.array.type_option_desc, Constant.EditOption.EDIT),
                        /* messageType = */ ALERT_CONFIRM,
                        /* activity = */ this,
                        /* isFragment = */ false
                    )
                    data.apply {
                        val title = getStringExtra(Constant.TAG_TITLE) ?: ""
                        val value = getStringExtra(Constant.TAG_VALUE) ?: ""
                        val lastPos = savedResponses.indexOfFirst { it.value == value }
                        val newPos = if (lastPos != -1) lastPos else getIntExtra(Constant.TAG_POSITION, 0)
                        savedResponses[newPos].title = title
                        savedAdapter.setSavedResponses(savedResponses)
                        savedAdapter.notifyDataSetChanged()
                    }
                }
            }

            requestCode == Constant.REQ_SAVE_SAVED -> {
                if (resultCode == RESULT_OK) {
                    handleEditOrSaveAction(Constant.EditOption.SAVE)
                } else if (resultCode == RESULT_CANCELED && data != null) {
                    onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE))
                }
            }

            else -> {
                if (resultCode != Activity.RESULT_CANCELED) {
                    data?.getBooleanExtra(Constant.REQUEST_RECEIPT, false)
                    this.setResult(RESULT_OK, data)
                    finish()
                }
            }
        }
    }

    override fun injectDependency() {
        super.injectDependency()
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlForm(revampEndpointSettings.getFormEndpoint())
        presenter.setUrlInquiry(revampEndpointSettings.getInquiryEndpoint())
        presenter.setUrlConfirm(revampEndpointSettings.getConfirmEndpoint())
        presenter.setUrlPayment(revampEndpointSettings.getPaymentEndpoint())
        presenter.getDataForm()
        presenter.start()
    }

    override fun getData(intent: Intent) = with(intent) {
        isFromFastMenu = getBooleanExtra(IS_FROM_FAST_MENU, isFromFastMenu)
        journeyType = getStringExtraDefault(JOURNEY_TYPE).toJourneyType()
    }

    override fun onNoSavedItemSearchNotFound(query: CharSequence?): Pair<String, String> = when (journeyType) {
        JourneyType.JOURNEY_TYPE_REVAMP_CICILAN,
        JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR,
        -> Pair(getString(R.string.no_result_found_title), getString(R.string.no_result_found_desc))
        null -> Pair("", "")
    }

    override fun handleHistorySavedEmpty() {
        gotoNewTransactionPage(false)
    }

    override fun onExceptionFO(response: EmptyStateResponse) {
        onExceptionFeatureOffRevamp(response)
    }

    override fun getTitleBar() = when(journeyType){
        JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> getString(R.string.pasca_title_bar)
        JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> getString(R.string.cicilan_revamp_titlebar)
        null -> ""
    }

    override fun getDefaultIconResource() = when(journeyType){
        JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> R.drawable.akun
        JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> R.drawable.cicilan
        null -> R.drawable.briva
    }

    override fun setTextForm(): Unit = with(binding) {
        tvLastTrx.text = getString(R.string.pembayaran_terakhir)
        tvSavedData.text = getString(R.string.daftar_tersimpan)
        searchview.queryHint = when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> getString(R.string.txt_cari_nama_atau_nomor_hp)
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> getString(R.string.cari_nama_atau_jasa)
            null -> ""
        }
        tvNoHistory.text = when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR,
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN,
            -> getString(R.string.desc_no_history_pendidikan)

            null -> getString(R.string.desc_no_history)
        }
        tvNoFavoritTitle.text = when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR,
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN,
            -> getString(R.string.no_saved_list_edu)

            null -> getString(R.string.no_favorit)
        }
        tvDescNoDataSaved.text = when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> getString(R.string.desc_pascabayar_no_saved)
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> getString(R.string.desc_cicilan_no_saved)
            null -> getString(R.string.desc_briva_no_saved)
        }
        tvNoDataFoundTitle.text = when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> getString(R.string.no_result_found_title)
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> getString(R.string.no_result_found_title)
            null -> getString(R.string.no_result_found_title)
        }
        btnSubmit.text = when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR,
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN,
            -> getString(R.string.buat_pembayaran_baru)

            null -> getString(R.string.tambah_transaksi_baru)
        }
        when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR,
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN,
            -> {
                llAddSavedList.visible()
                llAddSavedList.setOnClickListener { gotoNewTransactionPage(true) }
            }

            null -> Unit
        }
    }

    override fun buttonClick(): Unit = with(binding) {
        btnSubmit.setOnClickListener { gotoNewTransactionPage(false) }
    }

    override fun onSuccessGetRestResponse(restResponse: RestResponse) {
        tempRestResponse = restResponse
    }

    override fun onSuccessGetInquiry(inquiryBrivaRevampResponse: InquiryBrivaRevampResponse, mUrlPayment: String, mUrlConfirm: String) {
        gotoInquiryBrivaRevampClose(inquiryBrivaRevampResponse, mUrlConfirm, mUrlPayment)
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun onSuccessUpdate(savedResponse: SavedResponse, item: Int, type: Int) {
        val message = GeneralHelper.getString(R.array.type_option_desc_revamp, type)
        showSnackbarErrorMessageRevamp(message, ALERT_CONFIRM, this, false)

        val itemPos = savedResponses.indexOfFirst { it.value == savedResponse.value }
        if (type.toEditOption() == EditOption.NON_FAV || type.toEditOption() == EditOption.FAV) {
            savedResponses[itemPos].favorite = type.toEditOption() == EditOption.FAV
            savedResponses.sortByDescending { it.favorite }
        } else if (type.toEditOption() == EditOption.HAPUS) {
            savedResponses.removeAt(itemPos)
            if (savedResponses.size > 0) binding.llNoDataSaved.gone()
            else binding.llNoDataSaved.visibility = View.VISIBLE
        }
        savedAdapter.setSavedResponses(savedResponses)
        savedAdapter.notifyDataSetChanged()
    }

    override fun onClickHistoryItem(historyResponse: HistoryResponse) {
        when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> {
                val paymentNumber =
                    if (historyResponse.value.contains("|")) historyResponse.value.split("|").getOrElse(1) { "" }
                    else historyResponse.value
                presenter.getDataInquiry(InquiryPascabayarRevampRequest(paymentNumber))
            }
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> {
                val historyValue = historyResponse.value
                val str1 = historyValue.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                if (str1.size == 3) {
                    val code = str1[0]
                    val codeGopay = str1[1]
                    val subtitle = str1[2]
                    val purchaseNumber = "$codeGopay|$subtitle"
                    presenter.getDataInquiry(CicilanFinanceRequest(code, purchaseNumber))
                } else {
                    val code = str1[0]
                    val subtitle = str1[1]
                    presenter.getDataInquiry(CicilanFinanceRequest(code, subtitle))
                }
            }
            null -> Unit
        }
    }

    override fun onClickSavedItem(savedResponse: SavedResponse) {
        when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> {
                val split = savedResponse.value.split("|")
                val paymentNumber = split.getOrElse(2) { "" }
                presenter.getDataInquiry(InquiryPascabayarRevampRequest(paymentNumber))
            }
            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> {
                val historyValue = savedResponse.value
                val str1 = historyValue.split("\\|".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
                if (str1.size == 4) {
                    val code = str1[1]
                    val codeGopay = str1[2]
                    val subtitle = str1[3]
                    val purchaseNumber = "$codeGopay|$subtitle"
                    presenter.getDataInquiry(CicilanFinanceRequest(code, purchaseNumber))
                } else {
                    val code = str1[1]
                    val subtitle = str1[2]
                    presenter.getDataInquiry(CicilanFinanceRequest(code, subtitle))
                }
            }
            null -> Unit
        }
    }

    override fun onClickUpdateItem(savedResponse: SavedResponse, position: Int) {
        val fragmentSearchRevamp = SearchRevampFragment(
            fetchList(savedResponse),
            { optionSearchRevampModel: OptionSearchRevampModel ->
                when (optionSearchRevampModel.codeModel) {
                    EditOption.EDIT.option.toString() -> gotoFormEditSavedRevamp(savedResponse, position)
                    EditOption.HAPUS.option.toString() -> showDialogRemoveSavedRevamp(savedResponse, position)
                    EditOption.NON_FAV.option.toString() -> presenter.setUpdateItem(revampEndpointSettings.getUnFavoriteEndpoint(), savedResponse, position, EditOption.NON_FAV.option, journeyType)
                    EditOption.FAV.option.toString() -> presenter.setUpdateItem(revampEndpointSettings.getFavoriteEndpoint(), savedResponse, position, EditOption.FAV.option, journeyType)
                }
            },
            "",
            GeneralHelper.getString(R.string.pengaturan),
            false
        )
        fragmentSearchRevamp.show(supportFragmentManager, "")
    }

    override fun onException58(errorMessage: String) {
        val dialogNotice = DialogNotice(
            { gotoFormWallet() },
            GeneralHelper.getString(R.string.title_fitur_wallet),
            errorMessage,
            GeneralHelper.getString(R.string.baiklah_revamp),
            true,
            true
        )
        val fragmentTransaction = this.supportFragmentManager.beginTransaction()
        fragmentTransaction.add(dialogNotice, null)
        fragmentTransaction.commitAllowingStateLoss()
    }

    override fun onException59(errorMessage: String) {
        val dialogNotice = DialogNotice(
            { gotoFormBpjs() },
            GeneralHelper.getString(R.string.title_fitur_bpjs),
            errorMessage,
            GeneralHelper.getString(R.string.baiklah_revamp),
            true,
            true
        )
        val fragmentTransaction = supportFragmentManager.beginTransaction()
        fragmentTransaction.add(dialogNotice, null)
        fragmentTransaction.commitAllowingStateLoss()
    }

    override fun onBillAlreadyPaid(errorMessage: String) {
        val fragmentBottomDialog = FragmentBottomDialog(this,
            Constant.TRX_TYPE_IPL,
            GeneralHelper.getString(R.string.already_paid_bill_title),
            GeneralHelper.getString(R.string.already_paid_bill_desc),
            "ilustrasi_success_transaction", false,
            { },
            GeneralHelper.getString(R.string.btn_tutup)
        )
        fragmentBottomDialog.isCancelable = false
        fragmentBottomDialog.show(supportFragmentManager, "")
    }

    private fun fetchList(savedResponse: SavedResponse): MutableList<OptionSearchRevampModel> {
        val optionSearchRevampModelArrayList: MutableList<OptionSearchRevampModel> = ArrayList()
        if (savedResponse.favorite) optionSearchRevampModelArrayList.add(OptionSearchRevampModel("ic_opt_fav", GeneralHelper.getString(this, R.string.opt_non_fav), "", 0, 0, EditOption.NON_FAV.option.toString(), true))
        else optionSearchRevampModelArrayList.add(OptionSearchRevampModel("ic_opt_fav", GeneralHelper.getString(this, R.string.opt_fav), "", 0, 0, EditOption.FAV.option.toString(), true))
        optionSearchRevampModelArrayList.add(OptionSearchRevampModel("ic_opt_edit", GeneralHelper.getString(this, R.string.ubah_nama), "", 0, 0, EditOption.EDIT.option.toString(), true))
        optionSearchRevampModelArrayList.add(OptionSearchRevampModel("ic_opt_hapus", GeneralHelper.getString(this, R.string.unfavorit_saved), "", 0, 0, EditOption.HAPUS.option.toString(), true))

        return optionSearchRevampModelArrayList
    }

    private fun setParameter(): ParameterModel {
        val parameterModel = ParameterModel()
        parameterModel.stringLabelTujuan = GeneralHelper.getString(R.string.destination_number_hint)
        parameterModel.stringLabelNominal = GeneralHelper.getString(R.string.payment_amount)
        parameterModel.defaultIcon = getDefaultIconResource()
        return parameterModel
    }

    private fun handleEditOrSaveAction(editOption: Int) {
        val message = GeneralHelper.getString(R.array.type_option_desc_revamp, editOption)
        showSnackbarErrorMessage(message, ALERT_CONFIRM, this, false)
        presenter.getDataForm()
    }

    private fun showDialogRemoveSavedRevamp(savedResponse: SavedResponse, position: Int) {
        val dialogTitle = GeneralHelper.getString(R.string.konfirmasi_hapus_tersimpan_title)
        val dialogDesc = String.format(GeneralHelper.getString(R.string.konfirmasi_hapus_tersimpan_desc), savedResponse.title)
        val dialogSetDefaultRevamp = DialogSetDefaultRevamp(object : DialogSetDefaultRevamp.DialogDefaultListener {
            override fun onClickYesDefault(requestId: Int) {
                presenter.setUpdateItem(revampEndpointSettings.getDeleteSavedListEndpoint(), savedResponse, position, EditOption.HAPUS.option, journeyType)
            }

            override fun onClickNoDefault(requestId: Int) {
                //do nothing
            }
        }, dialogTitle, dialogDesc, GeneralHelper.getString(R.string.hapus), GeneralHelper.getString(R.string.batal), Constant.REQ_EDIT_SAVED)
        val ft: FragmentTransaction = this.supportFragmentManager.beginTransaction()
        ft.add(dialogSetDefaultRevamp, null)
        ft.commitAllowingStateLoss()
    }

    private fun gotoFormBpjs() {
        FormBpjsActivity.launchIntent(this, isFromFastMenu)
    }

    private fun gotoFormWallet() {
        FormWalletActivity.launchIntentNotice(this, isFromFastMenu)
    }

    private fun gotoInquiryBrivaRevampClose(inquiryBrivaRevampResponse: InquiryBrivaRevampResponse, mUrlConfirm: String, mUrlPayment: String) {
        InquiryKonfirmasiBrivaRevampCloseActivity.launchIntent(
            caller = this,
            inquiryRevampResponse = inquiryBrivaRevampResponse,
            urlConfirm = mUrlConfirm,
            urlPayment = mUrlPayment,
            fromFastMenu = isFromFastMenu,
            paremeterModel = setParameter(),
            journeyType = journeyType?.type ?: ""
        )
    }

    private fun gotoFormEditSavedRevamp(savedResponse: SavedResponse, position: Int) {
        FormEditSavedRevampActivity.launchIntentBriva(
            this,
            savedResponse,
            position,
            0,
            revampEndpointSettings.getEditSavedListEndpoint(),
            GeneralHelper.getString(R.string.nomor_tujuan),
            true,
            journeyType?.type ?: ""
        )
    }

    private fun gotoNewTransactionPage(isOnlyAddSaved: Boolean) {
        when (journeyType) {
            JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> TambahGeneralRevampActivity.launchIntent(
                caller = this,
                tambahGeneralRevampSettings = generateTambahGeneralRevampSettings(),
                journeyType = journeyType,
                urlInquiry = if (isOnlyAddSaved) revampEndpointSettings.getAddSavedListEndpoint() else presenter.getUrlInquiry(),
                urlPayment = if (isOnlyAddSaved) revampEndpointSettings.getSaveSavedListEndpoint() else presenter.getUrlPayment(),
                urlConfirm = presenter.getUrlConfirm(),
                isOnlyAddSaved = isOnlyAddSaved,
                productList = fetchProduct()
            )

            JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> ListProductGeneralRevampActivity.launchIntent(
                caller = this,
                fromFastMenu = false,
                journeyType = journeyType,
                productList = fetchProduct(),
                filterList = listOf(),
                tambahGeneralRevampSettings = generateTambahGeneralRevampSettings(),
                isOnlyAddSaved = isOnlyAddSaved
            )
            null -> Unit
        }
    }

    private fun fetchProduct(): MutableList<ProductListModel> = when (journeyType) {
        JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> {
            tempRestResponse?.getData(DataPascaBayarResponse::class.java)?.postpaidList?.map { listResponse ->
                ProductListModel(
                    code = listResponse.code,
                    name = listResponse.name,
                    iconName = listResponse.icon,
                    prefix = listResponse.prefix,
                )
            }?.toMutableList() ?: mutableListOf()
        }
        JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> {
            tempRestResponse?.getData(DataCicilanRevampRes::class.java)?.multifinanceProviders?.map { installmentList ->
                ProductListModel(installmentList.code, installmentList.name, installmentList.iconPath, installmentList.iconName)
            }?.toMutableList() ?: mutableListOf()
        }

        else -> mutableListOf()
    }

    private fun generateTambahGeneralRevampSettings(): TambahGeneralRevampSettings? = when (journeyType) {
        JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR -> TambahGeneralRevampSettings(
            toolbar = getString(R.string.hint_no_hp),
            editTextTitle = getString(R.string.txt_nomor_hp),
            editTextHint = getString(R.string.masukkan_nomor_hp),
            editTextIconStart = R.drawable.ic_phone_grey_revamp,
            editTextIconEnd = R.drawable.ic_contact_number_blue_80,
        )

        JourneyType.JOURNEY_TYPE_REVAMP_CICILAN -> TambahGeneralRevampSettings(
            toolbar = getString(R.string.masukkan_nomor_titlebar),
            editTextTitle = getString(R.string.label_input_cust_num),
            editTextHint = getString(R.string.placeholder_input_cust_num),
            editTextIconStart = R.drawable.ic_ktp_primary,
            editTextIconEnd = 0,
            toolbarList = getString(R.string.pilih_jasa_titlebar),
            editTextHintList = getString(R.string.cari_penyedia_jasa)
        )

        null -> null
    }
}