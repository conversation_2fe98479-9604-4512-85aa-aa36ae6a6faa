package id.co.bri.brimo.ui.activities;

import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.nfc.NfcAdapter;
import android.nfc.NfcManager;
import android.nfc.Tag;
import android.nfc.tech.IsoDep;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;

import androidx.annotation.Nullable;
import androidx.appcompat.app.AlertDialog;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.brizzi.ICekBrizzi2Presenter;
import id.co.bri.brimo.contract.IView.brizzi.ICekBrizzi2View;
import id.co.bri.brimo.databinding.ActivityCekBrizzi2Binding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.ParameterKonfirmasiModel;
import id.co.bri.brimo.models.PendingBrizziResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse;
import id.co.bri.brimo.models.apimodel.response.InquiryBrizziResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brizzi.Brizzi;


public class CekBrizziDuaActivity extends BaseActivity implements ICekBrizzi2View {

    private ActivityCekBrizzi2Binding binding;

    private static final String TAG = "CekBrizzi2";

    NfcManager nfcManager;
    private NfcAdapter adapter;

    protected static boolean mIsFromTopUpOnline;


    static GeneralConfirmationResponse mbrivaResponse;
    static String mUrlPayment;
    static String mTitle;
    static String mPin;
    static ParameterKonfirmasiModel mParameterKonfirmasiModel;
    static Brizzi mbrizzi;
    static PendingBrizziResponse mPendingBrizziResponse;

    @Inject
    ICekBrizzi2Presenter<ICekBrizzi2View> cekbrizzipresenter;

    public static void launchIntent(Activity caller, GeneralConfirmationResponse generalConfirmationResponse, String urlPayment, String pin, String title, ParameterKonfirmasiModel parameterKonfirmasiModel, Brizzi brizzi, boolean isFromFastmenu) {
        Intent intent = new Intent(caller, CekBrizziDuaActivity.class);
        mbrivaResponse = generalConfirmationResponse;
        mUrlPayment = urlPayment;
        mTitle = title;
        mbrizzi = brizzi;
        mPin = pin;
        isFromFastMenu = isFromFastmenu;
        mParameterKonfirmasiModel = parameterKonfirmasiModel;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityCekBrizzi2Binding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.tbBriva.toolbar, "BRIZZI");

        injectDependency();

        try {
            nfcManager = (NfcManager) getApplicationContext().getSystemService(Context.NFC_SERVICE);
            adapter = nfcManager.getDefaultAdapter();

            if (adapter == null) {
                adapter = NfcAdapter.getDefaultAdapter(this);
                if (adapter == null) {
                    showSnackbarErrorMessage(GeneralHelper.getString(R.string.brizzi_device_tidak_support_nfc), ALERT_ERROR, this, false);
                } else {
                    if (!adapter.isEnabled()) {
                        AlertDialog.Builder alertbox = new AlertDialog.Builder(this);
                        alertbox.setMessage(GeneralHelper.getString(R.string.brizzi_aktifkan_nfc_lanjut));
                        alertbox.setPositiveButton(GeneralHelper.getString(R.string.aktifkan2), new DialogInterface.OnClickListener() {
                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                {
                                    Intent intent = new Intent(Settings.ACTION_NFC_SETTINGS);
                                    startActivity(intent);
                                }
                            }
                        });
                        alertbox.setNegativeButton(GeneralHelper.getString(R.string.batal), new DialogInterface.OnClickListener() {

                            @Override
                            public void onClick(DialogInterface dialog, int which) {
                                finish();
                            }
                        });
                        alertbox.show();
                    } else {
                        onNewIntent(getIntent());
                    }
                }

            }
        } catch (Exception e) {
            showSnackbarErrorMessage("Silakan coba kembali.", ALERT_ERROR, this, false);
        }
    }


    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (cekbrizzipresenter != null) {
            if (isFromFastMenu) {
                cekbrizzipresenter.setView(this);
                cekbrizzipresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_fm_check_top_up_brizzi));
                cekbrizzipresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_payment_top_up_brizzi_fm));
                cekbrizzipresenter.setValidateUrl(GeneralHelper.getString(R.string.url_validate_top_up_brizzi_fm));
                cekbrizzipresenter.start();

            } else {
                cekbrizzipresenter.setView(this);
                cekbrizzipresenter.setInquiryUrl(GeneralHelper.getString(R.string.url_check_top_up_brizzi));
                cekbrizzipresenter.setPaymentUrl(GeneralHelper.getString(R.string.url_payment_top_up_brizzi));
                cekbrizzipresenter.setValidateUrl(GeneralHelper.getString(R.string.url_validate_top_up_brizzi));
                cekbrizzipresenter.start();
            }
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (adapter != null) {
            Intent intent = new Intent(this, CekBrizziDuaActivity.class);
            intent.setFlags(Intent.FLAG_RECEIVER_REPLACE_PENDING);

            //perbaikan pendingIntent Android 31
            int currentFlags = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ? PendingIntent.FLAG_MUTABLE | PendingIntent.FLAG_UPDATE_CURRENT : 0;
            PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, currentFlags);

            IntentFilter[] filters = new IntentFilter[]{};
            adapter.enableForegroundDispatch(this, pendingIntent, filters, null);
        }
    }


    @Override
    protected void onPause() {
        super.onPause();
        adapter.disableForegroundDispatch(this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        try {
            //Your Implementation Here
            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            IsoDep brizziTag = IsoDep.get(tag);
            mbrizzi = new Brizzi(brizziTag);
            String tempRC = " ";
            Long y = mbrivaResponse.getPayAmount();

            cekbrizzipresenter.topUpOnlinePres(mbrizzi, String.valueOf(y), isFromFastMenu);
        } catch (Exception e) {
            showSnackbarErrorMessage("Kartu Gagal Terbaca", ALERT_ERROR, this, false);
        }

    }

    @Override
    public void showErrorMessage(String message) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    /**
     * Method nembak ke service buat dapatin hostRand
     */
    @Override
    public void onSuccesGetRandomHost(InquiryBrizziResponse inquiryBrizziResponse) {
        cekbrizzipresenter.continueTopUpOnlinePres(mbrizzi, inquiryBrizziResponse, mPin, mbrivaResponse, isFromFastMenu);
    }

    /**
     * Method berhasil setelah checkbalance lanjut masuk ke tahap commit
     */
    @Override
    public void onSuccesGetCommit(PendingBrizziResponse pendingBrizziResponse) {
        mPendingBrizziResponse = pendingBrizziResponse;
//        commitContinue(pendingBrizziResponse);
        cekbrizzipresenter.commitContinuePres(pendingBrizziResponse, mbrizzi, isFromFastMenu);
    }

    /**
     * Method berhasil setelah commit lanjut ke receipt
     */
    @Override
    public void onSuccesGetValidate() {
        ReceiptActivity.launchIntent(this, mPendingBrizziResponse);
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException01(String message) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message);
        setResult(RESULT_OK);
    }

    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();
        if (message.contains("PIN")) {
            returnIntent.putExtra(Constant.TAG_ERROR_PIN_BRIZZI, message);
        } else {
            returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);
        }
        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onExceptionGagalDefault(String message) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }


    @Override
    public void onException01OnValidate(String messagePayment, String messageValidate) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, messagePayment);
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE, messageValidate);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }


    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (data != null && requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data);
                this.finish();
            } else {
                this.setResult(RESULT_CANCELED, data);
                this.finish();
            }
        }
    }

    @Override
    protected void onDestroy() {
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        cekbrizzipresenter.stop();
        super.onBackPressed();
    }

}