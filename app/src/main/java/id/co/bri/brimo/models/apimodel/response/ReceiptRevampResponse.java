package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.models.apimodel.response.cc.CardDataKki;
import id.co.bri.brimo.models.apimodel.response.lifestyle.AddressDetail;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ExpeditionDetail;
import id.co.bri.brimo.models.apimodel.response.lifestyle.TripDetail;
import id.co.bri.brimo.models.apimodel.response.lifestyle.ekspedisi.DetailDataView;

import id.co.bri.brimo.models.apimodel.response.rdnrevamp.RdnContactUs;
import id.co.bri.brimo.models.apimodel.response.travel.DetailRoute;

public class ReceiptRevampResponse {
    @SerializedName("immediately_flag")
    @Expose
    private Boolean immediatelyFlag;
    @SerializedName("total_data_view")
    @Expose
    private List<DataView> totalDataView;
    @SerializedName("header_data_view")
    @Expose
    private List<DataView> headerDataView;
    @SerializedName("source_account_data_view")
    @Expose
    private DetailListType sourceAccountDataView;
    @SerializedName("billing_detail")
    @Expose
    private DetailListType billingDetail;
    @SerializedName("voucher_data_view")
    @Expose
    private List<DataView> voucherDataView = null;
    @SerializedName("eticket_data_view")
    @Expose
    private List<DataView> eticketDataView = null;
    @SerializedName("data_view_transaction")
    @Expose
    private List<DataView> transactionDataView;
    @SerializedName("detail_data_view")
    @Expose
    private List<DataView> detailDataView;
    @SerializedName("detail_amount_data_view")
    @Expose
    private List<DataView> detailAmountDataView;
    @SerializedName("amount_data_view")
    @Expose
    private List<DataView> amountDataView;
    @SerializedName("date_transaction")
    @Expose
    private String dateTransaction;
    @SerializedName("on_process")
    @Expose
    private boolean onProcess;
    @SerializedName("share")
    @Expose
    private String share;
    @SerializedName("help_flag")
    @Expose
    private boolean helpFlag;
    @SerializedName("help_center")
    @Expose
    private boolean helpCenter;
    @SerializedName("title")
    @Expose
    private String title;
    @SerializedName("subtitle")
    @Expose
    private String subtitle;
    @SerializedName("title_image")
    @Expose
    private String titleImage;
    @SerializedName("footer")
    @Expose
    private String footer;
    @SerializedName("row_data_show")
    @Expose
    private int rowDataShow;
    @SerializedName("footer_html")
    @Expose
    private String footerHtml;
    @SerializedName("reference_number")
    @Expose
    private String referenceNumber;
    @SerializedName("share_button_string")
    @Expose
    private String shareButtonString;
    @SerializedName("close_button_string")
    @Expose
    private String closeButtonString;
    @SerializedName("voucher_game_id")
    @Expose
    private String voucherGameId;
    @SerializedName("detail_route")
    private List<TripDetail> detailRoutes;
    @SerializedName("streaming_id")
    @Expose
    private String streamingId;
    @SerializedName("additional_info")
    @Expose
    private String additionalInfo;
    @SerializedName("subtitle_html")
    @Expose
    private String subtitleHtml;
    @SerializedName("order_detail")
    @Expose
    private List<DataView> orderDetail;
    @SerializedName("merchant_data")
    @Expose
    private DetailListType merchantData;
    @SerializedName("card_data")
    @Expose
    private CardDataKki cardDataKki;
    @SerializedName("detail_button_string")
    @Expose
    private String detailButtonString;
    @SerializedName("binding_text")
    @Expose
    private String bindingText;
    @SerializedName("binding_response")
    @Expose
    private String bindingResponse;
    @SerializedName("status_order")
    @Expose
    private List<DataView> statusOrder;
    @SerializedName("order_number")
    @Expose
    private String orderNumber = "";
    @SerializedName("product_code")
    @Expose
    private String productCode;

    @SerializedName("button_redirect_text")
    @Expose
    private String buttonRedirectText;
    @SerializedName("journey_type")
    @Expose
    private String journeyType;
    @SerializedName("package_detail")
    @Expose
    private DetailDataView packageDetail;
    @SerializedName("tracking_data_view")
    @Expose
    private List<DataView> trackingDataView;
    @SerializedName("address_detail")
    @Expose
    private AddressDetail addressDetail;
    @SerializedName("trip_detail")
    @Expose
    private List<TripDetail> tripDetail = null;
    @SerializedName("expedition_detail")
    @Expose
    private ExpeditionDetail expeditionDetail;
    @SerializedName("billing_amount_detail")
    @Expose
    private List<DataView> billingAmountDetail;
    @SerializedName("receipt_pattern_code")
    @Expose
    private String receiptPatternCode;


    @SerializedName("info")
    @Expose
    private String webviewInfo;

    @SerializedName("main_data_view")
    @Expose
    private ArrayList<DataView> mainDataView;
    @SerializedName("transaction_date")
    @Expose
    private String transactionDate;
    @SerializedName("transaction_image")
    @Expose
    private String transactionImage;
    @SerializedName("transaction_success")
    @Expose
    private Boolean transactionSuccess;
    @SerializedName("detail_open_junio")
    @Expose
    private ArrayList<DataView> detailOpenJunio;
    @SerializedName("kurs_data_view")
    @Expose
    private ArrayList<DataView> kursDataView;
    @SerializedName("sub_title")
    @Expose
    private String subTitle;
    @SerializedName("description")
    @Expose
    private String description;
    @SerializedName("reference_data_view")
    @Expose
    private ArrayList<DataView> referenceDataView;
    @SerializedName("transaction_data_view")
    @Expose
    private ArrayList<DataView> transactionDataViewPending;


    @SerializedName("minim_data_transaction")
    @Expose
    private ArrayList<DataView> minimDataTransaction;

    @SerializedName("is_show_detail")
    @Expose
    private Boolean isShowDetail;

    @SerializedName("product_detail")
    @Expose
    private ProductResponse productDetail;

    @SerializedName("trx_id")
    @Expose
    private String trxId;

    @SerializedName("payment_drawer")
    @Expose
    private InfoResponse paymentDrawer;

    @SerializedName("billing_code")
    @Expose
    private String billingCode = "";

    @SerializedName("feature_code")
    @Expose
    private String featureCode = "";

    @SerializedName("type")
    @Expose
    private String type = "";

    @SerializedName("is_success_wd")
    @Expose
    private int isSccessWd = 0;

    @SerializedName("rdn_contact_us")
    @Expose
    private RdnContactUs rdnContactUs;

    @SerializedName("transaction_number")
    @Expose
    private String transactionNumber = "";

    public String getTransactionNumber() {
        return transactionNumber;
    }

    public void setTransactionNumber(String transactionNumber) {
        this.transactionNumber = transactionNumber;
    }

    public RdnContactUs getRdnContactUs() {
        return rdnContactUs;
    }

    public void setRdnContactUs(RdnContactUs rdnContactUs) {
        this.rdnContactUs = rdnContactUs;
    }

    public int getIsSccessWd() {
        return isSccessWd;
    }

    public void setIsSccessWd(int isSccessWd) {
        this.isSccessWd = isSccessWd;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Boolean getImmediatelyFlag() {
        return immediatelyFlag;
    }

    public void setImmediatelyFlag(Boolean immediatelyFlag) {
        this.immediatelyFlag = immediatelyFlag;
    }

    public List<DataView> getTotalDataView() {
        return totalDataView;
    }

    public void setTotalDataView(List<DataView> totalDataView) {
        this.totalDataView = totalDataView;
    }

    public List<DataView> getHeaderDataView() {
        return headerDataView;
    }

    public void setHeaderDataView(List<DataView> headerDataView) {
        this.headerDataView = headerDataView;
    }

    public DetailListType getSourceAccountDataView() {
        return sourceAccountDataView;
    }

    public void setSourceAccountDataView(DetailListType sourceAccountDataView) {
        this.sourceAccountDataView = sourceAccountDataView;
    }

    public DetailListType getBillingDetail() {
        return billingDetail;
    }

    public void setBillingDetail(DetailListType billingDetail) {
        this.billingDetail = billingDetail;
    }

    public List<DataView> getVoucherDataView() {
        return voucherDataView;
    }

    public void setVoucherDataView(List<DataView> voucherDataView) {
        this.voucherDataView = voucherDataView;
    }

    public List<DataView> getTransactionDataView() {
        return transactionDataView;
    }

    public void setTransactionDataView(List<DataView> transactionDataView) {
        this.transactionDataView = transactionDataView;
    }

    public List<DataView> getDetailDataView() {
        return detailDataView;
    }

    public void setDetailDataView(List<DataView> detailDataView) {
        this.detailDataView = detailDataView;
    }

    public List<DataView> getAmountDataView() {
        return amountDataView;
    }

    public void setAmountDataView(List<DataView> amountDataView) {
        this.amountDataView = amountDataView;
    }

    public String getDateTransaction() {
        return dateTransaction;
    }

    public void setDateTransaction(String dateTransaction) {
        this.dateTransaction = dateTransaction;
    }

    public boolean isOnProcess() {
        return onProcess;
    }

    public void setOnProcess(boolean onProcess) {
        this.onProcess = onProcess;
    }

    public String getShare() {
        return share;
    }

    public void setShare(String share) {
        this.share = share;
    }

    public boolean isHelpFlag() {
        return helpFlag;
    }

    public void setHelpFlag(boolean helpFlag) {
        this.helpFlag = helpFlag;
    }

    public boolean isHelpCenter() {
        return helpCenter;
    }

    public void setHelpCenter(boolean helpCenter) {
        this.helpCenter = helpCenter;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getSubtitle() {
        return subtitle;
    }

    public void setSubtitle(String subtitle) {
        this.subtitle = subtitle;
    }

    public String getTitleImage() {
        return titleImage;
    }

    public void setTitleImage(String titleImage) {
        this.titleImage = titleImage;
    }

    public String getFooter() {
        return footer;
    }

    public void setFooter(String footer) {
        this.footer = footer;
    }

    public String getFooterHtml() {
        return footerHtml;
    }

    public void setFooterHtml(String footerHtml) {
        this.footerHtml = footerHtml;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public String getShareButtonString() {
        return shareButtonString;
    }

    public void setShareButtonString(String shareButtonString) {
        this.shareButtonString = shareButtonString;
    }

    public String getCloseButtonString() {
        return closeButtonString;
    }

    public void setCloseButtonString(String closeButtonString) {
        this.closeButtonString = closeButtonString;
    }

    public int getRowDataShow() {
        return rowDataShow;
    }

    public void setRowDataShow(int rowDataShow) {
        this.rowDataShow = rowDataShow;
    }

    public String getVoucherGameId() {
        return voucherGameId;
    }

    public String getSubtitleHtml() {
        return subtitleHtml;
    }

    public void setSubtitleHtml(String subtitleHtml) {
        this.subtitleHtml = subtitleHtml;
    }

    public void setVoucherGameId(String voucherGameId) {
        this.voucherGameId = voucherGameId;
    }

    public List<DataView> getEticketDataView() {
        return eticketDataView;
    }

    public void setEticketDataView(List<DataView> eticketDataView) {
        this.eticketDataView = eticketDataView;
    }

    public List<TripDetail> getDetailRoutes() {
        return detailRoutes;
    }

    public void setDetailRoutes(List<TripDetail> detailRoutes) {
        this.detailRoutes = detailRoutes;
    }

    public String getAdditionalInfo() {
        return additionalInfo;
    }

    public void setAdditionalInfo(String additionalInfo) {
        this.additionalInfo = additionalInfo;
    }

    public String getStreamingId() {
        return streamingId;
    }

    public void setStreamingId(String streamingId) {
        this.streamingId = streamingId;
    }

    public List<DataView> getOrderDetail() {
        return orderDetail;
    }

    public DetailListType getMerchantData() {
        return merchantData;
    }

    public void setMerchantData(DetailListType merchantData) {
        this.merchantData = merchantData;
    }

    public CardDataKki getCardDataKki() {
        return cardDataKki;
    }

    public void setCardDataKki(CardDataKki cardData) {
        this.cardDataKki = cardData;
    }

    public String getDetailButtonString() {
        return detailButtonString;
    }

    public void setDetailButtonString(String detailButtonString) {
        this.detailButtonString = detailButtonString;
    }

    public String getBindingText() {
        return bindingText;
    }

    public void setBindingText(String bindingText) {
        this.bindingText = bindingText;
    }

    public String getBindingResponse() {
        return bindingResponse;
    }

    public void setBindingResponse(String bindingResponse) {
        this.bindingResponse = bindingResponse;
    }
    public void setOrderDetail(List<DataView> orderDetail) {
        this.orderDetail = orderDetail;
    }

    public List<DataView> getStatusOrder() {
        return statusOrder;
    }

    public void setStatusOrder(List<DataView> statusOrder) {
        this.statusOrder = statusOrder;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public String getProductCode() {
        return productCode;
    }

    public void setProductCode(String productCode) {
        this.productCode = productCode;
    }

    public boolean isVoucherGame() {
        if (voucherGameId != null && !voucherGameId.isEmpty() && !voucherGameId.equals("")) {
            return true;
        } else {
            return false;
        }
    }

    public boolean isVoucherStreaming() {
        if (streamingId != null && !streamingId.isEmpty() && !streamingId.equals("")) {
            return true;
        } else {
            return false;
        }
    }

    public String getButtonRedirectText() {
        return buttonRedirectText;
    }

    public void setButtonRedirectText(String buttonRedirectText) {
        this.buttonRedirectText = buttonRedirectText;
    }

    public String getJourneyType() {
        return journeyType;
    }

    public void setJourneyType(String journeyType) {
        this.journeyType = journeyType;
    }

    public DetailDataView getPackageDetail() {
        return packageDetail;
    }

    public void setPackageDetail(DetailDataView packageDetail) {
        this.packageDetail = packageDetail;
    }

    public AddressDetail getAddressDetail() {
        return addressDetail;
    }

    public void setAddressDetail(AddressDetail addressDetail) {
        this.addressDetail = addressDetail;
    }

    public List<TripDetail> getTripDetail() {
        return tripDetail;
    }

    public void setTripDetail(List<TripDetail> tripDetail) {
        this.tripDetail = tripDetail;
    }

    public ExpeditionDetail getExpeditionDetail() {
        return expeditionDetail;
    }

    public void setExpeditionDetail(ExpeditionDetail expeditionDetail) {
        this.expeditionDetail = expeditionDetail;
    }

    public List<DataView> getBillingAmountDetail() {
        return billingAmountDetail;
    }

    public void setBillingAmountDetail(List<DataView> billingAmountDetail) {
        this.billingAmountDetail = billingAmountDetail;
    }

    public String getReceiptPatternCode() {
        return receiptPatternCode;
    }

    public void setReceiptPatternCode(String receiptPatternCode) {
        this.receiptPatternCode = receiptPatternCode;
    }

    public List<DataView> getTrackingDataView() {
        return trackingDataView;
    }

    public void setTrackingDataView(List<DataView> trackingDataView) {
        this.trackingDataView = trackingDataView;
    }

    public String getWebviewInfo() {
        return webviewInfo;
    }

    public void setWebviewInfo(String webviewInfo) {
        this.webviewInfo = webviewInfo;
    }

    public ArrayList<DataView> getMainDataView() {
        return mainDataView;
    }

    public void setMainDataView(ArrayList<DataView> mainDataView) {
        this.mainDataView = mainDataView;
    }

    public String getTransactionDate() {
        return transactionDate;
    }

    public void setTransactionDate(String transactionDate) {
        this.transactionDate = transactionDate;
    }

    public String getTransactionImage() {
        return transactionImage;
    }

    public void setTransactionImage(String transactionImage) {
        this.transactionImage = transactionImage;
    }

    public ArrayList<DataView> getTransactionDataViewPending() {
        return transactionDataViewPending;
    }

    public void setTransactionDataViewPending(ArrayList<DataView> transactionDataViewPending) {
        this.transactionDataViewPending = transactionDataViewPending;
    }

    public ArrayList<DataView> getDetailOpenJunio() {
        return detailOpenJunio;
    }

    public void setDetailOpenJunio(ArrayList<DataView> detailOpenJunio) {
        this.detailOpenJunio = detailOpenJunio;
    }

    public ArrayList<DataView> getKursDataView() {
        return kursDataView;
    }

    public void setKursDataView(ArrayList<DataView> kursDataView) {
        this.kursDataView = kursDataView;
    }

    public String getSubTitle() {
        return subTitle;
    }

    public void setSubTitle(String subTitle) {
        this.subTitle = subTitle;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public ArrayList<DataView> getReferenceDataView() {
        return referenceDataView;
    }

    public void setReferenceDataView(ArrayList<DataView> referenceDataView) {
        this.referenceDataView = referenceDataView;
    }

    public List<DataView> getDetailAmountDataView() {
        return detailAmountDataView;
    }

    public void setDetailAmountDataView(List<DataView> detailAmountDataView) {
        this.detailAmountDataView = detailAmountDataView;
    }

    public ArrayList<DataView> getMinimDataTransaction() {
        return minimDataTransaction;
    }

    public void setMinimDataTransaction(ArrayList<DataView> minimDataTransaction) {
        this.minimDataTransaction = minimDataTransaction;
    }

    public Boolean getShowDetail() {
        return isShowDetail;
    }

    public void setShowDetail(Boolean showDetail) {
        isShowDetail = showDetail;
    }

    public ProductResponse getProductDetail() {
        return productDetail;
    }

    public void setProductDetail(ProductResponse productDetail) {
        this.productDetail = productDetail;
    }

    public String getTrxId() {
        return trxId;
    }

    public void setTrxId(String trxId) {
        this.trxId = trxId;
    }

    public InfoResponse getPaymentDrawer() {
        return paymentDrawer;
    }

    public void setPaymentDrawer(InfoResponse paymentDrawer) {
        this.paymentDrawer = paymentDrawer;
    }

    public Boolean getTransactionSuccess() {
        return transactionSuccess;
    }

    public void setTransactionSuccess(Boolean transactionSuccess) {
        this.transactionSuccess = transactionSuccess;
    }

    public String getFeatureCode() {
        return featureCode;
    }

    public void setFeatureCode(String featureCode) {
        this.featureCode = featureCode;
    }

    public String getBillingCode() {
        return billingCode;
    }

    public void setBillingCode(String billingCode) {
        this.billingCode = billingCode;
    }
}