package id.co.bri.brimo.ui.activities.changepassnewskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.OnBackPressedCallback
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.ubahpassword.IUbahPasswordBaruPresenter
import id.co.bri.brimo.contract.IView.ubahpassword.IUbahPasswordBaruView
import id.co.bri.brimo.databinding.ActivityUbahPasswordBaruNewskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.ui.activities.SuccessNewSkinActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.util.extension.view.addMinLengthCharValidation
import id.co.bri.brimo.util.extension.view.disableCopyPaste
import id.co.bri.brimo.util.extension.view.onTextChanged
import id.co.bri.brimo.util.extension.view.preventSpaceInput
import id.co.bri.brimo.util.extension.view.togglePasswordVisibility
import javax.inject.Inject

class ChangePassActivity : NewSkinBaseActivity(), IUbahPasswordBaruView {

    private lateinit var binding: ActivityUbahPasswordBaruNewskinBinding
    private var isPasswordVisible = false
    private var isConfirmPasswordVisible = false
    private val handler = Handler(Looper.getMainLooper())

    @Inject
    lateinit var ubahPasswordBaruPresenter: IUbahPasswordBaruPresenter<IUbahPasswordBaruView>

    companion object {
        var mDescError: String? = null
        val TAG: String = "UbahPasswordActivity"
        var stringRefNumber: String = ""
        const val EXTRA_PIN_ENTRY_TYPE = "extra_pin_entry_type"


        fun launchIntent(caller: Activity, ref: String) {
            val intent = Intent(caller, ChangePassActivity::class.java)
            stringRefNumber = ref
            caller.startActivityForResult(intent, Constant.REQ_UBAH_KATA_KUNCI)
        }

        fun launchIntentError(caller: Activity, desc: String?) {
            val intent = Intent(caller, ChangePassActivity::class.java)
            caller.startActivity(intent)
            mDescError = desc
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityUbahPasswordBaruNewskinBinding.inflate(layoutInflater)
        setContentView(binding.root)
        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)
        observeNewKataKunci()
        observeConfirmObserveNewKataKunci()
        injectDependency()
        setupView()

    }

    private fun injectDependency() {
        activityComponent.inject(this)
        if (ubahPasswordBaruPresenter != null) {
            ubahPasswordBaruPresenter.view = this
            ubahPasswordBaruPresenter.setUrlValidatePassKunci(GeneralHelper.getString(R.string.v5_check_password_submit))
            ubahPasswordBaruPresenter.setUrlValidateOldPass(GeneralHelper.getString(R.string.v5_check_password_check))
            ubahPasswordBaruPresenter.start()
        }
    }

    private fun setupView() {
        GeneralHelperNewSkin.setToolbarWithoutNav(binding.toolbar.toolbar, getString(R.string.ubah_password))
        setupTogglePasswordVisibility()
        binding.apply {
            inputPassword.preventSpaceInput()
            inputConfirmPassword.preventSpaceInput()

            inputPassword.disableCopyPaste()
            inputConfirmPassword.disableCopyPaste()

            inputConfirmPassword.editText.isEnabled = false
        }
    }

    private fun observeNewKataKunci() = with(binding) {
        inputPassword.onTextChanged { s ->
            val input = s?.toString() ?: ""
            val isValidChar = input.length >= 8
            val hasUppercase = input.any { it.isUpperCase() }
            val hasLowercase = input.any { it.isLowerCase() }
            val hasDigit = input.any { it.isDigit() }
            val hasSymbol = input.any { !it.isLetterOrDigit() }
            val noSpace = input.isNotEmpty() && !input.contains(" ")

            val isValidAlpha = hasUppercase && hasLowercase && hasDigit && hasSymbol
            val allValid = isValidChar && isValidAlpha && noSpace

            inputConfirmPassword.editText.isEnabled = allValid

            ivCharacter.setImageResource(if (isValidChar) R.drawable.tick_circle else R.drawable.tick_circle_disable)
            ivCapital.setImageResource(if (isValidAlpha) R.drawable.tick_circle else R.drawable.tick_circle_disable)
            ivSpace.setImageResource(if (noSpace) R.drawable.tick_circle else R.drawable.tick_circle_disable)

            if (allValid) {
//                handler.postDelayed({
//                    ubahPasswordBaruPresenter.confirmNewPass()
//                }, 2000)
            } else {
                btnNext.isEnabled = false
            }
        }

        inputPassword.addMinLengthCharValidation(
            minLength = 8,
            errorText = "Minimal harus 8 digit angka",
            debounceDelayOnError = 3000L
        )
    }

    private fun observeConfirmObserveNewKataKunci() = with(binding) {
        inputConfirmPassword.onTextChanged { s ->
            val confirm = s?.toString() ?: ""
            val pass = inputPassword.getText()
            val isMatch = confirm == pass && confirm.isNotEmpty()

            ivSame.setImageResource(if (isMatch) R.drawable.tick_circle else R.drawable.tick_circle_disable)
            btnNext.isEnabled = isMatch

            if (isMatch) {

            }
        }
    }

    private fun setupTogglePasswordVisibility() = with(binding) {
        inputPassword.inputLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            inputPassword.togglePasswordVisibility(isPasswordVisible)
        }

        inputConfirmPassword.inputLayout.setEndIconOnClickListener {
            isConfirmPasswordVisible = !isConfirmPasswordVisible
            inputConfirmPassword.togglePasswordVisibility(isConfirmPasswordVisible)
        }
    }


    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_UBAH_KATA_KUNCI && resultCode == Activity.RESULT_OK) {
            setResult(Activity.RESULT_OK, data)
            finish()
        }
        if (requestCode == Constant.REQ_UBAH_KATA_KUNCI && resultCode == Activity.RESULT_CANCELED) {
            setResult(Activity.RESULT_CANCELED, data)
            finish()
        }

        if (requestCode == Constant.REQ_UBAH_KATA_KUNCI && resultCode == 3) {
            setResult(3, data)
            finish()
        }
    }


    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, 9)
                setResult(Activity.RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onDestroy() {
        ubahPasswordBaruPresenter?.stop()
        super.onDestroy()
    }

    override fun getPasswordBaru(): String {
        return binding.inputPassword.getText().toString()
    }

    override fun getRefNumber(): String {
        return stringRefNumber
    }

    override fun getKonfirmasiPassword(): String {
        return binding.inputConfirmPassword.getText().toString()
    }

    override fun onSubmitSuccess(ref_number: String?, cell_number: String?) {
        SuccessNewSkinActivity.launchIntent(this, "Selamat", "Password barumu berhasil diubah", true)
    }

    override fun onException93(desc: String?) {
        //do nothing
    }

    override fun onError() {
        var firstBtnFunction: Runnable = Runnable {
            ubahPasswordBaruPresenter.onUbahPasswordSubmit()
        }
        OpenBottomSheetGeneralNewSkinFragment.showDialogInformation(
            supportFragmentManager,
            "",
            "ic_fail_newskin",
            "Perubahan Gagal Disimpan",
            "Silakan coba beberapa saat lagi",
            createKotlinFunction0(firstBtnFunction),
            false,
            "Coba Lagi"
        )
    }

//    override fun isScreenshotDisabled() = true

    override fun onFailConfirm() {
        binding.ivDiffPass.setImageResource(R.drawable.tick_circle_disable)
    }

    override fun onSuccessConfirm() {
        binding.ivDiffPass.setImageResource(R.drawable.tick_circle)
        binding.inputConfirmPassword.isEnabled = true
    }
}