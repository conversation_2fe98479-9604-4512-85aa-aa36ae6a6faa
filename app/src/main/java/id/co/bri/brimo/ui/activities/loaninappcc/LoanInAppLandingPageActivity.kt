package id.co.bri.brimo.ui.activities.loaninappcc

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import androidx.viewpager.widget.ViewPager.OnPageChangeListener
import id.co.bri.brimo.adapters.loaninapp.LoanInAppBenefitsPageAdapter
import id.co.bri.brimo.contract.IPresenter.loaninapp.ILoanInAppLandingPresenter
import id.co.bri.brimo.contract.IView.loaninapp.ILoanInAppLandingView
import id.co.bri.brimo.databinding.ActivityLoanInAppCcLandingPageBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.models.loaninapp.LoanInAppOnboardingModel
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.laoninapp.LoanInAppSchemeBottomFragment
import id.co.bri.brimo.util.extension.addFlagClearSingleTop
import id.co.bri.brimo.util.extension.debugMessage
import id.co.bri.brimo.util.extension.setBackgroundTransparent
import id.co.bri.brimo.util.extension.setOnSingleClickListener
import java.util.Timer
import java.util.TimerTask
import javax.inject.Inject

class LoanInAppLandingPageActivity :
    BaseActivity(),
    ILoanInAppLandingView
{
    private var loanInAppOnboardingModel: LoanInAppOnboardingModel = LoanInAppOnboardingModel()

    private lateinit var timer: Timer
    private lateinit var binding: ActivityLoanInAppCcLandingPageBinding

    private val loanInAppBenefitsPageAdapter by lazy(LazyThreadSafetyMode.NONE) { LoanInAppBenefitsPageAdapter(supportFragmentManager) }

    @Inject
    lateinit var landingPresenter: ILoanInAppLandingPresenter<ILoanInAppLandingView>

    companion object {
        private const val LOAN_IN_APP_DIALOG_TAG = "LoanInAppLandingPageActivity - showSchemeDialog"

        fun launchIntent(caller: Activity) {
            val intent = Intent(caller, LoanInAppLandingPageActivity::class.java).addFlagClearSingleTop()
            caller.startActivity(intent)
        }
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
    }

    override fun onDestroy() {
        super.onDestroy()
        if (this::timer.isInitialized) timer.cancel()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityLoanInAppCcLandingPageBinding.inflate(layoutInflater)
        setContentView(binding.root)
        injectDependency()
        getData()
        setContent()
    }

    override fun onSuccessGetOnboardingData(loanInAppOnboardingModel: LoanInAppOnboardingModel) {
        this.loanInAppOnboardingModel = loanInAppOnboardingModel
        handleSliderText(loanInAppOnboardingModel.sliderText)
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        landingPresenter.view = this
    }

    private fun getData() {
        landingPresenter.getOnboardingData(true)
    }

    private fun setContent() = with(binding) {
        tbActivityLoanOnAppCcLandingPage.setSupportActionBar(this@LoanInAppLandingPageActivity)
        setBackgroundTransparent()

        layActivityLoanOnAppCcLandingPageScheme.setOnSingleClickListener { showSchemeDialog() }
        bActivityLoanOnAppCcLandingPageAjukan.setOnSingleClickListener { gotoPengajuan() }
        bActivityLoanOnAppCcLandingPageRiwayat.setOnSingleClickListener { gotoRiwayat() }
    }

    private fun handleSliderText(sliderTexts: List<LoanInAppOnboardingModel.SliderText>) = with(binding) {
        vpActivityLoanOnAppCcLandingPage.adapter = loanInAppBenefitsPageAdapter
        vpActivityLoanOnAppCcLandingPage.offscreenPageLimit = loanInAppBenefitsPageAdapter.count
        diActivityLoanOnAppCcLandingPage.setViewPager(vpActivityLoanOnAppCcLandingPage)
        loanInAppBenefitsPageAdapter.setDatas(sliderTexts)
        handleSlideTextAuto()
    }

    private fun handleSlideTextAuto() = with(binding) {
        if (loanInAppOnboardingModel.sliderText.isNotEmpty()) {
            timer = Timer()
            timer.schedule(SliderTimer(), 3000, 5000)

            vpActivityLoanOnAppCcLandingPage.addOnPageChangeListener(object : OnPageChangeListener {
                override fun onPageScrolled(position: Int, positionOffset: Float, positionOffsetPixels: Int) = Unit

                override fun onPageScrollStateChanged(state: Int) = Unit

                override fun onPageSelected(position: Int) {
                    timer.cancel()
                    timer = Timer()
                    timer.schedule(SliderTimer(), 3000, 5000)
                }
            })
        } else {
            if (this@LoanInAppLandingPageActivity::timer.isInitialized) timer.cancel()
        }
    }

    private fun showSchemeDialog() {
        LoanInAppSchemeBottomFragment(
            mContext = this,
            schemeArrayList = loanInAppOnboardingModel.scheme
        ).show(supportFragmentManager, LOAN_IN_APP_DIALOG_TAG)
    }

    private fun gotoPengajuan() {
        LoanInAppListSofCcActivity.launchIntent(this, loanInAppOnboardingModel)
    }

    private fun gotoRiwayat() {
        LoanInAppRiwayatPengajuanActivity.launchIntent(this, loanInAppOnboardingModel, true)
    }

    inner class SliderTimer : TimerTask() {
        override fun run() = with(binding) {
            runOnUiThread {
                vpActivityLoanOnAppCcLandingPage.currentItem = (vpActivityLoanOnAppCcLandingPage.currentItem + 1) % loanInAppOnboardingModel.sliderText.size
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            Constant.REQ_PAYMENT -> {
                if (resultCode == RESULT_OK) {
                    getData()
                }
            }
        }
    }
}