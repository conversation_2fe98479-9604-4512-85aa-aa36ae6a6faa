package id.co.bri.brimo.adapters

import android.content.Context
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.databinding.ItemPromoFastMenuNewSkinBinding
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.DetailPromoResponse

class PromoFastMenuNewSkinAdapter(
    private var mContext: Context?,
    private var spesialUntukmuResponse: List<DetailPromoResponse>,
    private var clickItem: OnClickItem,
) : RecyclerView.Adapter<PromoFastMenuNewSkinAdapter.ViewHolder>() {

    inner class ViewHolder(val binding: ItemPromoFastMenuNewSkinBinding) : RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(
        parent: ViewGroup,
        viewType: Int
    ): PromoFastMenuNewSkinAdapter.ViewHolder {
        val binding = ItemPromoFastMenuNewSkinBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun onBindViewHolder(holder: PromoFastMenuNewSkinAdapter.ViewHolder, position: Int) {
        val data = spesialUntukmuResponse[position]

        GeneralHelper.loadIconTransaction(
            mContext,
            data.image,
            data.title,
            holder.binding.ivPromo,
            GeneralHelper.getImageId(mContext, "default_promo")
        )
        holder.binding.llPromo.setOnClickListener{
            clickItem.onClickPromoItem(data)
        }
    }

    override fun getItemCount(): Int {
//        return if (spesialUntukmuResponse.size < 3) spesialUntukmuResponse.size
//        else 3
        return spesialUntukmuResponse.size
    }

    interface OnClickItem {
        fun onClickPromoItem(promoResponse: DetailPromoResponse)
    }
}