package id.co.bri.brimo.adapters.profilerevamp

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.databinding.ItemListProfileInfoBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.models.apimodel.response.profilerevamp.ProfileInfoResp

class ListProfileInfoAdapter(
    private var bodyList: List<ProfileInfoResp>,
    private val listener: ItemProfileInfoClick
) : RecyclerView.Adapter<ListProfileInfoAdapter.ViewHolder>() {

    private val cardProfileInfoAdapter = CardProfileInfoAdapter()

    interface ItemProfileInfoClick {
        fun clickItem(id: Int, isSwitchChecked: Boolean)
    }

    inner class ViewHolder(val binding: ItemListProfileInfoBinding) :
        RecyclerView.ViewHolder(binding.root)

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        val binding = ItemListProfileInfoBinding
            .inflate(LayoutInflater.from(parent.context), parent, false)
        return ViewHolder(binding)
    }

    override fun getItemCount(): Int {
        return bodyList.size
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        val dataView = bodyList[position]
        holder.binding.apply {
            tvTitleInfo.text = dataView.title
            rvMenuInfo.apply {
                layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
                adapter = cardProfileInfoAdapter
            }

            cardProfileInfoAdapter.updateData(dataView.listInfoProfile)

            cardProfileInfoAdapter.clickActionListener { id, title ->
                listener.clickItem(id,false)
            }

            cardProfileInfoAdapter.clickSwitchListener() { id, isChecked, title ->
                listener.clickItem(id, isChecked)
            }

        }

    }

    fun isSwitchChecked(id: Int, isChecked: Boolean){
        bodyList.forEach {info->
            info.listInfoProfile.forEach {item->
                if (item.id.equals(id)){
                    item.status = isChecked
                }
            }
        }
        notifyDataSetChanged()
    }

    fun getStatusSwitch(): Boolean?{
        bodyList.forEach {info->
            info.listInfoProfile.forEach {item->
                if (item.id.equals(Constant.ProfileInfoRevamp.BIOMETRIC)){
                    return item.status
                }
            }
        }
        return null
    }
}