package id.co.bri.brimo.ui.activities;


import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.nfc.NfcAdapter;
import android.nfc.NfcManager;
import android.nfc.Tag;
import android.nfc.tech.IsoDep;
import android.os.Build;
import android.os.Bundle;
import android.provider.Settings;

import androidx.appcompat.app.AlertDialog;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.brizzi.ITapAktivasiBrizziPresenter;
import id.co.bri.brimo.contract.IView.brizzi.ITapAktivasiBrizziView;
import id.co.bri.brimo.databinding.ActivityTapBrizziAktivasiBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.AktivasiBrizziResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brizzi.Brizzi;


public class TapBrizziAktivasiActivity extends BaseActivity implements ITapAktivasiBrizziView {

    private ActivityTapBrizziAktivasiBinding binding;

    private static final String TAG = "TapBrizziAktivasiActivi";

    NfcManager nfcManager;
    private NfcAdapter adapter;
    private String RCNulisKartu;
    protected static String mJourneyType;
    protected static boolean mIsFromScan;
    protected static AktivasiBrizziResponse mAktivasi;
    protected static Integer mState;

    Brizzi mBrizzi;


    @Inject
    ITapAktivasiBrizziPresenter<ITapAktivasiBrizziView> presenter;

    public static void launchIntent(Activity caller, boolean isFromFastMenus, String journeyType, boolean isFromScan, Integer state) {
        Intent intent = new Intent(caller, TapBrizziAktivasiActivity.class);
        mJourneyType = journeyType;
        isFromFastMenu = isFromFastMenus;
        mIsFromScan = isFromScan;
        mState = state;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTapBrizziAktivasiBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.tbBriva.toolbar, "BRIZZI");

        injectDependency();
//        Glide.with(getApplicationContext()).load(R.drawable.cek_brizzi).into(imageView);
        nfcManager = (NfcManager) getApplicationContext().getSystemService(Context.NFC_SERVICE);
        adapter = nfcManager.getDefaultAdapter();

        if (adapter == null) {
            adapter = NfcAdapter.getDefaultAdapter(this);
            if (adapter == null) {
                showSnackbarErrorMessage(GeneralHelper.getString(R.string.brizzi_device_tidak_support_nfc), ALERT_ERROR, this, false);
            } else {
                if (!adapter.isEnabled()) {
                    AlertDialog.Builder alertbox = new AlertDialog.Builder(this);
                    alertbox.setMessage(GeneralHelper.getString(R.string.brizzi_aktifkan_nfc_lanjut));
                    alertbox.setPositiveButton(GeneralHelper.getString(R.string.aktifkan2), new DialogInterface.OnClickListener() {
                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            {
                                Intent intent = new Intent(Settings.ACTION_NFC_SETTINGS);
                                startActivity(intent);
                            }
                        }
                    });
                    alertbox.setNegativeButton(GeneralHelper.getString(R.string.batal), new DialogInterface.OnClickListener() {

                        @Override
                        public void onClick(DialogInterface dialog, int which) {
                            finish();
                        }
                    });
                    alertbox.show();
                } else {
                    onNewIntent(getIntent());
                }
            }

        }
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);
        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
//            if (mState == 0) {
//                presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_fm_check_top_up_brizzi));
//                presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_fm_aktivasi_top_up_brizzi));
//                presenter.setValidateUrl(GeneralHelper.getString(R.string.url_fm_validate_aktivasi_brizzi));
//
//            }
//            else if (mState ==1 || mState==4){
//                presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_check_top_up_brizzi));
//                presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_aktivasi_top_up_brizzi));
//                presenter.setValidateUrl(GeneralHelper.getString(R.string.url_validate_deposit_brizzi));
//            }
//            else {
//                presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_non_user_check_top_up_brizzi));
//                presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_non_user_aktivasi_top_up_brizzi));
//                presenter.setValidateUrl(GeneralHelper.getString(R.string.url_non_user_check_validate_brizzi));
//                }

            if (isFromFastMenu) {
                presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_non_user_check_top_up_brizzi));
                presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_non_user_aktivasi_top_up_brizzi));
                presenter.setValidateUrl(GeneralHelper.getString(R.string.url_non_user_check_validate_brizzi));

            } else {
                presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_check_top_up_brizzi));
                presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_aktivasi_top_up_brizzi));
                presenter.setValidateUrl(GeneralHelper.getString(R.string.url_validate_deposit_brizzi));
            }
        }


    }

    @Override
    protected void onResume() {
        super.onResume();

        if (adapter != null) {
            Intent intent = new Intent(this, TapBrizziAktivasiActivity.class);
            intent.setFlags(Intent.FLAG_RECEIVER_REPLACE_PENDING);

            //perbaikan pendingIntent android 31
            int currentFlags = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ? PendingIntent.FLAG_MUTABLE | PendingIntent.FLAG_UPDATE_CURRENT : 0;
            PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, currentFlags);


            IntentFilter[] filters = new IntentFilter[]{};
            adapter.enableForegroundDispatch(this, pendingIntent, filters, null);
        }
    }


    @Override
    protected void onPause() {
        super.onPause();
        adapter.disableForegroundDispatch(this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        try {
            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            IsoDep brizziTag = IsoDep.get(tag);
            mBrizzi = new Brizzi(brizziTag);
            presenter.initUpdateBalancePres(mBrizzi, isFromFastMenu);
        } catch (Exception e) {
            showSnackbarErrorMessage(GeneralHelper.getString(R.string.brizzi_kartu_gagal_terbaca), ALERT_ERROR, this, false);
        }

    }

    @Override
    public void showErrorMessage(String message) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }

    /**
     * Method berhasil setelah checkbalance lanjut masuk ke tahap commit
     */
    @Override
    public void onSuccesGetCommitAktivasi(AktivasiBrizziResponse aktivasiBrizziResponse) {
        mAktivasi = aktivasiBrizziResponse;
        presenter.commitContinuePres(aktivasiBrizziResponse, mBrizzi, isFromFastMenu);
    }

    /**
     * Method berhasil setelah commit lanjut ke receipt
     */
    @Override
    public void onSuccesGetValidateAtktivasi() {
        RecieptBrizziActivty.launchIntent(this, mAktivasi, mJourneyType);
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException01(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException01OnValidate(String messagePayment, String messageValidate) {
        Intent returnIntent = new Intent();
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, messagePayment);
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_VALIDATE, messageValidate);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onExceptionGagalDefault(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }


    @Override
    protected void onDestroy() {
        presenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        presenter.stop();
        super.onBackPressed();
    }


}