package id.co.bri.brimo.ui.fragments.dashboardrevamp

import android.annotation.SuppressLint
import android.app.Activity
import android.app.Activity.RESULT_CANCELED
import android.app.Activity.RESULT_OK
import android.content.Context
import android.content.Intent
import android.content.res.ColorStateList
import android.graphics.Color
import android.graphics.Typeface
import android.os.Bundle
import android.os.CountDownTimer
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.animation.AnimationUtils
import androidx.activity.result.ActivityResultLauncher
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.dashboard.KategoriMenuAdapter
import id.co.bri.brimo.adapters.dashboard.MenuDefaultAdapter
import id.co.bri.brimo.adapters.spesialuntukmurevamp.SpesialUntukmuAdapter
import id.co.bri.brimo.contract.IPresenter.revamp.dashboard.IDashboardRevampPresenter
import id.co.bri.brimo.contract.IView.revamp.dashboard.IDashboardRevampView
import id.co.bri.brimo.databinding.FragmentDashboardIBRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.Constant.LIST_TYPE_GAGAL
import id.co.bri.brimo.domain.config.MenuConfig
import id.co.bri.brimo.domain.helpers.AnimationExpand
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseListener
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence
import id.co.bri.brimo.domain.helpers.calendar.makeGone
import id.co.bri.brimo.domain.helpers.calendar.makeVisible
import id.co.bri.brimo.models.apimodel.response.*
import id.co.bri.brimo.models.apimodel.response.lifestyle.DashboardLifestyleMenuResponse
import id.co.bri.brimo.models.apimodel.response.nfcpayment.NfcPayloadResponse
import id.co.bri.brimo.models.apimodel.response.splitbill.viewentity.BillEntity
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuKategori
import id.co.bri.brimo.nfcpayment.NFCPayment
import id.co.bri.brimo.nfcpayment.NFCSettingsContract
import id.co.bri.brimo.ui.activities.AllPromoActivity
import id.co.bri.brimo.ui.activities.CatatanKeuanganActivity
import id.co.bri.brimo.ui.activities.DashboardIBActivity
import id.co.bri.brimo.ui.activities.DashboardIBActivity.s
import id.co.bri.brimo.ui.activities.DetailPromoActivity
import id.co.bri.brimo.ui.activities.DetailPusatBantuanActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.NotificationListActivity
import id.co.bri.brimo.ui.activities.RekeningActivity
import id.co.bri.brimo.ui.activities.SyaratKetentuanActivity
import id.co.bri.brimo.ui.activities.dashboardInvestasi.DashboardInvestasiActivity
import id.co.bri.brimo.ui.activities.dashboardrevamp.MenuListGeneralActivity
import id.co.bri.brimo.ui.activities.halamancarirevamp.HalamanCariRevampActivity
import id.co.bri.brimo.ui.activities.lifestyle.DashboardLifestyleActivity
import id.co.bri.brimo.ui.activities.nfcqrtap.NfcPaymentActivity
import id.co.bri.brimo.ui.activities.splitbill.SplitBillHistoryActivity
import id.co.bri.brimo.ui.activities.splitbill.SplitBillOnboardingActivity
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity
import id.co.bri.brimo.ui.activities.topuprevamp.TopUpRevampActivity
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogGeneral
import id.co.bri.brimo.ui.customviews.bottomsheetdialog.BottomSheetDialogType
import id.co.bri.brimo.ui.customviews.datepicker.CustomLinearLayoutManager
import id.co.bri.brimo.ui.customviews.dialog.DialogPromo
import id.co.bri.brimo.ui.customviews.dialog.DialogTutorial
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.PinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import id.co.bri.brimo.ui.fragments.login.BottomSheet2ButtonLogin
import id.co.bri.brimo.util.TimePeriod
import id.co.bri.brimo.util.toTimePeriod
import java.util.Calendar
import javax.inject.Inject

/**
 * A simple [Fragment] subclass.
 * Use the [DashboardRevampFragment.newInstance] factory method to
 * create an instance of this fragment.
 */
class DashboardRevampFragment :
    BaseFragment(),
    BubbleShowCaseListener,
    IDashboardRevampView,
    OnRefreshListener,
    KategoriMenuAdapter.AdapterCallback,
    SpesialUntukmuAdapter.ClicItem,
    View.OnClickListener,
    DialogTutorial.DialogTutorialCallback,
    BottomSheet2ButtonLogin.DialogDefaultListener,
    PinFragment.SendPin, DashboardIBActivity.OnWindowFocusChangeListener {


    @Inject
    lateinit var fragmentPresnter: IDashboardRevampPresenter<IDashboardRevampView>

    // TODO: Rename and change types of parameters
    private var _binding: FragmentDashboardIBRevampBinding? = null
    private val viewBinding get() = _binding!!
    private var isFromIsiSaldoBukrek = false
    private var isBottomBiometric = false
    private var isBubbleNewOnboard = false
    private var isSaldoShow = false
    private var isPfmShow = false
    private var isHold = false
    private var isExpand = false
    private val isFormNonIB = false
//    hide nfc menu
    private var isNfcActive = false
    private var skeletonScreenSaved: SkeletonScreen? = null
    private var currentCurrency: String? = null
    private var mSaldo: String? = null
    private var countDownTimer: CountDownTimer? = null
    private val SECOND = 1000
    private var spesialUntukmuAdapter: SpesialUntukmuAdapter? = null
    private var lMSpesialUntukmu: CustomLinearLayoutManager? = null
    private val detailPromoResponses = ArrayList<DetailPromoResponse>()

    private lateinit var bubblepusatBantuan: BubbleShowCaseBuilder
    private lateinit var bubbleShorcutMenu: BubbleShowCaseBuilder
    private lateinit var bubbleCariFitur: BubbleShowCaseBuilder
    private lateinit var bubbleKetegoriFitur: BubbleShowCaseBuilder
    private lateinit var bubblePFM: BubbleShowCaseBuilder
    private lateinit var bubbleShowCaseSequence: BubbleShowCaseSequence
    private lateinit var bubbleAlertSaldoHold: BubbleShowCaseBuilder
    private lateinit var bubbleNewOnboarding: BubbleShowCaseBuilder

//    hide nfc menu
    private var nfcPayment: NFCPayment? = null
    private lateinit var nfcSettingsLauncher: ActivityResultLauncher<Unit>

    companion object {
        /**
         * Use this factory method to create a new instance of
         * this fragment using the provided parameters.
         *.
         */
        // TODO: Rename and change types and number of parameters
        @JvmStatic
        fun newInstance(sMsg: String?): DashboardRevampFragment? {
            val fragment = DashboardRevampFragment()
            val args = Bundle()
            args.putString(MESSAGE_SUCCESS, sMsg)
            fragment.arguments = args
            return fragment
        }

        private const val TAG = "DashboardRevampFragment"
        private const val MESSAGE_SUCCESS = "message"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        // Inflate the layout for this fragment
        _binding = FragmentDashboardIBRevampBinding.inflate(inflater, container, false)
        return viewBinding.root
    }

//    hide nfc menu
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        nfcSettingsLauncher =
            registerForActivityResult(NFCSettingsContract()) { isActive: Boolean ->
                isNfcActive = isActive
                updateUI()
            }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupTransparentStatusBar()
        //set up UI
        setupView()
        //set up bubble
        settingBubbleTutorial(view)
        settingBubbleAlertSaldoHold(view)
        settingBubbleNewOnboarding(view)

        //inject presenter
        injectDependency()

        //klo ada data tampilkan snackbar sukses
        handleSnackBarSuccess(view)

        //cek apakah fresh install
        handleAppStart()
        showSnackbarChange(view)
    }

    private fun showSnackbarChange(rootView: View) {
        if (requireActivity().intent.getBooleanExtra(Constant.IS_CHANGE_LANGUAGE, false)) {
            GeneralHelperNewSkin.showSnackBarGreen(
                rootView.findViewById(R.id.contentDashboard),
                GeneralHelper.getString(R.string.txt_language_change_successfully)
            )
        }
        requireActivity().intent.removeExtra(Constant.IS_CHANGE_LANGUAGE)
    }


    private fun handleSnackBarSuccess(rootView: View) {
        if (arguments != null) {
            if (!requireArguments().getString("message")!!.isEmpty()) {
                GeneralHelper.showSnackBarWithTransparentStatusBar(
                    rootView.findViewById(R.id.contentDashboard),
                    requireArguments().getString("message"),
                    R.color.success80, R.drawable.ic_check_circle
                )
            }
        }
    }

    private fun handleAppStart() {
        try {
            when (GeneralHelper.checkAppStart()) {
                GeneralHelper.AppStart.NORMAL -> {
                    // Do Nothing
                    fragmentPresnter.getLatestBlastNotif()
                }

                GeneralHelper.AppStart.FIRST_TIME -> {
                    fragmentPresnter.updateFirstTimePrefrencesLanguage()
                    bubbleShowCaseSequence = s
                    bubbleShowCaseSequence.show()
                    if (java.lang.Boolean.TRUE == fragmentPresnter.newBubbleOnboard) {
                        isBubbleNewOnboard = true
                        isFromIsiSaldoBukrek = true
                    }
                }

                //jika update versi show bottomsheet enroll biometric
                GeneralHelper.AppStart.FIRST_TIME_VERSION -> {
                    if (java.lang.Boolean.TRUE == GeneralHelper.checkBiometricSupport()) {
                        if (java.lang.Boolean.FALSE == isBottomBiometric &&
                            java.lang.Boolean.FALSE == fragmentPresnter.statusUpdateBio
                        ) {
                            openSettingBio()
                            isBottomBiometric = true
                        }
                    }
                }

                else -> {
                    // Do Nothing
                }
            }
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) Log.d("DashboardRevamp", "onViewCreated: " + e.message)
        }
    }

    private fun settingBubbleTutorial(view: View) {
        bubblepusatBantuan = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.pusat_bantuan_bubble_title))
            .description(GeneralHelper.getString(R.string.pusat_bantuan_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(_binding!!.llPusatBantuan)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(this)

        bubbleShorcutMenu = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.shortcut_menu_bubble_title))
            .description(GeneralHelper.getString(R.string.shortcut_menu_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(_binding!!.rvMenu)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(this)

        bubbleCariFitur = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.cari_fitur_bubble_title))
            .description(GeneralHelper.getString(R.string.cari_fitur_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(_binding!!.llCariFitur)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(this)

        bubbleKetegoriFitur = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.menu_baru_bubble_title))
            .description(GeneralHelper.getString(R.string.menu_baru_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(_binding!!.llExpandableLayoutContainer)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(this)

        bubblePFM = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.pfm_bubble_title))
            .description(GeneralHelper.getString(R.string.pfm_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(_binding!!.llContainerTitlePfm)
            .buttonTitle(GeneralHelper.getString(R.string.finish))
            .enableLewati(true)
            .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
            .enableViewButtonSkip(false)
            .listener(object : BubbleShowCaseListener {
                override fun onTargetClick(bubbleShowCase: BubbleShowCase) {
                    if (isBubbleNewOnboard) {
                        fragmentPresnter?.closeBubbleOnboarding()
                    } else {
                        if (java.lang.Boolean.FALSE == isBubbleNewOnboard &&
                            java.lang.Boolean.FALSE == fragmentPresnter.bottomBiometric &&
                            java.lang.Boolean.FALSE == fragmentPresnter.newBubbleOnboard &&
                            java.lang.Boolean.TRUE == GeneralHelper.checkBiometricSupport()
                        ) {
                            openSettingBio()
                        }
                    }
                }

                override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
                    if (isBubbleNewOnboard) {
                        fragmentPresnter?.closeBubbleOnboarding()
                    } else {
                        if (java.lang.Boolean.FALSE == isBubbleNewOnboard &&
                            java.lang.Boolean.FALSE == fragmentPresnter.bottomBiometric &&
                            java.lang.Boolean.FALSE == fragmentPresnter.newBubbleOnboard &&
                            java.lang.Boolean.TRUE == GeneralHelper.checkBiometricSupport()
                        ) {
                            openSettingBio()
                        }
                    }
                }

                override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {
                    // do nothing
                }

                override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {
                    // do nothing
                }

                override fun onSkipActionClick(bubbleShowCase: BubbleShowCase) {
                    // do nothing
                }

            })

        val listBubble: MutableList<BubbleShowCaseBuilder> = ArrayList()
        listBubble.add(bubblepusatBantuan)
        listBubble.add(bubbleShorcutMenu)
        listBubble.add(bubbleCariFitur)
        listBubble.add(bubbleKetegoriFitur)
        listBubble.add(bubblePFM)
        s.addShowCases(listBubble)

    }

    private fun settingBubbleAlertSaldoHold(view: View) {
        bubbleAlertSaldoHold = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.saldo_tertahan))
            .description(GeneralHelper.getString(R.string.desc_saldo_tertahan))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(view.findViewById<View>(R.id.iv_alert_saldo))
            .buttonTitle(GeneralHelper.getString(R.string.lihat_detail_saldo))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .listener(object : BubbleShowCaseListener {
                override fun onSkipActionClick(bubbleShowCase: BubbleShowCase) {
                    //Do Nothing
                }

                override fun onTargetClick(bubbleShowCase: BubbleShowCase) {
                    intentInfoBantuanSaldoHold()
                }

                override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
                    intentInfoBantuanSaldoHold()
                }

                override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {
                    // do nothing
                }

                override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {
                    // do nothing
                }
            })
    }

    private fun settingBubbleNewOnboarding(view: View) {
        bubbleNewOnboarding = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.isi_saldo))
            .description(GeneralHelper.getString(R.string.desc_isi_saldo))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(view.findViewById<View>(R.id.ll_ke_list_rekening))
            .buttonTitle(GeneralHelper.getString(R.string.cara_isi_saldo))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .listener(object : BubbleShowCaseListener {
                override fun onSkipActionClick(bubbleShowCase: BubbleShowCase) {
                    //Do Nothing
                }

                override fun onTargetClick(bubbleShowCase: BubbleShowCase) {
                    intentInfoBantuanTopUp()
                }

                override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
                    intentInfoBantuanTopUp()
                }

                override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {
                    // do nothing
                }

                override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {
                    // do nothing
                }
            })
    }

    private fun intentInfoBantuanSaldoHold() {
        fragmentPresnter.getInfoSaldoHold()
    }

    private fun intentInfoBantuanTopUp() {
        fragmentPresnter?.getTermCondition()
    }

    override fun onDestroy() {
        fragmentPresnter?.stop()
        super.onDestroy()
    }


    @SuppressLint("SetTextI18n")
    private fun setupView() {

        viewBinding.lyMore.setOnClickListener {
            toggleExpandMenu()
        }

        viewBinding.notifLonceng.setOnClickListener {
            NotificationListActivity.launchIntent(activity)
        }

        viewBinding.llPusatBantuan.setOnClickListener {
            SelfServiceActivity.launchIntent(activity)
        }

        viewBinding.rlAllAccount.setOnClickListener {
            RekeningActivity.launchIntent(activity)
        }

        viewBinding.llPfmSummary.btnLihatPfm.setOnClickListener {
            CatatanKeuanganActivity.launchIntent(
                activity,
                false,
                false,
                null,
                false,
                MenuConfig.PFMMenuFragment.PFM_OUTCOME
            )
        }

        viewBinding.llPfmSummary.llIncome.setOnClickListener {
            CatatanKeuanganActivity.launchIntent(
                activity,
                false,
                false,
                null,
                false,
                MenuConfig.PFMMenuFragment.PFM_INCOME
            )
        }

        viewBinding.llPfmSummary.llOutcome.setOnClickListener {
            CatatanKeuanganActivity.launchIntent(
                activity,
                false,
                false,
                null,
                false,
                MenuConfig.PFMMenuFragment.PFM_OUTCOME
            )
        }

        viewBinding.llPfmSummary.llReport.setOnClickListener {
            CatatanKeuanganActivity.launchIntent(
                activity,
                false,
                false,
                null,
                false,
                MenuConfig.PFMMenuFragment.PFM_REPORT
            )
        }

        viewBinding.ivSaldoEye.setOnClickListener {
            toggleSaldoDot()
            fragmentPresnter?.saveSaldoHideFlag(isSaldoShow)
        }

        viewBinding.rlMenu.setOnClickListener {
            HalamanCariRevampActivity.launchIntent(context as Activity)
        }

        viewBinding.btnLihatPromo.setOnClickListener(this)

//    hide nfc menu
        context?.let {
            nfcPayment = NFCPayment.getInstance(it)
        }
        checkNfcStatus()

        viewBinding.llNfcPayment?.setOnClickListener {
            if (isNfcActive) {
                showInputPin()
            } else {
                showBottomSheetActiveInActiveNfc()
            }
        }

        setMenu()
        setView()
        initiateSavedAdapter()
    }

//    hide nfc menu
    private fun showBottomSheetActiveInActiveNfc() {
        activity?.supportFragmentManager?.let {
            val bottomSheetDialogGeneral = BottomSheetDialogGeneral.Builder()
                .setType(BottomSheetDialogType.SINGLE_BUTTON)
                .setImageResId(R.drawable.ic_activate_nfc)
                .setTitle(GeneralHelper.getString(R.string.txt_masalah_pada_nfc))
                .setDescription(GeneralHelper.getString(R.string.desc_masalah_pada_nfc))
                .setPositiveText(GeneralHelper.getString(R.string.txt_aktifkan_nfc))
                .setConfirmButtonVerticalDrawable(R.drawable.button_primary_bg)
                .setConfirmButtonVerticalStyle(R.style.CustomButtonPrimaryStyle, Typeface.BOLD)
                .setConfirmButtonVerticalTextColor(R.color.whiteColor)
                .setOnPositiveButtonClickListener { nfcSettingsLauncher.launch(Unit) }
                .setCancelable(true)
                .setOnCanceledOnTouchOutside(true)
                .build()

            if (!it.isStateSaved) {
                bottomSheetDialogGeneral.show(it, bottomSheetDialogGeneral.tag)
            }
        }
    }

    private fun checkNfcStatus() {
        if (nfcPayment?.isNfcAvailable() == true) {
            viewBinding.llNfcPayment.makeVisible()
            isNfcActive = nfcPayment?.isNfcEnabled() ?: false
            updateUI()
        } else {
            viewBinding.llNfcPayment.makeGone()
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        if (fragmentPresnter != null) {
            fragmentPresnter.view = this

            //update url service
            fragmentPresnter.setUrlSaldo(GeneralHelper.getString(R.string.url_v2_saldo_dashboard))
            fragmentPresnter.setUrlPromoFeatured(GeneralHelper.getString(R.string.url_promo_featured))
            fragmentPresnter.setDetailItemPromoUrl(GeneralHelper.getString(R.string.url_detail_promo))
            fragmentPresnter.setUrlLatestBlastNotif(GeneralHelper.getString(R.string.url_latestblast_pushnotif))
            fragmentPresnter.setUrlNotificationUnreads(GeneralHelper.getString(R.string.url_notification_unreads))
            fragmentPresnter.setUrlReadNotif(GeneralHelper.getString(R.string.url_notification_read_fast_menu))
            fragmentPresnter.setUrlSafetyMode(GeneralHelper.getString(R.string.url_safety_mode))
            fragmentPresnter.setUrlPusatBantuanSafety(GeneralHelper.getString(R.string.url_safety_pusat_bantuan))
            fragmentPresnter.setUrlPFMSummary(GeneralHelper.getString(R.string.url_v1_summary_dashboard_pfm))
            fragmentPresnter.setUrlUpdateToken(GeneralHelper.getString(R.string.url_v1_update_token))
            fragmentPresnter.setUrlInfoSaldoHold(GeneralHelper.getString(R.string.url_qna_detail))
            fragmentPresnter.setUrlDashboardLifestyleMenu(GeneralHelper.getString(R.string.url_dashboard_lifestyle_menu))
            fragmentPresnter.setUrlGetPayload(GeneralHelper.getString(R.string.url_generate_payload))
            fragmentPresnter.setUrlPrefrences(GeneralHelper.getString(R.string.url_prefrences_bilingual))
            fragmentPresnter.setUrlSplitBillList(GeneralHelper.getString(R.string.url_splitbill_get_history_list))

            fragmentPresnter.start()
            fragmentPresnter.getSaldoUtama()
            fragmentPresnter.getNotificationUnreads()
            fragmentPresnter.getTimerSafetyMode()
            fragmentPresnter.getNickname()
            fragmentPresnter.getPFMSummary()
            fragmentPresnter.getPromoFeatured()
            fragmentPresnter.updateTokenFirebase()

            isSaldoShow = fragmentPresnter.getSaldoHideFlag()
            isPfmShow = fragmentPresnter.getPfmHideFlag()
        }
    }

    private fun setView() {
        viewBinding.lyMore.setOnClickListener {
            toggleExpandMenu()
        }

        viewBinding.notifLonceng.setOnClickListener {
            NotificationListActivity.launchIntent(activity)
        }

        viewBinding.llPusatBantuan.setOnClickListener {
            SelfServiceActivity.launchIntent(activity)
        }

        viewBinding.rlAllAccount.setOnClickListener {
            RekeningActivity.launchIntent(activity)
        }

        viewBinding.ivSaldoEye.setOnClickListener {
            toggleSaldoDot()
            fragmentPresnter?.let {
                it.saveSaldoHideFlag(isSaldoShow)
            }
        }

        viewBinding.tbLihatPfm.setOnClickListener {
            toggleShowPfm()
            //updatee pref flag
            fragmentPresnter?.let {
                it.savePfmHideFlag(isPfmShow)
            }
        }

        viewBinding.ivAlertSaldo.setOnClickListener {
            bubbleAlertSaldoHold.show()
        }
    }

    private fun initiateSavedAdapter() {
        lMSpesialUntukmu = CustomLinearLayoutManager(context, LinearLayoutManager.HORIZONTAL, false)
        _binding?.rvListPromo?.layoutManager = lMSpesialUntukmu
        spesialUntukmuAdapter = SpesialUntukmuAdapter(context, detailPromoResponses, this, 0)
        skeletonScreenSaved = Skeleton.bind(_binding?.rvListPromo)
            .adapter(spesialUntukmuAdapter)
            .shimmer(true)
            .angle(20)
            .frozen(false)
            .duration(1200)
            .color(R.color.white)
            .count(5)
            .load(R.layout.item_skeleton_spesial_untukmu)
            .show()
    }

    private fun toggleSaldoDot() {
        if (!isSaldoShow) {
            showSaldo()
            isSaldoShow = true
        } else {
            hideSaldo()
            isSaldoShow = false
        }
    }

    private fun toggleExpandMenu() {

        if (!isExpand) {
            AnimationExpand.expand(viewBinding.rvMenuMore)
            viewBinding.ivMore.animate().rotation(180f).start()
            viewBinding.tvMore.text = GeneralHelper.getString(R.string.text_kategori_tutup)
            isExpand = true
        } else {
            AnimationExpand.collapse(viewBinding.rvMenuMore)
            viewBinding.ivMore.animate().rotation(0f).start()
            viewBinding.tvMore.text = GeneralHelper.getString(R.string.text_kategori_lainnya)
            isExpand = false
        }
    }

    private fun toggleShowPfm() {
        if (!isPfmShow) {
            showPfm()

            isPfmShow = true
        } else {
            hidePfm()

            isPfmShow = false
        }
    }


    private fun showPullRefresh(isRefreshed: Boolean) {
        if (viewBinding.srlDashboardIb != null) {
            viewBinding.srlDashboardIb.setRefreshing(isRefreshed)
        }
    }

    private fun setSaldoString(saldo: String?) {
        viewBinding.totalSaldoIb.setText(saldo)
    }

    /**
     * update Currency dan total Saldo
     *
     * @param currency
     * @param total
     * @param holdSaldo
     */
    protected fun updateSaldo(currency: String, total: String?) {
        currentCurrency = currency

        if (isSaldoShow) {

            //apakah saldo negatif
            if (total != null) {
                if (total.equals("0.0", ignoreCase = true)) {
                    setSaldoString("-")
                } else {
                    setSaldoString("$currency$total")
                }
            } else {
                setSaldoString("-")
            }
        } else {
            setSaldoString("$currency$total")
        }
        // saldo tertahan
        cekSaldoTertahan()
    }

    private fun cekSaldoTertahan() {
        if (isHold) {
            viewBinding.ivAlertSaldo.makeVisible()
        } else {
            viewBinding.ivAlertSaldo.makeGone()
        }
    }

    /**
     * Callback ketika servis saldo tidak mengembalikan nominal
     */
    override fun onSaldoNoResp() {
        showPullRefresh(false)
        showProgressSaldo(false)
        updateSaldo(Constant.CURRENCY, "0.0")
    }

    override fun onSuccessNotifUnread(notifUnreadsResponse: NotifUnreadsResponse?) {
        val total = notifUnreadsResponse?.totalUnreads?.total?.toIntOrNull() ?: 0

        if (total > 0) {
            viewBinding.layoutNotifUnread.makeVisible()
            viewBinding.tvNotifUnread.text = if (total > 9) "9+" else total.toString()
        } else {
            viewBinding.layoutNotifUnread.makeGone()
        }
    }

    override fun onSuccessGetTimerSafety(safetyModeResponse: SafetyModeResponse?) {
        if (safetyModeResponse != null) {
            if (safetyModeResponse.ttl.toInt() == 0) {
                setSafetyModeGone(true)
            } else {
                setSafetyModeGone(false)
                viewBinding.safetyMode.tvDescSafety.text = safetyModeResponse.getDesc()
                viewBinding.safetyMode.imgTanyaSafety.makeVisible()
                viewBinding.safetyMode.imgTanyaSafety.setOnClickListener {
                    fragmentPresnter.getPusatBantuanSafety(Constant.ID_PUSAT_BANTUAN_SAFETY_MODE)
                }

                //proses timer
                activity?.let { setTextTimer(safetyModeResponse.ttl.toInt()) }
            }

            handleAlertMaintenance(safetyModeResponse)
        }
    }

    private fun handleAlertMaintenance(response: SafetyModeResponse) {
        val alert = response.alertMaintenance
        val hasId = fragmentPresnter.alertMaintenanceTokenId
        val alertTokenId = alert.tokenId

        val hasAlert = alertTokenId == null || hasId.equals(alertTokenId)

        if (!hasAlert) {
            OpenBottomSheetGeneralFragment.showDialogInformation(
                fragmentManager = parentFragmentManager,
                imgPath = "",
                imgName = "illustration_maintenance",
                titleTxt = response.alertMaintenance.title,
                subTitleTxt = response.alertMaintenance.subTitle,
                btnFirstFunction = { saveAlertMaintenanceTokenId(alert.tokenId) },
                isClickableOutside = false,
                firstBtnTxt = getString(R.string.mengerti)
            )
        }
    }

    private fun saveAlertMaintenanceTokenId(id: String) {
        fragmentPresnter.saveAlertMaintenanceTokenId(id)
    }

    override fun onSuccessGetPusatBantuan(topicQuestionResponse: QuestionResponse) {
        DetailPusatBantuanActivity.launchIntent(
            activity,
            topicQuestionResponse,
            topicQuestionResponse.topicName
        )
    }

    override fun onSuccessGetIncome(income: String?) {
        income?.let { viewBinding.llPfmSummary.tvIncomeValue.setText(it) }
    }

    override fun onSuccessGetOutcome(outcome: String?) {
        outcome?.let { viewBinding.llPfmSummary.tvOutcomeValue.setText(it) }
    }

    override fun onSuccessGetDatePfm(date: String?) {
        date?.let { viewBinding.tvIncomeDate.setText(it) }
    }

    override fun checkPfmFlag() {
        if (isPfmShow) {
            showPfm()
        } else {
            hidePfm()
        }
    }

    /**
     * to general activity
     */
    override fun onSuccessGetMenubyId(
        menuDashboardList: MutableList<MenuDashboard>?,
        title: String,
        idKategori: Int
    ) {
        when (idKategori) {
            MenuConfig.MenuKategoriId.TOP_UP -> TopUpRevampActivity.launchIntent(
                activity,
                title,
                menuDashboardList,
                idKategori
            )

            MenuConfig.MenuKategoriId.INVESTASI -> DashboardInvestasiActivity.launchIntent(
                requireActivity()
            )

            MenuConfig.MenuKategoriId.LIFE_STYLE -> fragmentPresnter.getDataDashboardLifestyleMenu()

            MenuConfig.MenuKategoriId.SPLITBILL -> fragmentPresnter.getSplitBillList()

            else -> {
                MenuListGeneralActivity.launchIntent(activity, title, menuDashboardList, idKategori)
            }
        }
    }

    override fun onSuccessGetPromoFeatured(promoResponse: PromoResponse) {
        detailPromoResponses.clear()
        detailPromoResponses.addAll(promoResponse.promo)
        spesialUntukmuAdapter?.setSpecial(detailPromoResponses)
        spesialUntukmuAdapter?.notifyDataSetChanged()
    }

    override fun onSuccessGetDetailItem(promoResponse: PromoResponse?) {
        DetailPromoActivity.launchIntent(activity, promoResponse?.promo, isFormNonIB, false, false)
    }

    override fun successGetPromoNotification(response: NotifikasiBlastResponse) {
        val notification = response.notifikasiModel
        fragmentPresnter.getReadNotifFastMenu(notification.blastId.toString())

        if (!response.notifikasiModel.imagePopup.isNullOrEmpty()) {
            val promo = DialogPromo(requireContext(), response.notifikasiModel)
            val ft = requireActivity().supportFragmentManager.beginTransaction()
            ft.add(promo, null)
            ft.commitAllowingStateLoss()
        }

        if (response.notifikasiModel.promoId.equals("0")) {
            fragmentPresnter.getNotificationUnreads()
        }
    }

    override fun onSuccessInfoSaldoHold(questionResponse: QuestionResponse?) {
        DetailPusatBantuanActivity.launchIntent(
            activity,
            questionResponse,
            GeneralHelper.getString(R.string.toolbar_pusat_bantuan)
        )
    }

    override fun successTermConditionNewOnboarding(termCondition: String?) {
        SyaratKetentuanActivity.launchIntentOnboarding(
            activity,
            termCondition,
            GeneralHelper.getString(R.string.toolbar_pusat_bantuan),
            GeneralHelper.getString(R.string.kembali_ke_home),
            true
        )
    }

    override fun showBubbleNewOnboarding() {
        bubbleNewOnboarding.show()
        isBubbleNewOnboard = false
    }

    override fun onSuccessDashboardLifestyleMenu(dashboardLifestyleMenuResponse: DashboardLifestyleMenuResponse) {
        if (dashboardLifestyleMenuResponse.menuDataView.isNullOrEmpty()) {
            GeneralHelper.showSnackBarWithTransparentStatusBar(
                (activity as DashboardIBActivity?)?.findViewById(R.id.contentDashboard),
                GeneralHelper.getString(R.string.maaf_sedang_terjadi_kendala_di_sistem_kami),
                R.color.error80, R.drawable.ic_cancel_danger_18dp
            )
        } else {
            DashboardLifestyleActivity.launchIntent(
                requireActivity(),
                dashboardLifestyleMenuResponse
            )
        }
    }

    override fun onMenuLifestyleEmpty() {
        GeneralHelper.showSnackBarWithTransparentStatusBar(
            requireActivity().findViewById(R.id.contentDashboard),
            GeneralHelper.getString(R.string.maaf_sedang_terjadi_kendala_di_sistem_kami),
            R.color.error80, R.drawable.ic_cancel_danger_18dp
        )
    }

    override fun onSuccessGetNfcPayload(response: NfcPayloadResponse) {
        context?.let {
            NfcPaymentActivity.launchIntent(it as DashboardIBActivity, response, false)
        }
    }

    override fun onSuccessGetSplitBillList(bills: MutableList<BillEntity>) {
        if (!bills.isNullOrEmpty()) {
            SplitBillHistoryActivity.launchIntent(requireActivity(), bills)
        } else {
            if (fragmentPresnter.isBillCreated) {
                SplitBillHistoryActivity.launchIntent(requireActivity(), bills)
            } else {
                SplitBillOnboardingActivity.launchIntent(requireActivity())
            }
        }
    }

    override fun onSuccessGetPfmSummary(isPositive: Boolean, budget: String?) {

        activity?.let {
            if (!isPositive) {
                viewBinding.llPfmSummary.tvBudget.setTextColor(
                    GeneralHelper.getColor(it, R.color.dangerColor)
                );
            } else {
                viewBinding.llPfmSummary.tvBudget.setTextColor(
                    GeneralHelper.getColor(it, R.color.successColor)
                );
            }
        }

        budget?.let { viewBinding.llPfmSummary.tvBudget.setText(it) };
    }

    override fun showProgressSaldo(isShowProgress: Boolean) {
        if (isShowProgress) {
            viewBinding.llDotLoadingSaldo.visibility = View.VISIBLE
            viewBinding.llKeListRekening.visibility = View.GONE
        } else {
            viewBinding.llDotLoadingSaldo.visibility = View.GONE
            viewBinding.llKeListRekening.visibility = View.VISIBLE
        }
    }

    override fun showSkeletonPfm(isShowSkeleton: Boolean) {
        if (isAdded && activity != null) {
            requireActivity().runOnUiThread {
                viewBinding.tvIncomeDate.showSaldo()
                if (isShowSkeleton) {
                    viewBinding.llPfmSummary.tvOutcomeValue.showSkeleton()
                    viewBinding.llPfmSummary.tvIncomeValue.showSkeleton()
                    viewBinding.llPfmSummary.tvBudget.showSkeleton()
                    viewBinding.tvIncomeDate.showSkeleton()
                    skeletonScreenSaved?.show()
                } else {
                    viewBinding.llPfmSummary.tvOutcomeValue.hideSkeleton()
                    viewBinding.llPfmSummary.tvIncomeValue.hideSkeleton()
                    viewBinding.llPfmSummary.tvBudget.hideSkeleton()
                    viewBinding.tvIncomeDate.hideSkeleton()
                    skeletonScreenSaved?.hide()
                }
            }
        }
    }

    override fun setSafetyModeGone(showSafety: Boolean) {
        val layoutParams = viewBinding.viewGradient?.layoutParams as ViewGroup.MarginLayoutParams

        if (showSafety) {
            viewBinding.safetyMode.root.visibility = View.GONE
            countTimerStop()

            layoutParams.height =
                resources.getDimensionPixelSize(R.dimen.view_gradient_height_gone) // 118dp
            layoutParams.topMargin =
                resources.getDimensionPixelSize(R.dimen.view_gradient_margin_top_gone) // 85dp
        } else {
            viewBinding.safetyMode.root.visibility = View.VISIBLE

            layoutParams.height =
                resources.getDimensionPixelSize(R.dimen.view_gradient_height_visible) // 188dp
            layoutParams.topMargin =
                resources.getDimensionPixelSize(R.dimen.view_gradient_margin_top_visible) // 30dp
        }

        viewBinding.viewGradient?.layoutParams = layoutParams
    }


    private fun countTimerStop() {
        countDownTimer?.cancel()
    }

    private fun setTextTimer(timer: Int) {
        val countDown: Int = SECOND * timer
        countDownTimer = object : CountDownTimer(countDown.toLong(), SECOND.toLong()) {
            @SuppressLint("DefaultLocale")
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / SECOND
                viewBinding.safetyMode.timerSafetyMode.setText(
                    String.format(
                        "%02d : %02d : %02d", seconds / 3600,
                        seconds % 3600 / 60, seconds % 60
                    )
                )
            }

            override fun onFinish() {
                setSafetyModeGone(true)
            }
        }.start()
    }

    private fun showSaldo() {
        viewBinding.ivSaldoEye.setImageResource(R.drawable.ic_eye_close)
        val animation = AnimationUtils.loadAnimation(activity, R.anim.fade_in_trasition)
        viewBinding.totalSaldoIb.startAnimation(animation);
        viewBinding.totalSaldoIb.makeVisible()
        viewBinding.ivDotSaldo.makeGone()
        cekSaldoTertahan()
    }

    private fun hideSaldo() {
        viewBinding.ivSaldoEye.setImageResource(R.drawable.ic_eye_open)
        val animation = AnimationUtils.loadAnimation(activity, R.anim.fade_in_trasition)
        viewBinding.ivDotSaldo.startAnimation(animation);
        viewBinding.ivDotSaldo.makeVisible()
        viewBinding.totalSaldoIb.makeGone()
        viewBinding.ivAlertSaldo.makeGone()
    }

    private fun showPfm() {
        viewBinding.llPfmSummary.tvIncomeValue.showSaldo()
        viewBinding.llPfmSummary.tvOutcomeValue.showSaldo()
        viewBinding.llPfmSummary.tvBudget.showSaldo()
        viewBinding.tvIncomeDate.showSaldo()

        viewBinding.tvLihatPfm.setText(GeneralHelper.getString(R.string.sembunyikan))
        viewBinding.ivLihatPfm.setImageResource(R.drawable.ic_eye_close)
    }

    private fun hidePfm() {
        viewBinding.llPfmSummary.tvIncomeValue.hideSaldo()
        viewBinding.llPfmSummary.tvOutcomeValue.hideSaldo()
        viewBinding.llPfmSummary.tvBudget.hideSaldo()

        //ubah button
        viewBinding.tvLihatPfm.setText(GeneralHelper.getString(R.string.tampilkan))
        viewBinding.ivLihatPfm.setImageResource(R.drawable.ic_eye_open)
    }

    //    hide nfc menu
    private fun updateUI() = with(viewBinding) {
        if (isNfcActive) {
            tvStatus.text = GeneralHelper.getString(R.string.aktif)
            tvStatus.setTextColor(GeneralHelper.getColor(R.color.success100))
            ivCircle.setImageResource(R.drawable.ic_circle_semantic_green100)
            context?.let {
                viewBinding.rlStatus.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(it, R.color.success20))
            }
            ivNfc?.makeVisible()
            rlAktifkan?.makeGone()
        } else {
            viewBinding.tvStatus?.text = GeneralHelper.getString(R.string.nonaktif)
            viewBinding.tvStatus?.setTextColor(GeneralHelper.getColor(R.color.neutral_dark10))
            viewBinding.ivCircle?.setImageResource(R.drawable.ic_circle_neutral_dark20)
            context?.let {
                viewBinding.rlStatus?.backgroundTintList =
                    ColorStateList.valueOf(ContextCompat.getColor(it, R.color.neutralLight20))
            }
            ivNfc?.makeGone()
            rlAktifkan?.makeVisible()
        }
    }

    private fun setMenu() {
        viewBinding.srlDashboardIb.setOnRefreshListener(this)

        viewBinding.rvMenuTop.layoutManager =
            GridLayoutManager(activity, 4, GridLayoutManager.VERTICAL, false)
        viewBinding.rvMenuMore.layoutManager =
            GridLayoutManager(activity, 4, GridLayoutManager.VERTICAL, false)
        viewBinding.rvMenu.layoutManager =
            GridLayoutManager(activity, 4, GridLayoutManager.VERTICAL, false)

        context?.let {
            viewBinding.rvMenu.adapter =
                MenuDefaultAdapter(MenuConfig.getDefaultMenu(requireActivity()))
        }
    }

    private fun setToolbar(name: String) {
        viewBinding.tvName.text =
            String.format(GeneralHelper.getString(R.string.text_name_home), name)
        viewBinding.tvGreetingNasabah.text = getGreeting()
        viewBinding.tvGreetingNasabah.apply {
            setShadowLayer(8f, 0f, 0f, Color.argb(163, 0, 0, 0))
        }
        viewBinding.tvName.apply {
            setShadowLayer(8f, 0f, 0f, Color.argb(163, 0, 0, 0))
        }
        setBackgroundToolbar()
    }

    private fun getGreeting(): String {
        return when (Calendar.getInstance().toTimePeriod()) {
            TimePeriod.EARLY_MORNING,
            TimePeriod.MORNING,
            TimePeriod.NOON -> getString(R.string.greetings_good_morning)

            TimePeriod.AFTERNOON -> getString(R.string.greetings_good_afternoon)
            TimePeriod.EVENING -> getString(R.string.greetings_good_evening)
            TimePeriod.NIGHT,
            TimePeriod.LATE_NIGHT -> getString(R.string.greetings_good_night)
        }
    }

    private fun setBackgroundToolbar() {
        val timePeriod = Calendar.getInstance().toTimePeriod()
        val backgroundRes = when (timePeriod) {
            TimePeriod.EARLY_MORNING -> R.drawable.img_thematic_malam
            TimePeriod.MORNING -> R.drawable.img_thematic_pagi
            TimePeriod.NOON, TimePeriod.AFTERNOON -> R.drawable.img_thematic_siang
            TimePeriod.EVENING, TimePeriod.NIGHT -> R.drawable.img_thematic_sore
            TimePeriod.LATE_NIGHT -> R.drawable.img_thematic_malam
        }

        viewBinding.ivThematic.setImageResource(backgroundRes)
    }

    fun sendViewToBack(child: View) {
        val mParent = child.parent as ViewGroup
        mParent.removeView(child)
        mParent.addView(child, 0)
    }

    override fun onSuccessSaldoUtama(saldoReponse: SaldoReponse?) {
        updateWidget(context)
        showPullRefresh(false)
        showProgressSaldo(false)

        mSaldo = saldoReponse?.balanceString
        isHold = saldoReponse?.isOnHold ?: false

        //update view saldo
        saldoReponse?.currency?.let { updateSaldo(it, mSaldo) }

        if (isSaldoShow) {
            showSaldo()
        } else {
            hideSaldo()
        }

    }


    override fun setNickname(nickname: String) {
        setToolbar(nickname)
    }

    /**
     * Method untuk menampilkan 4 menu kategori
     */
    override fun setFourMenuKategori(menuKategoriList: MutableList<MenuKategori>) {

        activity?.let {
            viewBinding.rvMenuTop.adapter = KategoriMenuAdapter(
                it as DashboardIBActivity,
                menuKategoriList,
                this
            )
            try {
                requireActivity()?.let {
                    viewBinding.rvMenuTop.adapter = KategoriMenuAdapter(
                        requireActivity() as DashboardIBActivity,
                        menuKategoriList,
                        this
                    )
                }
            } catch (e: Exception) {
                Log.e(TAG, "setFourMenuKategori: ", e)
            }
        }
    }

    /**
     * Method untuk menampilkan menu semuanya
     */
    override fun setAllMenuKategori(menuAllKategoriList: MutableList<MenuKategori>) {
        activity?.let {
            viewBinding.rvMenuMore.layoutManager =
                GridLayoutManager(
                    it as DashboardIBActivity,
                    4,
                    GridLayoutManager.VERTICAL,
                    false
                )

            viewBinding.rvMenuMore.adapter =
                KategoriMenuAdapter(it, menuAllKategoriList, this)
        }
    }

    override fun onExceptionIgnore(Exception: String?) {
        if (activity == null) {
            return
        }

        requireActivity().runOnUiThread {
            showPullRefresh(false)
        }
    }

    override fun onExceptionSummaryPfm() {
        if (activity == null) {
            return
        }

        requireActivity().runOnUiThread {
            viewBinding.llPfmSummary.tvIncomeValue.setText(resources.getString(R.string.empty))
            viewBinding.llPfmSummary.tvOutcomeValue.setText(resources.getString(R.string.empty))
            viewBinding.llPfmSummary.tvBudget.setText(resources.getString(R.string.empty))
        }
    }

    override fun onResume() {
        super.onResume()
        fragmentPresnter.let {
            fragmentPresnter.getMenuKategori()
        }

        if (java.lang.Boolean.FALSE == isBubbleNewOnboard &&
            java.lang.Boolean.FALSE == fragmentPresnter.bottomBiometric &&
            java.lang.Boolean.FALSE == fragmentPresnter.newBubbleOnboard &&
            java.lang.Boolean.TRUE == isFromIsiSaldoBukrek &&
            java.lang.Boolean.TRUE == GeneralHelper.checkBiometricSupport()
        ) {
            openSettingBio()
        }
    }

    override fun onRefresh() {
        fragmentPresnter?.let {
            fragmentPresnter.getSaldoUtama()
            fragmentPresnter.getNotificationUnreads()
            fragmentPresnter.getPFMSummary()
            fragmentPresnter.getPromoFeatured()
        }
    }

    fun updateSaldoUtama() {
        fragmentPresnter?.let {
            fragmentPresnter.getSaldoUtama()
        }
    }

    override fun onItemClicked(kategoriId: Int, title: String) {
        fragmentPresnter.getListMenubyId(kategoriId, title)
    }

    override fun onKategoriUpdate(kategoriId: Int) {
        fragmentPresnter?.let {
            fragmentPresnter.updateFlagNewKategori(kategoriId)
        }
    }

    override fun onClickPromoItem(promoResponse: DetailPromoResponse) {
        fragmentPresnter.getDetailPromoItem(promoResponse.id.toString())
    }

    override fun onClick(view: View?) {
        var id = view?.id
        when (id) {
            R.id.btn_lihat_promo -> {
                AllPromoActivity.launchIntent(activity, false)
            }

            else -> {
                // Do Nothing
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_SET_DEFAULT && data != null) {
            if (resultCode == RESULT_OK) {
                updateSaldoUtama()

                if (data.getStringExtra("nama_impian") != null && activity != null) {
                    GeneralHelper.showSnackBarWithTransparentStatusBar(
                        requireActivity().findViewById(R.id.contentDashboard),
                        data.getStringExtra(Constant.TAG_MESSAGE),
                        R.color.success80, R.drawable.ic_check_circle
                    )
                }
            } else {
                if (data != null && activity != null) {
                    GeneralHelper.showSnackBarWithTransparentStatusBar(
                        requireActivity().findViewById(R.id.contentDashboard),
                        data.getStringExtra(Constant.TAG_ERROR_MESSAGE),
                        R.color.error80, R.drawable.ic_cancel_danger_18dp
                    )
                }
            }
        }

        if (requestCode == Constant.REQ_SET_Notif && data != null) {
            if (resultCode == RESULT_OK) {
                fragmentPresnter.getNotificationUnreads()
            }
        }

        if (resultCode == RESULT_CANCELED && data != null && data.hasExtra(Constant.TAG_ERROR_MESSAGE)) {
            if (data.getStringExtra(Constant.TAG_ERROR_MESSAGE) != null) {
                onException(data.getStringExtra(Constant.TAG_ERROR_MESSAGE))
            }
        }
    }

    override fun onSessionEnd(message: String?) {
        _binding?.srlDashboardIb?.setRefreshing(false)
        if (activity != null) {
            //session end to main activity Dashboard
            (activity as DashboardIBActivity?)!!.onSessionEnd(message)
        }
        fragmentPresnter.stop()
    }

    override fun closeCallback() {
        //do nothing
    }

    override fun onTargetClick(bubbleShowCase: BubbleShowCase) {
        // Do nothing
    }

    override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
        // Do nothing
    }

    override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {
        // Do nothing
    }

    override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {
        // Do nothing
    }

    override fun onSkipActionClick(bubbleShowCase: BubbleShowCase) {
        if (fragmentPresnter != null) {
            if (isBubbleNewOnboard) {
                fragmentPresnter.closeBubbleOnboarding()
                fragmentPresnter.getLatestBlastNotif()
            } else {
                if (java.lang.Boolean.FALSE == fragmentPresnter.bottomBiometric) {
                    if (java.lang.Boolean.TRUE == GeneralHelper.checkBiometricSupport()) {
                        openSettingBio()
                    }
                }
            }
        }
    }

    override fun onException(message: String?) {
        try {
            //release swipe refresh
            showPullRefresh(false)

            if (GeneralHelper.isContains(LIST_TYPE_GAGAL, message) && activity != null) {
                GeneralHelper.showBottomDialog(activity, message)
            } else {
                if (activity != null) {
                    GeneralHelper.showSnackBarWithTransparentStatusBar(
                        (activity as DashboardIBActivity?)?.findViewById(R.id.contentDashboard),
                        message, R.color.error80, R.drawable.ic_cancel_danger_18dp
                    )
                }
            }
        } catch (e: java.lang.Exception) {
        }
    }

    fun openSettingBio() {
        val title = String.format(
            GeneralHelper.getString(R.string.login_dalam_genggaman),
            fragmentPresnter.biometricType
        )
        val subtitle: String = if (java.lang.Boolean.TRUE == fragmentPresnter.statusUpdateBio) {
            String.format(
                GeneralHelper.getString(R.string.desc_login_dalam_genggaman_update),
                fragmentPresnter.biometricType
            )
        } else {
            String.format(
                GeneralHelper.getString(R.string.desc_login_dalam_genggaman),
                fragmentPresnter.biometricType
            )
        }
        val bottomFinger = BottomSheet2ButtonLogin.newInstance(
            GeneralHelper.getString(R.string.image_setting_bio),
            title,
            subtitle,
            GeneralHelper.getString(R.string.ke_pengaturan_biometric),
            GeneralHelper.getString(R.string.atur_nanti),
            this, false
        )
        bottomFinger.isCancelable = false
        val ft = requireActivity().supportFragmentManager.beginTransaction()
        ft.add(bottomFinger, null)
        ft.commitAllowingStateLoss()
        fragmentPresnter.updateStatusUpdateBio(true)
        fragmentPresnter.updateBottomBiometric(true)
    }

    override fun onClickOk() {
        (activity as DashboardIBActivity?)?.switchToProfile()
    }

    override fun onClickCancel() {
        //do nothing
    }

    override fun onClickLogin() {
        //do nothing
    }

    override fun onWindowFocusChanged(hasFocus: Boolean) {
        checkNfcStatus()
    }

    override fun onAttach(context: Context) {
        super.onAttach(context)
        if (context is DashboardIBActivity) {
            context.focusChangeListener = this
        }
    }

    override fun onDetach() {
        super.onDetach()
        if (activity is DashboardIBActivity) {
            (activity as DashboardIBActivity).focusChangeListener = null
        }
    }

    //    hide nfc menu
    private fun showInputPin() {
        context?.let {
            if (nfcPayment?.isDefaultService() == true) {
                val pinFragment = PinFragment(it, this)
                pinFragment.show()
            } else {
                nfcPayment?.changeDefaultPaymentService(it)
            }
        }
    }

    override fun onSendPinComplete(pin: String) {
        fragmentPresnter.getDataPayloadNfc(pin)
    }

    override fun onLupaPin() {
        context?.let {
            LupaPinActivity.launchIntent(it as DashboardIBActivity)
        }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            setupTransparentStatusBar();
        }
    }
}