package id.co.bri.brimo.contract.IView.britamarencanarevamp

import id.co.bri.brimo.contract.IView.IMvpView
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptGagalEmasResponse

interface IKonfirmasiTabunganRevView: IMvpView {

    fun onSuccessGetPayment(paymentResponse: ReceiptRevampResponse?)

    fun onException93(message: String?)

    fun onException01(message: String?)

    fun onSuccessGetPaymentEmas(receiptEmasResponse: ReceiptRevampResponse)

    fun onSuccessGetPaymentEmasFailed(receiptGagalEmasResponse: ReceiptGagalEmasResponse)
}