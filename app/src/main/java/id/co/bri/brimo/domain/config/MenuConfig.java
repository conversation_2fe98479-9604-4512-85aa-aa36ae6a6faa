package id.co.bri.brimo.domain.config;

import static java.lang.Boolean.TRUE;

import android.app.Activity;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

import androidx.annotation.Nullable;
import androidx.fragment.app.FragmentActivity;

import java.util.ArrayList;
import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.domain.config.enumconfig.JourneyType;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.profilerevamp.ListInfoProfile;
import id.co.bri.brimo.models.apimodel.response.profilerevamp.ProfileInfoResp;
import id.co.bri.brimo.models.daomodel.DashboardMenu.LanguageModel;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashFav;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuDashboard;
import id.co.bri.brimo.models.daomodel.DashboardMenu.MenuKategori;
import id.co.bri.brimo.models.daomodel.FastMenu;
import id.co.bri.brimo.models.daomodel.FastMenuDefault;
import id.co.bri.brimo.models.revamp.dashboard.KategoriMenuModel;
import id.co.bri.brimo.ui.activities.BrigunaDigitalActivity;
import id.co.bri.brimo.ui.activities.CatatanKeuanganActivity;
import id.co.bri.brimo.ui.activities.CekBrizziDariLuarActivity;
import id.co.bri.brimo.ui.activities.CeriaActivity;
import id.co.bri.brimo.ui.activities.DaftarCcActivity;
import id.co.bri.brimo.ui.activities.DashboardIBActivity;
import id.co.bri.brimo.ui.activities.EditFastMenuRevampActivity;
import id.co.bri.brimo.ui.activities.FormBpjsActivity;
import id.co.bri.brimo.ui.activities.FormBrizziActivity;
import id.co.bri.brimo.ui.activities.FormCicilanActivity;
import id.co.bri.brimo.ui.activities.FormKaiActivity;
import id.co.bri.brimo.ui.activities.FormKonversiVallasActivity;
import id.co.bri.brimo.ui.activities.FormKreditActivity;
import id.co.bri.brimo.ui.activities.FormLTMPTActivity;
import id.co.bri.brimo.ui.activities.FormMpnActivity;
import id.co.bri.brimo.ui.activities.FormPdamActivity;
import id.co.bri.brimo.ui.activities.FormQrActivity;
import id.co.bri.brimo.ui.activities.FormTelevisiActivity;
import id.co.bri.brimo.ui.activities.InfoKursActivity;
import id.co.bri.brimo.ui.activities.InfoPinjamanActivity;
import id.co.bri.brimo.ui.activities.InfoSahamActivity;
import id.co.bri.brimo.ui.activities.KontakKamiActivity;
import id.co.bri.brimo.ui.activities.ListRekeningCategoryActivity;
import id.co.bri.brimo.ui.activities.MutasiActivity;
import id.co.bri.brimo.ui.activities.QrMPMActivity;
import id.co.bri.brimo.ui.activities.QrTransferActivity;
import id.co.bri.brimo.ui.activities.SyaratKetentuanActivity;
import id.co.bri.brimo.ui.activities.UbahPinActivity;
import id.co.bri.brimo.ui.activities.applyvcc.ApplyVccSofListActivity;
import id.co.bri.brimo.ui.activities.asuransirevamp.DashboardAsuransiActivity;
import id.co.bri.brimo.ui.activities.britamarencanarevamp.DashboardRencanaActivity;
import id.co.bri.brimo.ui.activities.brivarevamp.FormBrivaRevampActivity;
import id.co.bri.brimo.ui.activities.cardless.FormSetorTunaiActivity;
import id.co.bri.brimo.ui.activities.ccqrismpm.SofQrisActivity;
import id.co.bri.brimo.ui.activities.changepassnewskin.ChangePassAccountActivity;
import id.co.bri.brimo.ui.activities.changepinnewskin.ChangePinActivity;
import id.co.bri.brimo.ui.activities.dashboardInvestasi.DashboardInvestasiActivity;
import id.co.bri.brimo.ui.activities.depositorevamp.DashboardDepositoActivity;
import id.co.bri.brimo.ui.activities.dompetdigitalreskin.FormDompetDigitalReskinActivity;
import id.co.bri.brimo.ui.activities.donasirevamp.FormDonasiRevampActivity;
import id.co.bri.brimo.ui.activities.dplkrevamp.DashboardDplkRevampActivity;
import id.co.bri.brimo.ui.activities.emas.DashboardEmasActivity;
import id.co.bri.brimo.ui.activities.form.FormGeneralRevampActivity;
import id.co.bri.brimo.ui.activities.ibbiz.IbbizActivity;
import id.co.bri.brimo.ui.activities.launcher.BrowserIntentActivity;
import id.co.bri.brimo.ui.activities.listrikrevamp.FormListrikRevampActivity;
import id.co.bri.brimo.ui.activities.pajakhoreka.FormPajakHorekaActivity;
import id.co.bri.brimo.ui.activities.pbb.FormPbbActivity;
import id.co.bri.brimo.ui.activities.pendidikanrevamp.FormPendidikanRevampActivity;
import id.co.bri.brimo.ui.activities.pengelolaankartu.PengelolaanKartuNewActivity;
import id.co.bri.brimo.ui.activities.portofolioksei.DashboardKseiActivity;
import id.co.bri.brimo.ui.activities.rdnrevamp.dashboard.DashboardRdnRevampActivity;
import id.co.bri.brimo.ui.activities.sbnrevamp.DashboardSbnRevampActivity;
import id.co.bri.brimo.ui.activities.property.FormPropertyActivity;
import id.co.bri.brimo.ui.activities.signal.FormSignalActivity;
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity;
import id.co.bri.brimo.ui.activities.tartun.FormTarikTunaiActivity;
import id.co.bri.brimo.ui.activities.pulsadata.FormPulsaDataRevActivity;
import id.co.bri.brimo.ui.activities.qrdagang.QrMerchantActivity;
import id.co.bri.brimo.ui.activities.telkomrevamp.FormTelkomRevampActivity;
import id.co.bri.brimo.ui.activities.transactionlimitinformation.TransactionLimitInformationActivity;
import id.co.bri.brimo.ui.activities.transferinternasional.FormTfInternasionalActivity;
import id.co.bri.brimo.ui.activities.transferrevamp.FormTransferAliasRevampActivity;
import id.co.bri.brimo.ui.activities.virtualdebitcard.OnBoardingVDCActivity;
import id.co.bri.brimo.ui.activities.voucher.VoucherActivity;


public class MenuConfig {

    private static final String TAG = "MenuConfig";
    public static final String MENU_MODEL = "menumodel";
    public static final String MENU_MODEL2 = "menumodel2";
    public static final String MENU_MODEL3 = "menumodel3";
    public static final String MENU_MODEL4 = "menumodel4";
    public static final String MENU_MODELALL = "menumodelall";
    public static final String MENU_MODELNONIB = "menumodelnonib";
    public static final String MENU_MODEL_FAST_MENU = "menumodelFastMenu";
    public static final String MENU_MODEL_FAST_MENU_LAIN = "menumodelFastMenuLain";
    public static final String MENU_MUTASI = "mutasi";
    public static final String MENU_TRANSFER = "transfer";
    public static final String MENU_BRIZZI = "brizzi";
    public static final String MENU_BRIZZI_NON_USER = "brizziNonUser";
    public static final String MENU_TOP_UP_WALLET = "dompet_digital";
    public static final String MENU_TOP_UP_GOPAY = "top_up_gopay";
    public static final String MENU_BRIVA = "briva";
    public static final String MENU_LISTRIK = "listrik";
    public static final String MENU_PULSA = "pulsa";
    public static final String MENU_TARIK_SETOR = "tarik_setor";
    public static final String MENU_BPJS = "bpjs";
    public static final String MENU_CICILAN = "cicilan";
    public static final String MENU_KAI = "kai";
    public static final String MENU_KARTU_KREDIT = "kartu_kredit";
    public static final String MENU_DEPOSITO = "deposito";
    public static final String MENU_PFM = "catatan_keuangan";
    public static final String MENU_PASCA_BAYAR = "pasca_bayar";
    public static final String MENU_LAINNYA = "lainnya";
    public static final String MENU_KOSONG = "kosong";
    public static final String MENU_BUKA_REKENING = "bukarekening";
    public static final String MENU_BRISPOT = "brispot";
    public static final String MENU_RDN = "rdn";
    public static final String MENU_DAFTAR_BRIMO = "daftar";
    public static final String MENU_TARIK_TUNAI = "tarik_tunai";
    public static final String MENU_SETOR_TUNAI = "setor_tunai";
    public static final String MENU_QR = "menuqr";
    public static final String MENU_TELKOM = "telkom";
    public static final String MENU_TV = "tv_kabel_internet";
    public static final String MENU_ASURANSI = "asuransi";
    public static final String MENU_ATM = "atm";
    public static final String MENU_CERIA = "ceria";
    public static final String MENU_PINANG = "pinang";
    public static final String MENU_DONASI = "donasi";
    public static final String MENU_DAFTAR_CC = "daftarcc";
    public static final String MENU_DPLK = "dplk_bri";
    public static final String MENU_QR_MERCHANT = "qr_pedagang";
    public static final String MENU_BRILIFE = "brilife";
    public static final String MENU_PRIO = "prioritas";
    public static final String MENU_PROMO = "promo";
    public static final String MENU_QR_TRANSFER = "qr_transfer";
    public static final String MENU_SNPMB = "snpmb";
    public static final String MENU_PDAM = "pdam";
    public static final String MENU_TRANSFER_INTERNASIONAL = "transfer_internasional";
    public static final String MENU_CC_SOF = "kartu_kredit_bri";
    public static final String MENU_BRITAMA_RENCANA = "rencana";
    public static final String MENU_QR_MCM = "qr_mcm";
    public static final String MENU_KONVERSI_VALAS = "konversi_valas";
    public static final String MENU_PENDIDIKAN = "pendidikan";
    public static final String MENU_BRIGUNA = "briguna";
    public static final String MENU_ESBN = "esbn";
    public static final String MENU_IBBIZ = "ibbiz";
    public static final String MENU_PBB = "pbb";
    public static final String MENU_PINJAMAN_BRI = "pinjaman_bri";
    public static final String MENU_TRAVEL = "travel";
    public static final String MENU_BANK_RAYA = "buka_tabungan";
    public static final String MENU_MPN = "penerimaan_negara";
    public static final String MENU_STREAMING = "streaming";
    public static final String MENU_BRI_FINANCE = "gadai_kendaraan";
    public static final String MENU_BRIGHTS = "brights";
    public static final String MENU_JADI_MERCHANT = "jadi_merchant";
    public static final String MENU_GAME = "game";
    public static final String MENU_VIRTUAL_DEBIT = "virtual_debit";
    public static final String MENU_SIGNAL = "signal";
    public static final String MENU_NFC = "nfc_payment";
    public static final String TAG_MENU_FAV = "favorit";
    public static final String TAG_MENU_LAINNYA = "lainnya";
    public static final String TAG_MENU_LENDING = "lending";
    public static final String TAG_MENU_BRI_GROUP = "bri_group";
    public static final String TAG_MENU_DASHBOARD = "dashboard";

    static int mCheckPoint = 0;
    static int mCheckPointDS = 0;

    /**
     * class ini digunakan utk mapping code menu kategori
     */
    public static class MenuKategoriId {
        public static final int OS = 0;
        public static final int TOP_UP = 1;
        public static final int TAGIHAN = 2;
        public static final int SETOR_TARIK = 3;
        public static final int LIFE_STYLE = 4;
        public static final int QRIS_TRANSFER = 5;
        public static final int DEBIT = 6;
        public static final int CATATAN_KEU = 7;
        public static final int INVESTASI = 8;
        public static final int DONASI = 9;
        public static final int TRANSFER_VALAS = 10;
        public static final int KARTU_KREDIT = 11;
        public static final int ASURANSI = 12;
        public static final int PINJAMAN = 13;
        public static final int SPLITBILL = 15;
        public static final int LAINNYA = 14;
        public static final int FAVORIT = 50;
        public static final int NON_KATEGORI = 51;
        public static final int INFO_AKUN_PENGATURAN = 52;
        public static final int INFO_AKUN_KEAMANAN = 53;
        public static final int INFO_AKUN_KONTAK_BRI = 54;
        public static final int INFO_AKUN_INFORMASI = 55;
        public static final int SETTINGS = 15;
        public static final int HELP = 16;
        public static final int INFORMATION = 17;

    }

    /**
     * class ini digunakan utk mapping code menu id
     */
    public static class SubMenuId {
        public static final String FAST_MENU = "FM";
        public static final String SECURITY = "SC";
        public static final String UPDATE_ACCOUNT = "UR";
        public static final String CHAT_BANKING = "CB";
        public static final String CARD_MANAGEMENT = "CM";
        public static final String LANGUAGE = "LG";
        public static final String TERMS_CONDITION = "SK";
        public static final String HELP_CENTER = "HC";
        public static final String CONTACT_US = "CU";
        public static final String TYPE_TRANSACTION_LIMIT = "LM";
        public static final String RATE_INFO = "RI";
        public static final String STOCK_INFO = "SI";
        public static final String ATM_LOCATION = "AL";
        public static final String BRANCH_LOCATION = "BL";
    }

    public static class MenuNameId {
        public static final String MENU_BRIVA = "BRIVA";
        public static final String MENU_BRIZZI = "BRIZZI";
        public static final String MENU_E_WALLET = "E-Wallet";
        public static final String MENU_PULSA_DATA = "Pulsa/Data";
        public static final String MENU_LISTRIK = "Listrik";
        public static final String MENU_TOPUP_LISTRIK = "Token Listrik";
        public static final String MENU_BPJS = "BPJS";
        public static final String MENU_KARTU_KREDIT = "Kartu Kredit";
        public static final String MENU_CICILAN = "Cicilan";
        public static final String MENU_KAI = "KAI";
        public static final String MENU_PDAM = "PDAM";
        public static final String MENU_PENDIDIKAN = "Pendidikan";
        public static final String MENU_TV_KABEL_INTERNET = "TV Kabel\n& Internet";
        public static final String MENU_ASURANSI = "Asuransi";
        public static final String MENU_PENERIMAAN_NEGARA = "Penerimaan\nNegara";
        public static final String MENU_PBB = "PBB";
        public static final String MENU_PASCA_BAYAR = "Pascabayar";
        public static final String MENU_SNPMB = "SNPMB";
        public static final String MENU_TELKOM = "Telkom";
        public static final String MENU_SIGNAL = "SIGNAL";
        public static final String MENU_PAJAK_DAERAH= "Pajak Daerah";
        public static final String MENU_IPL_PROPERTI = "IPL & Properti";
        public static final String MENU_SETOR_TUNAI = "Setor Tunai";
        public static final String MENU_TARIK_TUNAI = "Tarik Tunai";
        public static final String MENU_KARTU_DEBIT_VIRTUAL = "Kartu\nDebit Virtual";
        public static final String MENU_DEPOSITO = "Deposito";
        public static final String MENU_DPLK_BRI = "DPLK BRI";
        public static final String MENU_RDN = "RDN";
        public static final String MENU_SBN = "SBN";
        public static final String MENU_BRIGHTS = "Brights";
        public static final String MENU_EMAS = "Emas";
        public static final String MENU_PORTOFOLIO_KSEI = "Portofolio KSEI";
        public static final String MENU_DASHBOARD_INVESTASI = "Dashboard Investasi";
        public static final String MENU_RENCANA = "Rencana";
        public static final String MENU_VOUCHER_GAME = "Voucher\nGame";
        public static final String MENU_TOPUP_VOUCHER_GAME = "Voucher\nGame";
        public static final String MENU_VOUCHER_STREAMING = "Voucher\nStreaming";
        public static final String MENU_TOPUP_STREAMING = "Voucher\nStreaming";
        public static final String MENU_BRI_LIGA_1 = "BRI Liga 1";
        public static final String MENU_BELANJA_HARIAN = "Belanja Harian";
        public static final String MENU_PESAWAT = "Pesawat";
        public static final String MENU_WHOOSH = "Whoosh";
        public static final String MENU_BUS_SHUTTLE = "Bus & Shuttle";
        public static final String MENU_KERETA_API = "Kereta Api";
        public static final String MENU_LANGGANAN_INTERNET = "Langganan Internet";
        public static final String MENU_KIRIM_BARANG = "Kirim Barang";
        public static final String MENU_KARTU_KREDIT_BRI = "Kartu Kredit\nBRI";
        public static final String MENU_BAYAR_KARTU_KREDIT = "Bayar Kartu\nKredit";
        public static final String MENU_LOAN_ON_APP = "Loan On App";
        public static final String MENU_CERIA = "Ceria";
        public static final String MENU_QLOLA = "QLola";
        public static final String MENU_GADAI_KENDARAAN = "KKB BRI";
        public static final String MENU_BUKA_TABUNGAN_RAYA = "Buka\nTabungan Raya";
        public static final String MENU_QR_PEDAGANG = "QR Pedagang";
        public static final String MENU_INFO_LELANG = "Info Lelang";
        public static final String MENU_MUTASI = "Mutasi";
        public static final String MENU_TRANSFER = "Transfer";
        public static final String MENU_TRANSFER_INTERNASIONAL = "Transfer Internasional";
        public static final String MENU_CATATAN_KEUANGAN = "Catatan\nKeuangan";
        public static final String MENU_DONASI = "Donasi";
        public static final String MENU_PINJAMAN = "Pinjaman";
        public static final String MENU_KONVERSI_VALAS = "Konversi Valas";
        public static final String MENU_QR_TRANSFER = "QRIS Transfer";
        public static final String MENU_QRIS = "QRIS";
        public static final String MENU_PROMO = "Promo";
        public static final String MENU_TAGIHAN = "Tagihan";
        public static final String MENU_SETOR_TRAIK_TUNAI = "Setor & Tarik Tunai";
        public static final String MENU_INVESTASI = "Investasi";
        public static final String MENU_PRODUK_BRI_LAINYA = "Produk BRI Lainnya";
        public static final String MENU_TOP_UP = "Top Up";
        public static final String MENU_VIRTUAL_DEBIT = "Virtual Debit";
        public static final String MENU_LIFESTYLE = "Lifestyle";
        public static final String MENU_NFC = "Tap to Pay";
        public static final String MENU_SPLIT_BILL = "Split Bill";
        public static final String MENU_FASET_MENU = "Fast Menu";
        public static final String MENU_UPDATE_ACCOUNT = "Update Rekening";
        public static final String MENU_PENGELOLAAN_KARTU = "Pengelolaan Kartu";
        public static final String MENU_SUMBER_DANA_QRIS = "Sumber Dana QRIS";
        public static final String MENU_UBAH_PIN = "Ubah Pin";
        public static final String MENU_UBAH_KATA_KUNCI = "Ubah Password";
        public static final String MENU_HELP_CENTER = "Pusat Bantuan";
        public static final String MENU_CHAT_BANKING = "Chat Banking";
        public static final String MENU_LAYANAN_BEBAS_PULSA = "Layanan Bebas Pulsa";
        public static final String MENU_CONTACT_US = "Kontak Kami";
        public static final String MENU_LIMIT_INFORMATION = "Jenis & Limit Transaksi";
        public static final String MENU_RATE_INFO = "Info Kurs";
        public static final String MENU_STOCK_INFO_BRI = "Info Saham";
        public static final String MENU_ATM_LOCATION_BRI = "Lokasi ATM";
        public static final String MENU_OFFICE_LOCATION_BRI = "Lokasi Kantor";
        public static final String MENU_TNC = "Syarat dan Ketentuan";


    }

    public static class MenuNameEn {
        public static final String MENU_BRIVA = "BRIVA";
        public static final String MENU_BRIZZI = "BRIZZI";
        public static final String MENU_E_WALLET = "E-Wallet";
        public static final String MENU_PULSA_DATA = "Pulsa/Data";
        public static final String MENU_LISTRIK = "Electricity";
        public static final String MENU_TOPUP_LISTRIK = "PLN Prepaid";
        public static final String MENU_BPJS = "BPJS";
        public static final String MENU_KARTU_KREDIT = "Credit Card";
        public static final String MENU_CICILAN = "Installments";
        public static final String MENU_KAI = "KAI";
        public static final String MENU_PDAM = "PDAM";
        public static final String MENU_PENDIDIKAN = "Education";
        public static final String MENU_TV_KABEL_INTERNET = "TV & Internet";
        public static final String MENU_ASURANSI = "Insurance";
        public static final String MENU_PENERIMAAN_NEGARA = "State Revenue";
        public static final String MENU_PBB = "PBB";
        public static final String MENU_PASCA_BAYAR = "Postpaid";
        public static final String MENU_SNPMB = "SNPMB";
        public static final String MENU_TELKOM = "Telkom";
        public static final String MENU_SIGNAL = "SIGNAL";
        public static final String MENU_PAJAK_DAERAH= "Local Taxes";
        public static final String MENU_IPL_PROPERTI = "IPL & Property";
        public static final String MENU_SETOR_TUNAI = "Cash Deposit";
        public static final String MENU_TARIK_TUNAI = "Cash Withdrawal";
        public static final String MENU_KARTU_DEBIT_VIRTUAL = "Kartu\nDebit Virtual";
        public static final String MENU_DEPOSITO = "Deposito";
        public static final String MENU_DPLK_BRI = "DPLK BRI";
        public static final String MENU_RDN = "RDN";
        public static final String MENU_SBN = "SBN";
        public static final String MENU_BRIGHTS = "Brights";
        public static final String MENU_EMAS = "Emas";
        public static final String MENU_PORTOFOLIO_KSEI = "Portofolio KSEI";
        public static final String MENU_DASHBOARD_INVESTASI = "Dashboard Investasi";
        public static final String MENU_RENCANA = "Rencana";
        public static final String MENU_VOUCHER_GAME = "Voucher\nGame";
        public static final String MENU_TOPUP_VOUCHER_GAME = "Game\nVouchers";
        public static final String MENU_VOUCHER_STREAMING = "Voucher\nStreaming";
        public static final String MENU_TOPUP_STREAMING = "Streaming\nVouchers";
        public static final String MENU_BRI_LIGA_1 = "BRI Liga 1";
        public static final String MENU_BELANJA_HARIAN = "Belanja Harian";
        public static final String MENU_PESAWAT = "Pesawat";
        public static final String MENU_WHOOSH = "Whoosh";
        public static final String MENU_BUS_SHUTTLE = "Bus & Shuttle";
        public static final String MENU_KERETA_API = "Kereta Api";
        public static final String MENU_LANGGANAN_INTERNET = "Langganan Internet";
        public static final String MENU_KIRIM_BARANG = "Kirim Barang";
        public static final String MENU_KARTU_KREDIT_BRI = "BRI Credit\nCard";
        public static final String MENU_BAYAR_KARTU_KREDIT = "Pay Credit\nCard";
        public static final String MENU_LOAN_ON_APP = "Loan On App";
        public static final String MENU_CERIA = "Ceria";
        public static final String MENU_QLOLA = "QLola";
        public static final String MENU_GADAI_KENDARAAN = "KKB BRI";
        public static final String MENU_BUKA_TABUNGAN_RAYA = "Buka\nTabungan Raya";
        public static final String MENU_QR_PEDAGANG = "QR Pedagang";
        public static final String MENU_INFO_LELANG = "Info Lelang";
        public static final String MENU_MUTASI = "Mutasi";
        public static final String MENU_TRANSFER = "Transfer";
        public static final String MENU_TRANSFER_INTERNASIONAL = "Transfer Internasional";
        public static final String MENU_CATATAN_KEUANGAN = "Cash Flow";
        public static final String MENU_DONASI = "Charity";
        public static final String MENU_PINJAMAN = "Loan";
        public static final String MENU_KONVERSI_VALAS = "Currency Exchange";
        public static final String MENU_QR_TRANSFER = "QRIS Transfer";
        public static final String MENU_QRIS = "QRIS";
        public static final String MENU_PROMO = "Promo";
        public static final String MENU_TAGIHAN = "Bills";
        public static final String MENU_SETOR_TRAIK_TUNAI = "Cash Deposit & Withdrawal";
        public static final String MENU_INVESTASI = "Investment";
        public static final String MENU_PRODUK_BRI_LAINYA = "Other BRI Products";
        public static final String MENU_TOP_UP = "Top Up";
        public static final String MENU_VIRTUAL_DEBIT = "Virtual Debit";
        public static final String MENU_LIFESTYLE = "Lifestyle";
        public static final String MENU_NFC = "Tap to Pay";
        public static final String MENU_SPLIT_BILL = "Split Bill";
        public static final String MENU_FASET_MENU = "Fast Menu";
        public static final String MENU_UPDATE_ACCOUNT = "Update Account";
        public static final String MENU_PENGELOLAAN_KARTU = "Card Management";
        public static final String MENU_SUMBER_DANA_QRIS = "QRIS Source of Fund";
        public static final String MENU_UBAH_PIN = "Change PIN";
        public static final String MENU_UBAH_KATA_KUNCI = "Change Password";
        public static final String MENU_HELP_CENTER = "Help Center";
        public static final String MENU_CHAT_BANKING = "Chat Banking";
        public static final String MENU_LAYANAN_BEBAS_PULSA = "Toll-Free Service";
        public static final String MENU_CONTACT_US = "Contact Us";
        public static final String MENU_LIMIT_INFORMATION = "Transaction Types & Limits";
        public static final String MENU_RATE_INFO = "Currency Rates";
        public static final String MENU_STOCK_INFO_BRI = "Stock Information";
        public static final String MENU_ATM_LOCATION_BRI = "ATM Location";
        public static final String MENU_OFFICE_LOCATION_BRI = "Branch Office Location";
        public static final String MENU_TNC = "Terms and Conditions";
    }

    /**
     * Class Mapping Menu ID
     */
    public static class MenuId {
        public static final int MENU_PFM = 1;
        public static final int MENU_MUTASI = 2;
        public static final int MENU_KONVERSI_VALAS = 3;
        public static final int MENU_QR_MERCHANT = 4;
        public static final int MENU_TARIK_TUNAI = 5;
        public static final int MENU_TRANSFER = 6;
        public static final int MENU_TRANSFER_INTERNASIONAL = 7;
        public static final int MENU_BRIZZI = 8;
        public static final int MENU_DOMPET_DIGITAL = 9;
        public static final int MENU_PULSA = 10;
        public static final int MENU_ASURANSI = 11;
        public static final int MENU_BRIVA = 12;
        public static final int MENU_CICILAN = 13;
        public static final int MENU_KARTU_KREDIT = 14;
        public static final int MENU_LISTRIK = 15;
        public static final int MENU_LTMPT = 16;
        public static final int MENU_PASCA_BAYAR = 17;
        public static final int MENU_PDAM = 18;
        public static final int MENU_TELKOM = 19;
        public static final int MENU_TV = 20;
        public static final int MENU_DEPOSITO = 21;
        public static final int MENU_DPLK = 22;
        public static final int MENU_RDN = 23;
        public static final int MENU_BPJS = 24;
        public static final int MENU_DONASI = 25;
        public static final int MENU_KAI = 26;
        public static final int MENU_TRAVEL = 27;
        public static final int MENU_MPN = 28;
        public static final int MENU_CERIA = 29;
        public static final int MENU_PINJAMAN_BRI = 30;
        public static final int MENU_BANK_RAYA = 31;
        public static final int MENU_QR = 32;
        public static final int MENU_DAFTAR_CC = 33;
        public static final int MENU_QR_TRANSFER = 34;
        public static final int MENU_QR_MCM = 35;
        public static final int MENU_DAFTAR_BRIMO = 37;
        public static final int MENU_BRISPOT = 36;
        public static final int MENU_BUKA_REKENING = 38;
        public static final int MENU_SETOR_TUNAI = 39;
        public static final int MENU_STREAMING = 40;
        public static final int MENU_KARTU_KREDIT_BRI = 41;
        public static final int MENU_ESBN = 42;
        public static final int MENU_PENDIDIKAN = 43;
        public static final int MENU_QLOLA = 44;
        public static final int MENU_BRI_FINANCE = 45;
        public static final int MENU_PBB = 46;
        public static final int MENU_BRIGHTS = 47;
        public static final int MENU_SNPMB = 48;
        public static final int MENU_JADI_MERCHANT = 49;
        public static final int MENU_EMAS = 50;
        public static final int MENU_MOBELANJA = 51;
        public static final int MENU_KSEI = 52;
        public static final int MENU_VOUCHER_GAME = 53;
        public static final int MENU_PROMO = 54;
        public static final int MENU_BAYAR_KARTU_KREDIT = 55;
        public static final int MENU_SIGNAL = 56;
        public static final int MENU_PAJAK_HOREKA = 61;
        public static final int MENU_DEBIT_VIRTUAL = 57;
        public static final int MENU_DASHBOARD_INVESTASI = 59;
        public static final int MENU_RENCANA = 62;
        public static final int MENU_LIGA1 = 82;
        public static final int MENU_BELANJA = 51;
        public static final int MENU_PROPERTY = 64;
        public static final int MENU_KIRIM_BARANG = 58;
        public static final int MENU_PESAWAT = 81;
        public static final int MENU_WHOOSH = 84;
        public static final int MENU_BUS = 85;
        public static final int MENU_KERETA_API = 86;
        public static final int MENU_INDIHOME = 87;
//        public static final int MENU_LOAN_IN_APP = 79;
        public static final int MENU_TOPUP_LISTRIK = 92;
        public static final int MENU_TOPUP_VOUCHER_GAME = 93;
        public static final int MENU_TOPUP_STREAMING = 94;

        public static final int MENU_BRIZZI_NON_USER = 103;
        public static final int MENU_BRIGUNA = 109;
        public static final int MENU_NFC = 63;

        public static final int MENU_FM = 65;
        public static final int MENU_UPDATE_REKENING = 66;
        public static final int MENU_PENGELOLAAN_KARTU = 67;
        public static final int MENU_SUMBER_DANA_QRIS = 91;
        public static final int MENU_UBAH_PIN = 88;
        public static final int MENU_UBAH_KATA_KUNCI = 89;
        public static final int MENU_PUSAT_BANTUAN = 68;
        public static final int MENU_CHAT_BANKING = 69;
        public static final int MENU_LAYANAN_BEBAS_PULSA = 71;
        public static final int MENU_BIOMETRIC = 90;
        public static final int MENU_KONTAK_KAMI = 72;
        public static final int MENU_JENIS_DAN_LIMIT_TRX = 73;
        public static final int MENU_INFO_KURS = 74;
        public static final int MENU_INFO_SAHAM_BRI = 75;
        public static final int MENU_LOKASI_ATM_BRI = 76;
        public static final int MENU_LOKASI_KANTOR_BRI = 77;
        public static final int MENU_SNK = 78;
        public static final int MENU_INFO_LELANG = 105;

        public static final int FAST_MENU = 111;
        public static final int SECURITY = 112;
        public static final int UPDATE_ACCOUNT = 113;
        public static final int CHAT_BANKING = 114;
        public static final int CARD_MANAGEMENT = 115;
        public static final int LANGUAGE = 116;
        public static final int TERMS_CONDITION = 117;
        public static final int HELP_CENTER = 118;
        public static final int CONTACT_US = 119;
        public static final int TYPE_TRANSACTION_LIMIT = 120;
        public static final int RATE_INFO = 121;
        public static final int STOCK_INFO = 122;
        public static final int ATM_LOCATION = 123;
        public static final int BRANCH_LOCATION = 124;
        public static final int MENU_SPLIT_BILL = 104;

    }

    public static class NewStatus {
        public static final int NEW = 1;
        public static final int OLD = 0;
    }

    /**
     * Class Mapping Menu ID
     */
    public static class IdWaNotif {
        public static final String MENU_PFM = "catatan-keuanngan";
        public static final String MENU_MUTASI = "mutasi";
        public static final String MENU_KONVERSI_VALAS = "konversi-valas";
        public static final String MENU_QR_MERCHANT = "qr-pedagang";
        public static final String MENU_TARIK_TUNAI = "tarik-tunai";
        public static final String MENU_TRANSFER = "transfer";
        public static final String MENU_TRANSFER_INTERNASIONAL = "trf-internasional";
        public static final String MENU_BRIZZI = "brizzi";
        public static final String MENU_DOMPET_DIGITAL = "dompet-digital";
        public static final String MENU_PULSA = "pulsa-data";
        public static final String MENU_ASURANSI = "asuransi";
        public static final String MENU_BRIVA = "briva";
        public static final String MENU_CICILAN = "cicilan";
        public static final String MENU_KARTU_KREDIT = "kartu-kredit";

//        public static final String MENU_LOAN_IN_APP = "loan-in-app";
        public static final String MENU_LISTRIK = "listrik";
        public static final String MENU_LTMPT = "ltmpt";
        public static final String MENU_PASCA_BAYAR = "pascabayar";
        public static final String MENU_PDAM = "pdam";
        public static final String MENU_TELKOM = "telkom";
        public static final String MENU_TV = "tv-kabel";

        public static final String MENU_DEPOSITO = "deposito";
        public static final String MENU_DPLK = "dplk";
        public static final String MENU_RDN = "rdn";
        public static final String MENU_BPJS = "bpjs";
        public static final String MENU_DONASI = "donasi";
        public static final String MENU_KAI = "kai";
        public static final String MENU_TRAVEL = "travel";
        public static final String MENU_MPN = "MPN";
        public static final String MENU_CERIA = "ceria";
        public static final String MENU_PINJAMAN_BRI = "pinjaman";
        public static final String MENU_BANK_RAYA = "bank-raya";
        public static final String MENU_QR_TRANSFER = "qr-transfer";
        public static final String MENU_BRISPOT = "brispot";
        public static final String MENU_SETOR_TUNAI = "setor-tunai";
        public static final String MENU_STREAMING = "streaming";
        public static final String MENU_KARTU_KREDIT_BRI = "cc-bri";
        public static final String MENU_ESBN = "e-sbn";
        public static final String MENU_PENDIDIKAN = "pendidikan";
        public static final String MENU_IBBIZ = "ibbiz";
        public static final String MENU_BRI_FINANCE = "brifinance";
        public static final String MENU_PBB = "pajak-pbb";
        public static final String MENU_BRIGHTS = "brights";
        public static final String MENU_SNPMB = "ltmpt";
        public static final String MENU_JADI_MERCHANT = "jadi-merchant";
        public static final String MENU_VOUCHER_GAME = "game";
        public static final String MENU_EMAS = "emas";
        public static final String MENU_KSEI = "ksei";

        public static final String MENU_RENCANA = "rencana";

        public static final String MENU_NFC = "nfc-payment";
    }


    /**
     * class ini digunakan utk mapping code menu travel
     */
    public static class MenuTravelId {
        public static final String INJOURNEY_ID = "00000009";
        public static final String KCIC_ID = "00000010";
        public static final String PESAWAT_ID = "00000011";
    }

    public static class ProfileMenuIcon {
        public static final String IC_FAST_MENU = "ic_fast_menu";
        public static final String IC_UPDATE_REK = "ic_card_search";
        public static final String IC_KELOLA_KARTU = "ic_card_search";
        public static final String IC_VOICE_ASSISTANT = "ic_microphone_grey";
        public static final String IC_UBAH_PIN = "ic_ganti_pass";
        public static final String IC_UBAH_KATAKUNCI = "ic_lock";
        public static final String IC_LOGIN_BIOMETRIC = "ic_fingerprint_grey";
        public static final String IC_PUSAT_BANTUAN = "ic_help_info";
        public static final String IC_CHAT_BANKING = "ic_square_chat";
        public static final String IC_LAYANAN_BEBAS_PULSA = "ic_call_grey";
        public static final String IC_KONTAK_KAMI = "ic_group_kontak";
        public static final String IC_JENIS_DAN_LIMIT = "ic_finance_bill";
        public static final String IC_INFO_KURS = "ic_investasi_kurs";
        public static final String IC_INFO_SAHAM = "ic_poll_saham";
        public static final String IC_LOKASI_ATM = "ic_location_info";
        public static final String IC_LOKASI_KANTOR = "ic_building_grey";
        public static final String IC_TNC = "ic_finance_bill";

    }

    //need to be review
    public static List<FastMenu> fetchFastMenu() {
        List<FastMenu> menuModels = new ArrayList<>();

        menuModels.add(new FastMenu(Constant.MENU_QR_MCM, "KQ", new LanguageModel(MenuNameId.MENU_QRIS,MenuNameEn.MENU_QRIS), "ic_menu_qna_pembelian_qris", MENU_QR_MCM, TAG_MENU_LAINNYA, true));
        menuModels.add(new FastMenu(MenuId.MENU_NFC, "NF", new LanguageModel(MenuNameId.MENU_NFC,MenuNameEn.MENU_NFC), "ic_nfc", MENU_NFC, TAG_MENU_LAINNYA, true));
        menuModels.add(new FastMenu(Constant.MENU_BRIZZI, "BR", new LanguageModel(MenuNameId.MENU_BRIZZI,MenuNameEn.MENU_BRIZZI), "ic_menu_qna_brizzi", MENU_BRIZZI, TAG_MENU_LAINNYA, true));
        menuModels.add(new FastMenu(Constant.MENU_DOMPET_DIGITAL, "DD", new LanguageModel(MenuNameId.MENU_E_WALLET,MenuNameEn.MENU_E_WALLET), "ic_menu_qna_e_wallet", MENU_TOP_UP_WALLET, TAG_MENU_LAINNYA, true));
        menuModels.add(new FastMenu(Constant.MENU_BRIVA, "BV", new LanguageModel(MenuNameId.MENU_BRIVA,MenuNameEn.MENU_BRIVA), "ic_menu_qna_briva", MENU_BRIVA, TAG_MENU_LAINNYA, true));
        menuModels.add(new FastMenu(Constant.MENU_TRANSFER, "TR", new LanguageModel(MenuNameId.MENU_TRANSFER,MenuNameEn.MENU_TRANSFER), "ic_menu_qna_transfer", MENU_TRANSFER, TAG_MENU_LAINNYA, true));
        menuModels.add(new FastMenu(Constant.MENU_PULSA, "PD",  new LanguageModel(MenuNameId.MENU_PULSA_DATA,MenuNameId.MENU_PULSA_DATA), "ic_menu_qna_pulsa", MENU_PULSA, TAG_MENU_LAINNYA, true));

        return menuModels;
    }

    //need to be review
    public static List<FastMenuDefault> fetchFastMenuDefault() {
        List<FastMenuDefault> menuModels = new ArrayList<>();

        menuModels.add(new FastMenuDefault(Constant.MENU_QR_MCM, "KQ", new LanguageModel(MenuNameId.MENU_QRIS, MenuNameEn.MENU_QRIS), "ic_menu_qna_pembelian_qris", MENU_QR_MCM, TAG_MENU_LAINNYA, true, 0, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_BRIZZI, "BR", new LanguageModel(MenuNameId.MENU_BRIZZI, MenuNameEn.MENU_BRIZZI), "ic_menu_qna_brizzi", MENU_BRIZZI, TAG_MENU_LAINNYA, true, 1, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_DOMPET_DIGITAL, "DD", new LanguageModel(MenuNameId.MENU_E_WALLET, MenuNameEn.MENU_E_WALLET), "ic_menu_qna_e_wallet", MENU_TOP_UP_WALLET, TAG_MENU_LAINNYA, true, 2, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_BRIVA, "BV", new LanguageModel(MenuNameId.MENU_BRIVA, MenuNameEn.MENU_BRIVA), "ic_menu_qna_briva", MENU_BRIVA, TAG_MENU_LAINNYA, true, 3, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_TRANSFER, "TR", new LanguageModel(MenuNameId.MENU_TRANSFER, MenuNameEn.MENU_TRANSFER), "ic_menu_qna_transfer", MENU_TRANSFER, TAG_MENU_LAINNYA, true, 4, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_PULSA, "PD", new LanguageModel(MenuNameId.MENU_PULSA_DATA,MenuNameEn.MENU_PULSA_DATA), "ic_menu_qna_pulsa", MENU_PULSA, TAG_MENU_LAINNYA, true, 5, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_PFM, "CK",new LanguageModel(MenuNameId.MENU_CATATAN_KEUANGAN,MenuNameEn.MENU_CATATAN_KEUANGAN),"ic_menu_qna_pfm", MenuConfig.MENU_PFM, MenuConfig.TAG_MENU_LAINNYA, true, -1, false, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_PROMO, "PR", new LanguageModel(MenuNameId.MENU_PROMO,MenuNameEn.MENU_PROMO), "promo", MenuConfig.MENU_PROMO, MenuConfig.TAG_MENU_LAINNYA, true, -1, false, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_SETOR_TUNAI, "ST",new LanguageModel(MenuNameId.MENU_SETOR_TUNAI,MenuNameEn.MENU_SETOR_TUNAI), "ic_menu_qna_setor_tunai", MENU_SETOR_TUNAI, TAG_MENU_LAINNYA, true, -1, false, true));

        return menuModels;
    }

    //need to be review
    public static List<FastMenuDefault> fetchFastMenuDefaultNewSkin() {
        List<FastMenuDefault> menuModels = new ArrayList<>();

        menuModels.add(new FastMenuDefault(Constant.MENU_BRIVA, "BV", new LanguageModel(MenuNameId.MENU_BRIVA, MenuNameEn.MENU_BRIVA), "ic_fm_briva", MENU_BRIVA, TAG_MENU_LAINNYA, true, 0, true, true));
//        menuModels.add(new FastMenuDefault(Constant.MENU_BRIZZI, "BR", new LanguageModel(MenuNameId.MENU_BRIZZI, MenuNameEn.MENU_BRIZZI), "ic_fm_brizzi", MENU_BRIZZI, TAG_MENU_LAINNYA, true, 0, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_DOMPET_DIGITAL, "DD", new LanguageModel(MenuNameId.MENU_E_WALLET, MenuNameEn.MENU_E_WALLET), "ic_fm_topup", MENU_TOP_UP_WALLET, TAG_MENU_LAINNYA, true, 1, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_QR_MCM, "KQ", new LanguageModel(MenuNameId.MENU_QRIS, MenuNameEn.MENU_QRIS), "ic_fm_qris", MENU_QR_MCM, TAG_MENU_LAINNYA, true, 2, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_TRANSFER, "TR", new LanguageModel(MenuNameId.MENU_TRANSFER, MenuNameEn.MENU_TRANSFER), "ic_fm_transfer", MENU_TRANSFER, TAG_MENU_LAINNYA, true, 3, true, true));
        menuModels.add(new FastMenuDefault(Constant.MENU_PULSA, "PD", new LanguageModel(MenuNameId.MENU_PULSA_DATA,MenuNameEn.MENU_PULSA_DATA), "ic_fm_pulsa", MENU_PULSA, TAG_MENU_LAINNYA, true, 4, true, true));

        menuModels.add(new FastMenuDefault(Constant.MENU_PFM, "CK",new LanguageModel(MenuNameId.MENU_CATATAN_KEUANGAN,MenuNameEn.MENU_CATATAN_KEUANGAN),"ic_menu_qna_pfm", MenuConfig.MENU_PFM, MenuConfig.TAG_MENU_LAINNYA, true, 6, false, false));
        menuModels.add(new FastMenuDefault(Constant.MENU_SETOR_TUNAI, "ST",new LanguageModel(MenuNameId.MENU_SETOR_TUNAI,MenuNameEn.MENU_SETOR_TUNAI), "ic_fm_setor_tarik", MENU_SETOR_TUNAI, TAG_MENU_LAINNYA, true, 7, false, false));

        return menuModels;
    }

    public static List<FastMenuDefault> fetchFastMenuWithNfcMenu(boolean isNfcAvailable) {
        List<FastMenuDefault> newFastMenuList = new ArrayList<>(fetchFastMenuDefaultNewSkin());
        if (isNfcAvailable) {
//            newFastMenuList.add(new FastMenuDefault(MenuId.MENU_NFC, "NF", new LanguageModel(MenuNameId.MENU_NFC,MenuNameEn.MENU_NFC), "ic_nfc", MENU_NFC, TAG_MENU_LAINNYA, true, 0, false, true));
            newFastMenuList.add(new FastMenuDefault(MenuId.MENU_NFC, "NF", new LanguageModel(MenuNameId.MENU_NFC,MenuNameEn.MENU_NFC), "ic_nfc", MENU_NFC, TAG_MENU_LAINNYA, true, 8, false, false));
        }
        return newFastMenuList;
    }

    public static List<MenuDashFav> fetchMenuDashFav() {
        List<MenuDashFav> menuDashFavList = new ArrayList<>();
        menuDashFavList.add(new MenuDashFav(Constant.MENU_PULSA, "PD", "Pulsa/Data", "ic_menu_qna_pulsa", MENU_PULSA, true, false, 2, "Top Up", ""));
        menuDashFavList.add(new MenuDashFav(Constant.MENU_TRANSFER, "TR", "Transfer", "ic_menu_qna_transfer", MENU_TRANSFER, true, false, 1, "Transfer dan Tarik Tunai", ""));
        menuDashFavList.add(new MenuDashFav(Constant.MENU_DOMPET_DIGITAL, "DD", "E Wallet", "ic_menu_qna_e_wallet", MENU_TOP_UP_WALLET, true, false, 2, "Top Up", ""));
        menuDashFavList.add(new MenuDashFav(Constant.MENU_BRIZZI, "BR", "BRIZZI", "ic_menu_qna_brizzi", MENU_BRIZZI, true, false, 2, "Top Up", ""));
        menuDashFavList.add(new MenuDashFav(Constant.MENU_TARIK_TUNAI, "TT", GeneralHelper.getString(R.string.txt_tarik_tunai), "ic_menu_qna_tariktunai", MENU_TARIK_TUNAI, true, false, 2, "Transfer dan Tarik Tunai", ""));
        menuDashFavList.add(new MenuDashFav(Constant.MENU_BRIVA, "BV", MenuNameId.MENU_BRIVA, "ic_menu_qna_briva", MenuNameEn.MENU_BRIVA, true, false, 3, "Tagihan", ""));
        menuDashFavList.add(new MenuDashFav(Constant.MENU_LISTRIK, "LS", "Listrik", "ic_menu_qna_pln", MENU_LISTRIK, true, false, 3, "Tagihan", ""));
        menuDashFavList.add(new MenuDashFav(Constant.MENU_LAINNYA, "LL", "Lainnya", "ic_menu_lain", MENU_LAINNYA, true, false, 0, "Fitur Favorit", ""));
        return menuDashFavList;
    }

    //need to be review
    public static List<MenuKategori> getDefaultMenuKategori() {
        List<MenuKategori> menuKategoriList = new ArrayList<>();
        menuKategoriList.add(new MenuKategori(MenuKategoriId.TOP_UP, new LanguageModel( MenuNameId.MENU_TOP_UP,  MenuNameEn.MENU_TOP_UP), "Top Up adalah", "ic_kategori_topup", NewStatus.OLD, 1, "top up,dompet digital,e wallet"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.TAGIHAN, new LanguageModel( MenuNameId.MENU_TAGIHAN, MenuNameEn.MENU_TAGIHAN) , "Bayar adalah", "ic_kategori_bayar", NewStatus.OLD, 2, "tagihan,pln,listrik"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.SETOR_TARIK,  new LanguageModel( MenuNameId.MENU_SETOR_TRAIK_TUNAI, MenuNameEn.MENU_SETOR_TRAIK_TUNAI), "Setor adalah", "ic_kategori_tarik", NewStatus.OLD, 3, "setor,tarik,atm"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.LIFE_STYLE, new LanguageModel( MenuNameId.MENU_LIFESTYLE, MenuNameEn.MENU_LIFESTYLE), "Lifestyle adalah gaya hidup", "ic_kategori_lifestyle", NewStatus.OLD, 4, "gaya"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.QRIS_TRANSFER, new LanguageModel( MenuNameId.MENU_QR_TRANSFER,  MenuNameEn.MENU_QR_TRANSFER), "QRIS Transfer adalah", "ic_kategori_qr_trf", NewStatus.OLD, 5, "setor,tarik,atm"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.DEBIT, new LanguageModel( MenuNameId.MENU_VIRTUAL_DEBIT,  MenuNameEn.MENU_VIRTUAL_DEBIT), "Debit adalah", "ic_kategori_debit", NewStatus.OLD, 6, "debit,kartu,virtual"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.CATATAN_KEU, new LanguageModel( MenuNameId.MENU_CATATAN_KEUANGAN,MenuNameEn.MENU_CATATAN_KEUANGAN), "Catatan Keuangan adalah gaya hidup", "ic_kategori_pfm", NewStatus.OLD, 7, "pfm,catatan keuangan"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.INVESTASI, new LanguageModel( MenuNameId.MENU_INVESTASI, MenuNameEn.MENU_INVESTASI),"Investasi", "ic_kategori_investasi", NewStatus.OLD, 8, "investasi,sbn"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.DONASI,new LanguageModel( MenuNameId.MENU_DONASI,MenuNameEn.MENU_DONASI),  "Donasi adalah", "ic_kategori_donasi", NewStatus.OLD, 9, "donasi"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.TRANSFER_VALAS,new LanguageModel( MenuNameId.MENU_KONVERSI_VALAS,MenuNameEn.MENU_KONVERSI_VALAS),  "Transfer Valas adalah", "ic_menu_qna_konversi_valas", NewStatus.OLD, 9, "konversi valas"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.KARTU_KREDIT, new LanguageModel( MenuNameId.MENU_KARTU_KREDIT,MenuNameEn.MENU_KARTU_KREDIT),     "Kartu Kredit", "ic_category_cc", NewStatus.OLD, 10, "kartu kredit,credit card"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.ASURANSI, new LanguageModel( MenuNameId.MENU_ASURANSI, MenuNameEn.MENU_ASURANSI), "Asuransi adalah", "ic_kategori_asuransi", NewStatus.OLD, 11, "asuransi,brins,brilife"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.PINJAMAN, new LanguageModel( MenuNameId.MENU_PINJAMAN, MenuNameEn.MENU_PINJAMAN),  "Pinjaman adalah", "ic_kategori_pinjaman", NewStatus.OLD, 12, "briguna,pinjaman,pembayaran"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.SPLITBILL, new LanguageModel( MenuNameId.MENU_SPLIT_BILL, MenuNameEn.MENU_SPLIT_BILL ),  "Split Bill adalah", "ic_kategori_splitbill", NewStatus.NEW, 13, "split, bill, bagi, tagihan,  patungan, bayar, rata, hitung, perorang, pisah, share"));
        menuKategoriList.add(new MenuKategori(MenuKategoriId.LAINNYA, new LanguageModel( MenuNameId.MENU_PRODUK_BRI_LAINYA, MenuNameEn.MENU_PRODUK_BRI_LAINYA ),  "Lainnya adalah", "ic_kategori_lainnya", NewStatus.OLD, 14, "dplk,pinjaman,pembayaran"));

        return menuKategoriList;
    }


    public static List<KategoriMenuModel> getDefaultMenu(Context context) {
        List<KategoriMenuModel> mListKategori = new ArrayList<>();
        mListKategori.add(new KategoriMenuModel("ic_transfer_new", MenuId.MENU_TRANSFER, 1, false, true, "TF", context.getString(R.string.transfer_title_bar), ""));
        mListKategori.add(new KategoriMenuModel("ic_menu_qna_briva", MenuId.MENU_BRIVA, 1, false, true, "TF", context.getString(R.string.briva_title_bar), ""));
        mListKategori.add(new KategoriMenuModel("ic_menu_qna_topup", MenuId.MENU_DOMPET_DIGITAL, 1, false, true, "TF", context.getString(R.string.ewallet), ""));
        mListKategori.add(new KategoriMenuModel("ic_menu_qna_pulsa", MenuId.MENU_PULSA, 1, false, true, "TF", context.getString(R.string.pulsa_toolbar_title), ""));

        return mListKategori;
    }

    public static List<KategoriMenuModel> getMenuSearch() {
        List<KategoriMenuModel> mListKategori = new ArrayList<>();
        mListKategori.add(new KategoriMenuModel("", 0, 1, false, true, "TF", "Cari Fitur", ""));
        mListKategori.add(new KategoriMenuModel("", MenuId.MENU_DOMPET_DIGITAL, 1, false, true, "TF", "Top Up Gopay", ""));
        mListKategori.add(new KategoriMenuModel("", MenuId.MENU_PULSA, 1, false, true, "TF", "Beli Pulsa", ""));
        mListKategori.add(new KategoriMenuModel("", MenuId.MENU_BRIZZI, 1, false, true, "TF", "Top Up BRIZZI", ""));

        return mListKategori;
    }


    public static ArrayList<ProfileInfoResp> getMenuProfileRevamp(String bioType, Boolean bioChange) {
        List<ListInfoProfile> listPengaturan = new ArrayList<>();
        listPengaturan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.FASTMENU, "ic_fast_menu", GeneralHelper.getString(R.string.fast_menu), "action", false));
        listPengaturan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.UPDATE_REKENING, "ic_card_search", GeneralHelper.getString(R.string.update_account), "action", false));
        listPengaturan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.PENGELOLAAN_KARTU, "ic_kelola_kartu", GeneralHelper.getString(R.string.pengelolaan_kartu), "action", false));
        //listPengaturan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.QRIS, "ic_sumber_dana_qris", GeneralHelper.getString(R.string.txt_sumber_dana_qris), "action", false));
        listPengaturan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.LANGUAGE, "ic_change_language", GeneralHelper.getString(R.string.txt_language), "action", false));

        List<ListInfoProfile> listKeamanan = new ArrayList<>();
        listKeamanan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.UBAH_PIN, "ic_ganti_pass", GeneralHelper.getString(R.string.ubah_pin), "action", false));
        listKeamanan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.UBAH_KATA_KUNCI, "ic_lock", GeneralHelper.getString(R.string.ubah_password), "action", false));
        if (TRUE == GeneralHelper.checkBiometricSupport()) {
            listKeamanan.add(new ListInfoProfile(Constant.ProfileInfoRevamp.BIOMETRIC, "ic_fingerprint_grey", String.format(GeneralHelper.getString(R.string.login_fingerprint), "Biometrik"/*bioType*/), "switch", bioChange));
        }

        List<ListInfoProfile> listKontakBRI = new ArrayList<>();
        listKontakBRI.add(new ListInfoProfile(Constant.ProfileInfoRevamp.PUSAT_BANTUAN, "ic_help_info", GeneralHelper.getString(R.string.help_center), "action", false));
        listKontakBRI.add(new ListInfoProfile(Constant.ProfileInfoRevamp.CHAT_BANKING, "ic_square_chat", GeneralHelper.getString(R.string.chat_banking), "action", false));
        //listKontakBRI.add(new ListInfoProfile(Constant.ProfileInfoRevamp.LAYANAN_BEBAS_PULSA, "ic_call_grey", GeneralHelper.getString(R.string.layanan_bebas_pulsa), "action", false));
        listKontakBRI.add(new ListInfoProfile(Constant.ProfileInfoRevamp.KONTAK_KAMI, "ic_group_kontak", GeneralHelper.getString(R.string.contact_us), "action", false));

        List<ListInfoProfile> listInformasi = new ArrayList<>();
        //listInformasi.add(new ListInfoProfile(Constant.ProfileInfoRevamp.JENIS_DAN_LIMIT, "ic_limit_trx", GeneralHelper.getString(R.string.limit_information_toolbar), "action", false));
        //listInformasi.add(new ListInfoProfile(Constant.ProfileInfoRevamp.INFO_KURS, "ic_info_kurs", GeneralHelper.getString(R.string.rate_info), "action", false));
        //listInformasi.add(new ListInfoProfile(Constant.ProfileInfoRevamp.INFO_SAHAM, "ic_poll_saham", GeneralHelper.getString(R.string.stock_info_bri), "action", false));
        //listInformasi.add(new ListInfoProfile(Constant.ProfileInfoRevamp.LOKASI_ATM, "ic_location_atm", GeneralHelper.getString(R.string.atm_location_bri), "action", false));
        //listInformasi.add(new ListInfoProfile(Constant.ProfileInfoRevamp.LOKASI_KANTOR, "ic_building_grey", GeneralHelper.getString(R.string.office_location_bri), "action", false));
        listInformasi.add(new ListInfoProfile(Constant.ProfileInfoRevamp.TENTANG_BRIMO, "ic_info_brimo", GeneralHelper.getString(R.string.text_about), "action", false));
        listInformasi.add(new ListInfoProfile(Constant.ProfileInfoRevamp.LOGOUT, "ic_logout_info", GeneralHelper.getString(R.string.text_logout), "action", false));

        ArrayList<ProfileInfoResp> infoProfile = new ArrayList<>();
        infoProfile.add(new ProfileInfoResp(GeneralHelper.getString(R.string.setting), listPengaturan));
        infoProfile.add(new ProfileInfoResp(GeneralHelper.getString(R.string.security), listKeamanan));
        infoProfile.add(new ProfileInfoResp(GeneralHelper.getString(R.string.kontak_bri), listKontakBRI));
        infoProfile.add(new ProfileInfoResp(GeneralHelper.getString(R.string.information), listInformasi));

        return infoProfile;
    }


    public static List<MenuDashboard> fetchMenuDashboardAll() {
        List<MenuDashboard> menuDashAllList = new ArrayList<>();

        //dokumen mapping https://docs.google.com/spreadsheets/d/1guiJd3wQOFJHD2PzRV-AmUk9pTBuHHJvyVam6oxjYWA/edit#gid=0

        //Top Up
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BRIZZI, "BR", new LanguageModel( MenuNameId.MENU_BRIZZI, MenuNameEn.MENU_BRIZZI), MenuKategoriId.TOP_UP, "Top Up", "ic_menu_qna_brizzi", true, "topup, brizzi, emoney, top up", IdWaNotif.MENU_BRIZZI, false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_DOMPET_DIGITAL, "DD", new LanguageModel( MenuNameId.MENU_E_WALLET, MenuNameEn.MENU_E_WALLET), MenuKategoriId.TOP_UP, "Top Up", "ic_menu_qna_topup", true, "topup, gopay, linkaja, ovo, shopeepay, dana, isaku, dompet digital, wallet, top up shopeePay, e wallet, ewallet, top up", IdWaNotif.MENU_DOMPET_DIGITAL, false, 2, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PULSA, "PD", new LanguageModel( MenuNameId.MENU_PULSA_DATA, MenuNameEn.MENU_PULSA_DATA), MenuKategoriId.TOP_UP, "Top Up", "ic_menu_qna_pulsa", true, "topup, pulsa, paket data, xl, telkomsel, indosat, 3 tri, isi pulsa, beli data, top up", IdWaNotif.MENU_PULSA, false, 3, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TOPUP_LISTRIK, "LS", new LanguageModel( MenuNameId.MENU_TOPUP_LISTRIK,MenuNameEn.MENU_TOPUP_LISTRIK), MenuKategoriId.TOP_UP, "Top Up", "ic_menu_qna_topup_listrik", false, "token, topup, token listrik, beli token, isi ulang listrik, top up token, top up listrik, pln token, beli listrik, isi token listrik, pulsa listrik, listrik prabayar", IdWaNotif.MENU_LISTRIK, false, 4, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TOPUP_VOUCHER_GAME, "VG", new LanguageModel(MenuNameId.MENU_TOPUP_VOUCHER_GAME,MenuNameEn.MENU_TOPUP_VOUCHER_GAME), MenuKategoriId.TOP_UP, "Top Up", "ic_menu_qna_topup_game", false, "voucher, voucher game, topup game, top up game, isi saldo game, beli voucher game, isi ulang game, game voucher code, lifestyle, games, permainan, unipin, playstation gift, store, card, mobile legend bang bang, mlbb, googleplay, google, google, play, steam, wallet, idr, beli, topup, top up, psn, saldo kartu, hadiah", "", false, 5, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TOPUP_STREAMING, "VS", new LanguageModel(MenuNameId.MENU_TOPUP_STREAMING,MenuNameEn.MENU_TOPUP_STREAMING), MenuKategoriId.TOP_UP, "Top Up", "ic_menu_qna_topup_streaming", false, "voucher, voucher streaming, topup streaming, top up streaming, beli voucher streaming, langganan streaming, isi ulang streaming, lifestyle, vidio, netflix, spotify, youtube, music, video, subscription, langganan, mola, molatv, wetv", "", false, 6, "", ""));
        //Tagihan
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BRIVA, "BV", new LanguageModel( MenuNameId.MENU_BRIVA, MenuNameEn.MENU_BRIVA), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_briva", true, "tagihan, briva, virtual account, bayar, bayar briva, tokped, toped, tokopedia, shopee, bukalapak, lazada, topup briva, virtual account, belanja, bayar, bayar briva, top up", IdWaNotif.MENU_BRIVA, false, 4, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_LISTRIK, "LS", new LanguageModel( MenuNameId.MENU_LISTRIK,MenuNameEn.MENU_LISTRIK), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_pln", false, "tagihan, listrik, pln, pulsa, taglis, non taglis, pln prabayar", IdWaNotif.MENU_LISTRIK, false, 5, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BPJS, "BP", new LanguageModel( MenuNameId.MENU_BPJS,MenuNameEn.MENU_BPJS), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_bpjs", false, "tagihan, bpjs, asuransi, kesehatan, bayar, bpjs tk", IdWaNotif.MENU_BPJS, false, 6, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KARTU_KREDIT, "KK", new LanguageModel(MenuNameId.MENU_KARTU_KREDIT,MenuNameEn.MENU_KARTU_KREDIT), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_creditcard", false, "bayar, cc, kartu kredit, tagihan, visa, mastercard, kredit, credit card, bayar kartu kredit", IdWaNotif.MENU_KARTU_KREDIT, false, 7, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_CICILAN, "CC", new LanguageModel(MenuNameId.MENU_CICILAN,MenuNameEn.MENU_CICILAN), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_cicilan", true, "tagihan, cicilan, baf, fif, oto, wom, bri finance, angsuran, multifinance, kreditplus, suzuki, mega", IdWaNotif.MENU_CICILAN, false, 8, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KAI, "KA", new LanguageModel(MenuNameId.MENU_KAI,MenuNameEn.MENU_KAI), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_kai", false, "tagihan, kai, kereta api, sepur, tiket, karcis, travel", IdWaNotif.MENU_KAI, false, 9, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PDAM, "PM", new LanguageModel(MenuNameId.MENU_PDAM,MenuNameEn.MENU_PDAM), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_pdam", false, "tagihan, pdam, air, bayar, pam, aetra", IdWaNotif.MENU_PDAM, false, 10, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PENDIDIKAN, "PK", new LanguageModel( MenuNameId.MENU_PENDIDIKAN,MenuNameEn.MENU_PENDIDIKAN), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_pendidikan", false, "tagihan, pendidikan, education, edukasi, spp, ukt, semester, universitas, kampus, institut", IdWaNotif.MENU_PENDIDIKAN, false, 11, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TV, "KI", new LanguageModel(MenuNameId.MENU_TV_KABEL_INTERNET,MenuNameEn.MENU_TV_KABEL_INTERNET), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_television", false, "tagihan, tv, internet, mnc vision, transvision, big tv, nexmedia, myrepublic retail, indosat gig, biznet, firstmedia, kabel, mnc, indihome, telkom", IdWaNotif.MENU_TV, false, 12, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_ASURANSI, "AS", new LanguageModel(MenuNameId.MENU_ASURANSI,MenuNameEn.MENU_ASURANSI), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_asuransi", false, "allianz life, brilife, acci, care, insurance, prudential, asuransi, tagihan, mikro, asmik, kendaraan, jiwa, brins, rumah, tempat, usaha, tinggal, sepeda, mobil, tagihan, allianz life, brins, insurance, prudential, asuransi, bri, polis, premi, pemulihan, spaj, cetak", IdWaNotif.MENU_ASURANSI, false, 13, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_MPN, "MP", new LanguageModel(MenuNameId.MENU_PENERIMAAN_NEGARA,MenuNameEn.MENU_PENERIMAAN_NEGARA), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_mpn", false, "tagihan, penerimaan negara, mpn, pajak, retribusi, passport, perseroan, perorangan, sbn, kua, bayar", IdWaNotif.MENU_MPN, false, 14, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PBB, "PP", new LanguageModel(MenuNameId.MENU_PBB,MenuNameEn.MENU_PBB), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_pbb", false, "tagihan, pbb, himpunan, pajak, bumi, tanah, bangunan, gedung, rumah, bayar, retribusi", IdWaNotif.MENU_PBB, false, 15, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PASCA_BAYAR, "PB", new LanguageModel(MenuNameId.MENU_PASCA_BAYAR,MenuNameEn.MENU_PASCA_BAYAR), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_pascabayar", false, "tagihan, pascabayar, postpaid, xl, telkomsel", IdWaNotif.MENU_PASCA_BAYAR, false, 16, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_SNPMB, "SN", new LanguageModel(MenuNameId.MENU_SNPMB,MenuNameEn.MENU_SNPMB), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_snpmb", false, "tagihan, bayar, kuliah, ltmpt, tagihan, pendidikan, universitas, perguruan tinggi, education, edukasi, snpmb", IdWaNotif.MENU_SNPMB, false, 17, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TELKOM, "TP", new LanguageModel(MenuNameId.MENU_TELKOM,MenuNameEn.MENU_TELKOM), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_telkom", false, "tagihan, telkom, indihome, bayar, telpon rumah, telepon, telfon, speedy, internet", IdWaNotif.MENU_TELKOM, false, 18, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_SIGNAL, "SG", new LanguageModel(MenuNameId.MENU_SIGNAL,MenuNameEn.MENU_SIGNAL), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_signal", false, "tagihan, samsat, pajak, kendaraan, signal, motor, mobil", "", false, 19, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PAJAK_HOREKA, "PH", new LanguageModel(MenuNameId.MENU_PAJAK_DAERAH,MenuNameEn.MENU_PAJAK_DAERAH), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_pajak_daerah", false, "Pajak, Daerah, Horeka, Wilayah, Kota, Kabupaten, Hotel, Restoran, Hiburan, Kafe, Retribusi Daerah, Retribusi, Parkir, Parking", "", false, 61, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PROPERTY, "IP", new LanguageModel(MenuNameId.MENU_IPL_PROPERTI,MenuNameEn.MENU_IPL_PROPERTI), MenuKategoriId.TAGIHAN, "Tagihan", "ic_menu_qna_ipl_property", false, "", "", false, 64, "", ""));
//Tarik Setor
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_SETOR_TUNAI, "ST", new LanguageModel(MenuNameId.MENU_SETOR_TUNAI,MenuNameEn.MENU_SETOR_TUNAI) , MenuKategoriId.SETOR_TARIK, "Setor &\nTarik Tunai", "ic_menu_qna_setor_tunai", true, "setor, tunai, uang, setun, crm, cash, atm", IdWaNotif.MENU_SETOR_TUNAI, false, 20, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TARIK_TUNAI, "TT", new LanguageModel(MenuNameId.MENU_TARIK_TUNAI,MenuNameEn.MENU_TARIK_TUNAI) , MenuKategoriId.SETOR_TARIK, "Setor &\nTarik Tunai", "ic_menu_qna_tariktunai", false, "tarik tunai, atm, transfer, tartun, uang, brilink, merchant", IdWaNotif.MENU_TARIK_TUNAI, false, 21, "", ""));
//VDC
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_DEBIT_VIRTUAL, "DV", new LanguageModel(MenuNameId.MENU_KARTU_DEBIT_VIRTUAL,MenuNameEn.MENU_KARTU_DEBIT_VIRTUAL) , MenuKategoriId.DEBIT, "Kartu\nDebit Virtual", "ic_kategori_debit", false, "kartu, debit, virtual, online, card, merchant", "", false, 22, "", ""));
//Investasi
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_DEPOSITO, "DO", new LanguageModel(MenuNameId.MENU_DEPOSITO,MenuNameEn.MENU_DEPOSITO) , MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_deposito_revamp", false, "investasi, deposito, tabungan, berjangka, deposit", IdWaNotif.MENU_DEPOSITO, false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_DPLK, "DB", new LanguageModel(MenuNameId.MENU_DPLK_BRI,MenuNameEn.MENU_DPLK_BRI), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_icon_dplk", false, "investasi, dplk, dana pensiun, tabungan, topup, brifine, buka brifine, top up", IdWaNotif.MENU_DPLK, true, 2, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_RDN, "RD", new LanguageModel(MenuNameId.MENU_RDN,MenuNameEn.MENU_RDN), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_rdn", false, "investasi, rdn, rekening dana nasabah, saham, topup, buka, top up", IdWaNotif.MENU_RDN, false, 3, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_ESBN, "SB", new LanguageModel(MenuNameId.MENU_SBN,MenuNameEn.MENU_SBN), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_esbn", false, "investasi, sbn, surat berharga negara, esbn, e-sbn, primary bonds, sukuk, ori", IdWaNotif.MENU_ESBN, false, 4, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BRIGHTS, "BI", new LanguageModel(MenuNameId.MENU_BRIGHTS,MenuNameEn.MENU_BRIGHTS), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_brights", false, "investasi, saham, reksadana, danareksa, brights, brids", IdWaNotif.MENU_BRIGHTS, false, 6, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_EMAS, "EM", new LanguageModel(MenuNameId.MENU_EMAS,MenuNameEn.MENU_EMAS), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_emas", false, "investasi, emas, pegadaian, tabungan, investasi, gold", IdWaNotif.MENU_EMAS, false, 7, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KSEI, "KS", new LanguageModel(MenuNameId.MENU_PORTOFOLIO_KSEI,MenuNameEn.MENU_PORTOFOLIO_KSEI), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_ksei", false, "investasi, ksei, ekuitas, obligasi, saham", IdWaNotif.MENU_KSEI, false, 8, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KSEI, "KS", new LanguageModel(MenuNameId.MENU_PORTOFOLIO_KSEI,MenuNameEn.MENU_PORTOFOLIO_KSEI), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_ksei", false, "investasi, ksei, ekuitas, obligasi, saham", IdWaNotif.MENU_KSEI, false, 8, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_DASHBOARD_INVESTASI, "DI", new LanguageModel(MenuNameId.MENU_DASHBOARD_INVESTASI,MenuNameEn.MENU_DASHBOARD_INVESTASI), MenuKategoriId.INVESTASI, "Investasi", "ic_kategori_investasi", false, "dashboard, investasi, dashboard investasi, portofolio, aset", IdWaNotif.MENU_KSEI, false, 8, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_RENCANA, "RC", new LanguageModel(MenuNameId.MENU_RENCANA,MenuNameEn.MENU_RENCANA), MenuKategoriId.INVESTASI,"Investasi","ic_menu_qna_rencana", false, "", IdWaNotif.MENU_RENCANA, false, 8,"",""));
//Lifestyle
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_VOUCHER_GAME, "VG", new LanguageModel(MenuNameId.MENU_VOUCHER_GAME,MenuNameEn.MENU_VOUCHER_GAME), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_voucher_game", false, "lifestyle, voucher, games, permainan, unipin, playstation gift, store, card, mobile legend bang bang, mlbb, googleplay, google, google, play, steam, wallet, idr, beli, topup, top up, psn, saldo kartu, hadiah", "", false, 30, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_STREAMING, "VS", new LanguageModel(MenuNameId.MENU_VOUCHER_STREAMING,MenuNameEn.MENU_VOUCHER_STREAMING), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_voucher_streaming", false, "lifestyle, voucher, vidio, netflix, spotify, youtube, music, video, subscription, langganan, mola, molatv, wetv", "", false, 31, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_LIGA1, "BL", new LanguageModel(MenuNameId.MENU_BRI_LIGA_1 ,MenuNameEn.MENU_BRI_LIGA_1 ), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_bri_liga_1", false, "lifestyle, liga 1, sepak, bola, bri liga 1, pertandingan, sepak bola, match, soccer, football", "", false, 34, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BELANJA, "MB", new LanguageModel(MenuNameId.MENU_BELANJA_HARIAN  ,MenuNameEn.MENU_BELANJA_HARIAN), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_belanja_harian", false, "lifestyle, belanja, alfamart, groceries, shopping, harian, beli, jajan, alfagift", "", false, 34, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PESAWAT, "PS", new LanguageModel(MenuNameId.MENU_PESAWAT,MenuNameEn.MENU_PESAWAT), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_pesawat", false, "lifestyle, perjalanan, flight, tiket, pesawat, terbang, garuda, citilink, lion, batik", "", false, 34, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_WHOOSH, "WO", new LanguageModel(MenuNameId.MENU_WHOOSH,MenuNameEn.MENU_WHOOSH), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_whoosh", false, "lifestyle, perjalanan, kereta, kcic, whoosh, kereta cepat, cepat, high speed railway, kcjb, bandung, jakarta, kai", "", false, 34, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BUS, "BS", new LanguageModel(MenuNameId.MENU_BUS_SHUTTLE,MenuNameEn.MENU_BUS_SHUTTLE), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_bus", false, "lifestyle, perjalanan, bis, bus, shuttle, primajasa, sumber, restu", "", false, 34, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KERETA_API, "KI", new LanguageModel(MenuNameId.MENU_KERETA_API,MenuNameEn.MENU_KERETA_API), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_kereta_api", false, "lifestyle, perjalanan ,kai, kereta api, sepur, tiket, karcis", "", false, 34, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_INDIHOME, "IH", new LanguageModel(MenuNameId.MENU_LANGGANAN_INTERNET,MenuNameEn.MENU_LANGGANAN_INTERNET), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_indihome", false, "lifestyle, internet, indi, home, indihome, langganan, telkom, wifi", "", false, 34, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KIRIM_BARANG, "MK", new LanguageModel(MenuNameId.MENU_KIRIM_BARANG,MenuNameEn.MENU_KIRIM_BARANG), MenuKategoriId.LIFE_STYLE, "Lifestyle", "ic_menu_qna_kirim_barang", false, "lifestyle, kirim barang, pos, kirim, barang, paket, surat", "", false, 34, "", ""));
//Kartu Kredit
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KARTU_KREDIT_BRI, "KB", new LanguageModel(MenuNameId.MENU_KARTU_KREDIT_BRI,MenuNameEn.MENU_KARTU_KREDIT_BRI), MenuKategoriId.KARTU_KREDIT, "Kartu Kredit", "ic_menu_cc_sof", false, "kartu kredit, cc, produk bri, cicilan, credit card, kredit, lending", IdWaNotif.MENU_KARTU_KREDIT_BRI, false, 40, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BAYAR_KARTU_KREDIT, "YK", new LanguageModel(MenuNameId.MENU_BAYAR_KARTU_KREDIT,MenuNameEn.MENU_BAYAR_KARTU_KREDIT), MenuKategoriId.KARTU_KREDIT, "Kartu Kredit", "ic_menu_qna_creditcard_pay", false, "bayar, cc, kartu kredit, tagihan, visa, mastercard, kredit, credit card, bayar kartu kredit", IdWaNotif.MENU_KARTU_KREDIT, false, 41, "", ""));
//Lainnya
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_CERIA, "CR", new LanguageModel(MenuNameId.MENU_CERIA,MenuNameEn.MENU_CERIA), MenuKategoriId.LAINNYA, "Produk BRI", "ic_menu_qna_ceria", false, "ceria, produk bri, pinjaman, paylater", "", false, 51, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_QLOLA, "QL", new LanguageModel(MenuNameId.MENU_QLOLA,MenuNameEn.MENU_QLOLA), MenuKategoriId.LAINNYA, "Produk BRI", "ic_menu_qna_qlola", false, "produk bri, qlola, produk bri, bisnis, ibbiz", "", false, 52, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BRI_FINANCE, "BF", new LanguageModel(MenuNameId.MENU_GADAI_KENDARAAN,MenuNameEn.MENU_GADAI_KENDARAAN), MenuKategoriId.LAINNYA, "Produk BRI", "ic_menu_kkb_bri", false, "cicilan, pinjaman, kendaraan, gadai, brifinance, briflash, refinancing", "", false, 53, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_BANK_RAYA, "DR", new LanguageModel(MenuNameId.MENU_BUKA_TABUNGAN_RAYA ,MenuNameEn.MENU_BUKA_TABUNGAN_RAYA), MenuKategoriId.LAINNYA, "Produk BRI", "ic_menu_bank_raya", false, "tabungan raya, simpanan, produk bri", "", false, 54, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_QR_MERCHANT, "QM", new LanguageModel(MenuNameId.MENU_QR_PEDAGANG,MenuNameEn.MENU_QR_PEDAGANG), MenuKategoriId.LAINNYA, "Produk BRI", "ic_menu_qna_qr_merchant", false, "produk bri, qr pedagang, transfer, tarik tunai, pembayaran, scan", IdWaNotif.MENU_QR_MERCHANT, false, 55, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_INFO_LELANG, "IL", new LanguageModel(MenuNameId.MENU_INFO_LELANG,MenuNameEn.MENU_INFO_LELANG), MenuKategoriId.LAINNYA, "Produk BRI", "ic_menu_info_lelang", false, "produk bri, info lelang, lelang, bangunan, kendaraan, rumah, tanah, mobil", "", false, 56, "", ""));
//Non-Kategori
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_MUTASI, "MT", new LanguageModel(MenuNameId.MENU_MUTASI,MenuNameEn.MENU_MUTASI), MenuKategoriId.NON_KATEGORI, "", "ic_menu_qna_mutation", false, "mutasi,rekening koran,laporan keuangan,riwayat transaksi", "", false, 2, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TRANSFER, "TR", new LanguageModel(MenuNameId.MENU_TRANSFER,MenuNameEn.MENU_TRANSFER), MenuKategoriId.NON_KATEGORI, "", "ic_transfer_new", true, "transfer,bi fast,rtgs", IdWaNotif.MENU_TRANSFER, false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_TRANSFER_INTERNASIONAL, "TI", new LanguageModel(MenuNameId.MENU_TRANSFER_INTERNASIONAL,MenuNameEn.MENU_TRANSFER_INTERNASIONAL), MenuKategoriId.NON_KATEGORI, "", "ic_transfer_international_new", false, "transfer internasional, international, outgoing remittance, kirim uang, luar negeri, valas, asing, dollar, inggris", IdWaNotif.MENU_TRANSFER_INTERNASIONAL, false, 44, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PFM, "CK", new LanguageModel(MenuNameId.MENU_CATATAN_KEUANGAN,MenuNameEn.MENU_CATATAN_KEUANGAN), MenuKategoriId.NON_KATEGORI, "", "ic_menu_qna_pfm", true, "pfm, catatan, keuangan, laporan keuangan, mutasi, catatan keuangan", IdWaNotif.MENU_PFM, false, 56, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_DONASI, "ZD", new LanguageModel(MenuNameId.MENU_DONASI,MenuNameEn.MENU_DONASI), MenuKategoriId.NON_KATEGORI, "", "ic_kategori_donasi", false, "donasi, ybm brilian, dompet dhuafa, kasih, infaq, zakat, sedekah, infaq, qurban", IdWaNotif.MENU_DONASI, false, 57, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PINJAMAN_BRI, "PJ", new LanguageModel(MenuNameId.MENU_PINJAMAN,MenuNameEn.MENU_PINJAMAN), MenuKategoriId.NON_KATEGORI, "", "ic_kategori_pinjaman", false, "pinjaman, produk bri, briguna, hutang, kpr, mobil, daftar briguna, kredit, lending", IdWaNotif.MENU_PINJAMAN_BRI, false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KONVERSI_VALAS, "KV", new LanguageModel(MenuNameId.MENU_KONVERSI_VALAS,MenuNameEn.MENU_KONVERSI_VALAS), MenuKategoriId.NON_KATEGORI, "", "ic_menu_qna_konversi_valas", false, "konversi, valas, konversi valas, dollar, rupiah, exchange, rate, transfer valas", IdWaNotif.MENU_KONVERSI_VALAS, false, 5, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_QR_TRANSFER, "QT", new LanguageModel(MenuNameId.MENU_QR_TRANSFER ,MenuNameEn.MENU_QR_TRANSFER ), MenuKategoriId.NON_KATEGORI, "", "ic_kategori_qr_trf", false, "qris, transfer, qris transfer, terima, qris terima transfer", IdWaNotif.MENU_KONVERSI_VALAS, false, 5, "", ""));


        //Pengaturan
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_FM, "FM", new LanguageModel(MenuNameId.MENU_FASET_MENU, MenuNameEn.MENU_FASET_MENU), MenuKategoriId.INFO_AKUN_PENGATURAN, "Pengaturan", "ic_search_fast_menu", false, "fastmenu, fast, menu, Fast Menu, pengaturan, Setting", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_UPDATE_REKENING, "UR", new LanguageModel(MenuNameId.MENU_UPDATE_ACCOUNT, MenuNameEn.MENU_UPDATE_ACCOUNT), MenuKategoriId.INFO_AKUN_PENGATURAN, "Pengaturan", "ic_search_card_search", false, "update rekening, pembaruan, Update, Rekening, pengaturan, Setting", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PENGELOLAAN_KARTU, "CM", new LanguageModel(MenuNameId.MENU_PENGELOLAAN_KARTU, MenuNameEn.MENU_PENGELOLAAN_KARTU), MenuKategoriId.INFO_AKUN_PENGATURAN, "Pengaturan", "ic_search_kelola_kartu", false, "pengelolaan, kartu, manage, Card, Pengelolaan Kartu, pengaturan, Setting", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_SUMBER_DANA_QRIS, "SQ", new LanguageModel(MenuNameId.MENU_SUMBER_DANA_QRIS, MenuNameEn.MENU_SUMBER_DANA_QRIS), MenuKategoriId.INFO_AKUN_PENGATURAN, "Pengaturan", "ic_search_sumber_dana_qris", false, "Sumber, Dana, Qris, Sumber Dana, Dana Qris, Fund, Source, Source of Fund, Sumber Dana Qris, pengaturan, Setting", "", false, 1, "", ""));
        //Keamanan
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_UBAH_PIN, "CP",new LanguageModel(MenuNameId.MENU_UBAH_PIN ,MenuNameEn.MENU_UBAH_PIN ), MenuKategoriId.INFO_AKUN_KEAMANAN, "Keamanan", "ic_search_ganti_pass", false, "Ubah PIN, Ubah, PIN, keamanan, safety", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_UBAH_KATA_KUNCI, "CK", new LanguageModel(MenuNameId.MENU_UBAH_KATA_KUNCI,MenuNameEn.MENU_UBAH_KATA_KUNCI), MenuKategoriId.INFO_AKUN_KEAMANAN, "Keamanan", "ic_search_lock", false, "Ubah Password, Ubah, Password, Kunci, Pass, keamanan, safety", "", false, 1, "", ""));
        //Kontak BRI
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_PUSAT_BANTUAN, "HC", new LanguageModel(MenuNameId.MENU_HELP_CENTER, MenuNameEn.MENU_HELP_CENTER), MenuKategoriId.INFO_AKUN_KONTAK_BRI, "Kontak BRI", "ic_search_help_info", false, "sabrina, pusat, bantuan, call, center, komplen , komplain, complaint, Callcenter, pusat bantuan, kontak bri", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_CHAT_BANKING, "CB",new LanguageModel(MenuNameId.MENU_CHAT_BANKING ,MenuNameEn.MENU_CHAT_BANKING ), MenuKategoriId.INFO_AKUN_KONTAK_BRI, "Kontak BRI", "ic_search_square_chat", false, "sabrina, chat, banking, agen, complain, komplen, chatbanking, Chat banking, kontak bri", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_LAYANAN_BEBAS_PULSA, "FC",new LanguageModel(MenuNameId.MENU_LAYANAN_BEBAS_PULSA ,MenuNameEn.MENU_LAYANAN_BEBAS_PULSA ), MenuKategoriId.INFO_AKUN_KONTAK_BRI, "Kontak BRI", "ic_search_call_grey", false, "Layanan Bebas Pulsa, Layanan, Bebas, Pulsa, Free, Free Call, Kontak, Kontak BRI, kontak bri, bantuan", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_KONTAK_KAMI, "CU", new LanguageModel(MenuNameId.MENU_CONTACT_US ,MenuNameEn.MENU_CONTACT_US ) , MenuKategoriId.INFO_AKUN_KONTAK_BRI, "Kontak BRI", "ic_search_group_kontak", false, "kontak kami, tentang, telfon, telepon, Kontak, Contact, kontak bri", "", false, 1, "", ""));
        //Informasi
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_JENIS_DAN_LIMIT_TRX, "LM",new LanguageModel(MenuNameId.MENU_LIMIT_INFORMATION ,MenuNameEn.MENU_LIMIT_INFORMATION ), MenuKategoriId.INFO_AKUN_INFORMASI, "Informasi", "ic_search_limit_trx", false, "limit transaksi, jenis, Transaksi, Limit, informasi", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_INFO_KURS, "RI", new LanguageModel(MenuNameId.MENU_RATE_INFO ,MenuNameEn.MENU_RATE_INFO ), MenuKategoriId.INFO_AKUN_INFORMASI, "Informasi", "ic_search_info_kurs", false, "info kurs, Kurs, Info, informasi", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_INFO_SAHAM_BRI, "SI",  new LanguageModel(MenuNameId.MENU_STOCK_INFO_BRI ,MenuNameEn.MENU_STOCK_INFO_BRI ), MenuKategoriId.INFO_AKUN_INFORMASI, "Informasi", "ic_search_poll_saham", false, "info saham, Saham, info, informasi", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_LOKASI_ATM_BRI, "LA",  new LanguageModel(MenuNameId.MENU_ATM_LOCATION_BRI ,MenuNameEn.MENU_ATM_LOCATION_BRI ) , MenuKategoriId.INFO_AKUN_INFORMASI, "Informasi", "ic_search_location_atm", false, "lokasi atm, Lokasi, ATM, Location, Lokasi ATM BRI, informasi", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_LOKASI_KANTOR_BRI, "BL",new LanguageModel(MenuNameId.MENU_OFFICE_LOCATION_BRI ,MenuNameEn.MENU_OFFICE_LOCATION_BRI ) , MenuKategoriId.INFO_AKUN_INFORMASI, "Informasi", "ic_search_building_grey", false, "lokasi kantor, cabang, BRI, Uker, Lokasi Kantor BRI, informasi", "", false, 1, "", ""));
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_SNK, "SK",new LanguageModel(MenuNameId.MENU_TNC ,MenuNameEn.MENU_TNC ) , MenuKategoriId.INFO_AKUN_INFORMASI, "Informasi", "ic_search_snk", false, "term, condition, syarat, ketentuan, tnc, syarat dan ketentuan, syarat ketentuan, Term and Condition, informasi", "", false, 1, "", ""));

        //SplitBill
        menuDashAllList.add(new MenuDashboard(MenuId.MENU_SPLIT_BILL, "BT",new LanguageModel(MenuNameId.MENU_SPLIT_BILL ,MenuNameEn.MENU_SPLIT_BILL ) , MenuKategoriId.SPLITBILL, "", "ic_kategori_splitbill", false, "split, bill, bagi, tagihan,  patungan, bayar, rata, hitung, perorang, pisah, share", "", true, 58, "", ""));

        return menuDashAllList;
    }

    public static List<MenuDashboard> fetchMenuInvestasi() {
        List<MenuDashboard> menuDashboardInvestasi = new ArrayList<>();
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_BRIGHTS, "BI", new LanguageModel("Brights","Brights"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_brights", false, "saham, reksadana, danareksa, brights", IdWaNotif.MENU_ASURANSI, false, 1, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_DEPOSITO, "DO", new LanguageModel("Deposito","Deposito"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_deposito_revamp", false, "deposito, tabungan, investasi", IdWaNotif.MENU_ASURANSI, false, 2, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_DPLK, "DB", new LanguageModel("DPLK","DPLK"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_dplk", false, "investasi, dplk, dana pensiun, tabungan", IdWaNotif.MENU_ASURANSI, false, 3, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_EMAS, "EM", new LanguageModel("Emas","Emas"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_emas", false, "emas, pegadaian, tabungan, investasi, gold", IdWaNotif.MENU_ASURANSI, false, 4, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_ESBN, "SB", new LanguageModel("SBN","SBN"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_sbn_revamp", false, "investasi, sukuk, ori, sbn, sr, obligasi", IdWaNotif.MENU_ASURANSI, false, 5, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_KSEI, "KS", new LanguageModel("Portofolio KSEI","Portofolio KSEI"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_ksei", false, "investasi, ksei, ekuitas, obligasi, saham", IdWaNotif.MENU_ASURANSI, false, 6, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_RDN, "RD", new LanguageModel("RDN","RDN"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_rdn", false, "rdn, investasi, rekening dana nasabah, saham", IdWaNotif.MENU_ASURANSI, false, 7, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_RENCANA, "RC", new LanguageModel("Rencana","Rencana"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_rencana_revamp", false, "", IdWaNotif.MENU_RENCANA, false, 8, "", ""));

        return menuDashboardInvestasi;
    }

    public static List<MenuDashboard> fetchMenuDefaultInvestasi() {
        List<MenuDashboard> menuDashboardInvestasi = new ArrayList<>();
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_DEPOSITO, "DO", new LanguageModel("Deposito", "Deposito"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_deposito", false, "deposito, tabungan, investasi", IdWaNotif.MENU_ASURANSI, false, 1, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_DPLK, "DB", new LanguageModel("DPLK", "DPLK"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_dplk_revamp", false, "investasi, dplk, dana pensiun, tabungan", IdWaNotif.MENU_ASURANSI, false, 2, "", ""));
        menuDashboardInvestasi.add(new MenuDashboard(Constant.MENU_ESBN, "SB", new LanguageModel("SBN", "SBN"), MenuKategoriId.INVESTASI, "Investasi", "ic_menu_qna_sbn_revamp", false, "investasi, sukuk, ori, sbn, sr, obligasi", IdWaNotif.MENU_ASURANSI, false, 3, "", ""));

        return menuDashboardInvestasi;
    }

    public static List<MenuDashboard> onCheckBiometic(List<MenuDashboard> menuDashAllList, String bioType) {
        if (TRUE == GeneralHelper.checkBiometricSupport()) {
            menuDashAllList.add(new MenuDashboard(MenuId.MENU_BIOMETRIC, "BL", new LanguageModel(bioType, bioType), MenuKategoriId.INFO_AKUN_KEAMANAN, "Keamanan", "ic_search_fingerprint", false, "Login Fingerprint, Login, Finger, Fingerprint, Login FaceID, Face, FaceID, Login Biometric, Biometric, Jari, Muka, keamanan, safety", "", false, 1, "", ""));
        }
        return menuDashAllList;
    }

    public static List<MenuDashboard> fetchAllMenuWithNfcMenu(boolean isNfcAvailable) {
        List<MenuDashboard> newMenuList = new ArrayList<>(fetchMenuDashboardAll());
        if (isNfcAvailable) {
            newMenuList.add(new MenuDashboard(MenuId.MENU_NFC, "NF", new LanguageModel(MenuNameId.MENU_NFC,MenuNameEn.MENU_NFC), MenuKategoriId.NON_KATEGORI, "", "ic_nfc", true, "nfc, tap to pay, taptopay, qris tap, qristap, qris, contactless, tap", IdWaNotif.MENU_NFC, true, 63, "", ""));
        }
        return newMenuList;
    }

    public static void onChangeMenu(int id, Activity context, @Nullable OnItemviewClickListener mOnClickListener) {
        Intent newIntent = null;
        boolean fromFastMenu = false;

        //cek lastActivity
        try {
            String lastActivity = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                lastActivity = GeneralHelper.getLastActivity(context);
            }

            if (lastActivity != null) {
                if (!lastActivity.contains("DashboardIBActivity") && !lastActivity.contains("DashboardInvestasiActivity")
                        && !lastActivity.contains("DetailPromoCashbackActivity") && !lastActivity.contains("AllPromoActivity")
                        && !lastActivity.contains("ReceiptRevampActivity") && !lastActivity.contains("DashboardLifestyleActivity")) {
                    context.finish();
                }

                if (lastActivity.contains("FastMenuActivity")) {
                    fromFastMenu = true;
                }
            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onChangeMenu: ", e);
            }
        }


        switch (id) {
            case Constant.MENU_LAINNYA:
                if (mOnClickListener != null)
                    mOnClickListener.onMenuLainClick();
                return;
            case MenuId.MENU_TRANSFER:
                FormTransferAliasRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_PULSA:
                FormPulsaDataRevActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_PFM:
                newIntent = new Intent(context, CatatanKeuanganActivity.class);
                break;
            case MenuId.MENU_BRIVA:
                FormBrivaRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_DOMPET_DIGITAL:
                FormDompetDigitalReskinActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TELKOM:
                FormTelkomRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_KAI:
                FormKaiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BAYAR_KARTU_KREDIT:
            case MenuId.MENU_KARTU_KREDIT:
                FormKreditActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BRIZZI:
                FormBrizziActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BRIZZI_NON_USER:
                CekBrizziDariLuarActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_DEPOSITO:
                DashboardDepositoActivity.launchIntent(context, fromFastMenu, false);
                break;
            case MenuId.MENU_MUTASI:
                newIntent = new Intent(context, MutasiActivity.class);
                break;
            case MenuId.MENU_PASCA_BAYAR:
                FormGeneralRevampActivity.launchIntent(context, fromFastMenu, JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR);
                return;
            case Constant.MENU_DONASI:
                FormDonasiRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_CICILAN:
                FormCicilanActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TV:
                FormTelevisiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TARIK_TUNAI:
                FormTarikTunaiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_SETOR_TUNAI:
                FormSetorTunaiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_LISTRIK:
                FormListrikRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TOPUP_LISTRIK:
                FormListrikRevampActivity.launchIntentTopUp(context, fromFastMenu);
                return;
            case MenuId.MENU_QR:
                FormQrActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_CERIA:
                CeriaActivity.launchIntent(context);
                return;
            case MenuId.MENU_RDN:
                DashboardRdnRevampActivity.Companion.launchIntent(context);
                return;
            case MenuId.MENU_DPLK:
                DashboardDplkRevampActivity.launchIntent(context, false);
                return;
            case MenuId.MENU_ASURANSI:
                DashboardAsuransiActivity.launchIntent(context, false);
                return;
            case MenuId.MENU_QR_MERCHANT:
                QrMerchantActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_DAFTAR_CC:
                DaftarCcActivity.launchIntent(context);
                return;
            case MenuId.MENU_KONVERSI_VALAS:
                FormKonversiVallasActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BPJS:
                FormBpjsActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_SNPMB:
                FormLTMPTActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_LTMPT:
                FormLTMPTActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_QR_TRANSFER:
                QrTransferActivity.launchIntent(context);
                return;
            case MenuId.MENU_QR_MCM:
                QrMPMActivity.launchIntent(context, false, false);
                return;
            case MenuId.MENU_PDAM:
                FormPdamActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_PENDIDIKAN:
                FormPendidikanRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_KARTU_KREDIT_BRI:
                ApplyVccSofListActivity.Companion.launchIntent(context);
                return;
            /*case MenuId.MENU_LOAN_IN_APP:
                LoanInAppLandingPageActivity.Companion.launchIntent(context);
                return;*/
            case MenuId.MENU_MPN:
                FormMpnActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_PBB:
                FormPbbActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_PINJAMAN_BRI:
                InfoPinjamanActivity.launchIntent(context);
                return;
            case MenuId.MENU_BRIGUNA:
                BrigunaDigitalActivity.launchIntent(context);
                return;
            case MenuId.MENU_TRANSFER_INTERNASIONAL:
                FormTfInternasionalActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_ESBN:
                DashboardSbnRevampActivity.launchIntent(context, fromFastMenu, false);
                return;
            case MenuId.MENU_STREAMING:
            case MenuId.MENU_TOPUP_STREAMING:
                VoucherActivity.launchIntent(context, Constant.Voucher.STREAMING.name());
                return;
            case MenuId.MENU_VOUCHER_GAME:
            case MenuId.MENU_TOPUP_VOUCHER_GAME:
                VoucherActivity.launchIntent(context, Constant.Voucher.GAME.name());
                return;
            case MenuId.MENU_SIGNAL:
                FormSignalActivity.launchIntent(context);
                return;
            case MenuId.MENU_DEBIT_VIRTUAL:
                newIntent = new Intent(context, OnBoardingVDCActivity.class);
                break;
            case MenuId.MENU_EMAS:
                DashboardEmasActivity.launchIntent(context, false);
                return;
            case MenuId.MENU_KSEI:
                DashboardKseiActivity.launchIntent(context, false);
                return;
            case MenuId.MENU_PAJAK_HOREKA:
                FormPajakHorekaActivity.launchIntent(context);
                return;
            case MenuId.MENU_PROPERTY:
                FormPropertyActivity.launchIntent(context);
                return;
            default:
                GeneralHelper.showBottomDialog((FragmentActivity) context, Constant.COMING_SOON);
                return;
        }
        if (newIntent != null) {
            context.startActivityForResult(newIntent, Constant.REQ_PAYMENT);
        }
    }

    public static void navigationToSpecificFeature(String menuName, Activity context) {
        switch (menuName) {
            case Constant.SpecificFeatureId.FEATURE_PANGELOLAAN_KARTU:
                PengelolaanKartuNewActivity.launchIntent(context);
                return;
            default:
                GeneralHelper.showBottomDialog((FragmentActivity) context, Constant.COMING_SOON);
                break;
        }
    }

    /**
     * Method routing menu untuk Dashboard Revamp
     *
     * @param context
     * @param menuId
     */
    public static void onMenuDefaultClick(Activity context, int menuId) {
        Intent newIntent = null;
        boolean fromFastMenu = false;

        //cek lastActivity
        try {
            String lastActivity = null;
            if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.Q) {
                lastActivity = GeneralHelper.getLastActivity(context);
            }

            if (lastActivity != null) {
                if (!lastActivity.contains("DashboardIBActivity") && !lastActivity.contains("MenuListGeneralActivity") &&
                        !lastActivity.contains("TopUpRevampActivity") && !lastActivity.contains("HalamanCariRevampActivity") &&
                        !lastActivity.contains("DashboardInvestasiActivity") && !lastActivity.contains("DashboardLifestyleActivity") &&
                        !lastActivity.contains("SpltBillHistoryActivity")
                ) {
                    context.finish();
                }

            }
        } catch (Exception e) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onChangeMenu: ", e);
            }
        }

        switch (menuId) {
            case MenuId.MENU_TRANSFER:
                FormTransferAliasRevampActivity.launchIntent(context, false);
                return;
            case MenuId.MENU_PULSA:
                FormPulsaDataRevActivity.launchIntent(context, false);
                return;
            case MenuId.MENU_PFM:
                newIntent = new Intent(context, CatatanKeuanganActivity.class);
                break;
            case MenuId.MENU_BRIVA:
                FormBrivaRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_DOMPET_DIGITAL:
                FormDompetDigitalReskinActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TELKOM:
                FormTelkomRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_KAI:
                FormKaiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BAYAR_KARTU_KREDIT:
            case MenuId.MENU_KARTU_KREDIT:
                FormKreditActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BRIZZI:
                FormBrizziActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BRIZZI_NON_USER:
                CekBrizziDariLuarActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_DEPOSITO:
                DashboardDepositoActivity.launchIntent(context, fromFastMenu, false);
                break;
            case MenuId.MENU_MUTASI:
                newIntent = new Intent(context, MutasiActivity.class);
                break;
            case MenuId.MENU_PASCA_BAYAR:
                FormGeneralRevampActivity.launchIntent(context, fromFastMenu, JourneyType.JOURNEY_TYPE_REVAMP_PASCABAYAR);
                return;
            case MenuId.MENU_DONASI:
                FormDonasiRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_CICILAN:
                FormCicilanActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TV:
                FormTelevisiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TARIK_TUNAI:
                FormTarikTunaiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_SETOR_TUNAI:
                FormSetorTunaiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_LISTRIK:
                FormListrikRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_TOPUP_LISTRIK:
                FormListrikRevampActivity.launchIntentTopUp(context, fromFastMenu);
                return;
            case MenuId.MENU_QR:
                FormQrActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_CERIA:
                CeriaActivity.launchIntent(context);
                return;
            case MenuId.MENU_RDN:
                DashboardRdnRevampActivity.Companion.launchIntent(context);
                return;
            case MenuId.MENU_DPLK:
                DashboardDplkRevampActivity.launchIntent(context, false);
                return;
            case MenuId.MENU_ASURANSI:
                DashboardAsuransiActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_QR_MERCHANT:
                QrMerchantActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_DAFTAR_CC:
                DaftarCcActivity.launchIntent(context);
                return;
            case MenuId.MENU_KONVERSI_VALAS:
                FormKonversiVallasActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_BPJS:
                FormBpjsActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_SNPMB:
                FormLTMPTActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_LTMPT:
                FormLTMPTActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_QR_TRANSFER:
                QrTransferActivity.launchIntent(context);
                return;
            case MenuId.MENU_QR_MCM:
                QrMPMActivity.launchIntent(context, false, false);
                return;
            case MenuId.MENU_PDAM:
                FormPdamActivity.launchIntent(context, fromFastMenu);
                return;
            case Constant.MENU_PENDIDIKAN:
                FormPendidikanRevampActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_KARTU_KREDIT_BRI:
                ApplyVccSofListActivity.Companion.launchIntent(context);
                return;
            /*case MenuId.MENU_LOAN_IN_APP:
                LoanInAppLandingPageActivity.Companion.launchIntent(context);
                return;*/
            case MenuId.MENU_MPN:
                FormMpnActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_PBB:
                FormPbbActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_PINJAMAN_BRI:
                InfoPinjamanActivity.launchIntent(context);
                return;
            case MenuId.MENU_BRIGUNA:
                BrigunaDigitalActivity.launchIntent(context);
                return;
            case MenuId.MENU_TRANSFER_INTERNASIONAL:
                FormTfInternasionalActivity.launchIntent(context, fromFastMenu);
                return;
            case MenuId.MENU_ESBN:
                DashboardSbnRevampActivity.launchIntent(context, fromFastMenu, false);
                return;
            case MenuId.MENU_STREAMING:
            case MenuId.MENU_TOPUP_STREAMING:
                VoucherActivity.launchIntent(context, Constant.Voucher.STREAMING.name());
                return;
            case MenuId.MENU_VOUCHER_GAME:
            case MenuId.MENU_TOPUP_VOUCHER_GAME:
                VoucherActivity.launchIntent(context, Constant.Voucher.GAME.name());
                return;
            case MenuId.MENU_SIGNAL:
                FormSignalActivity.launchIntent(context);
                return;
            case MenuId.MENU_PAJAK_HOREKA:
                FormPajakHorekaActivity.launchIntent(context);
                return;
            case MenuId.MENU_PROPERTY:
                FormPropertyActivity.launchIntent(context);
                return;
            case MenuId.MENU_DEBIT_VIRTUAL:
                newIntent = new Intent(context, OnBoardingVDCActivity.class);
                break;
            case MenuId.MENU_BANK_RAYA:
            case MenuId.MENU_BRI_FINANCE:
            case MenuId.MENU_BRIGHTS:
            case MenuId.MENU_JADI_MERCHANT:
            case MenuId.MENU_INFO_LELANG:
                BrowserIntentActivity.launchIntent(context, GeneralHelper.getString(R.string.url_get_launcher), menuId);
                break;
            case MenuId.MENU_EMAS:
                DashboardEmasActivity.launchIntent(context, false);
                break;
            case MenuId.MENU_KSEI:
                DashboardKseiActivity.launchIntent(context, false);
                break;
            case MenuId.MENU_QLOLA:
                IbbizActivity.launchIntent(context);
                break;
            case MenuId.MENU_DASHBOARD_INVESTASI:
                DashboardInvestasiActivity.launchIntent(context);
                break;
            case MenuId.MENU_RENCANA:
                DashboardRencanaActivity.launchIntent(context, false);
                break;
            case MenuId.MENU_FM:
                EditFastMenuRevampActivity.launchIntent(context, false, null, null);
                break;
            case MenuId.MENU_UPDATE_REKENING:
                ListRekeningCategoryActivity.launchIntent(context);
                break;
            case MenuId.MENU_PENGELOLAAN_KARTU:
                PengelolaanKartuNewActivity.launchIntent(context);
                break;
            case MenuId.MENU_UBAH_PIN:
                ChangePinActivity.Companion.launchIntent(context);
                break;
            case MenuId.MENU_UBAH_KATA_KUNCI:
                ChangePassAccountActivity.Companion.launchIntent(context);
                break;
            case MenuId.MENU_PUSAT_BANTUAN:
                SelfServiceActivity.launchIntent(context);
                break;
            case MenuId.MENU_KONTAK_KAMI:
                KontakKamiActivity.launchIntent(context);
                break;
            case MenuId.MENU_JENIS_DAN_LIMIT_TRX:
                newIntent = new Intent(context, TransactionLimitInformationActivity.class);
                break;
            case MenuId.MENU_INFO_KURS:
                InfoKursActivity.launchIntent(context);
                break;
            case MenuId.MENU_INFO_SAHAM_BRI:
                InfoSahamActivity.launchIntent(context);
                break;
            case MenuId.MENU_SNK:
                SyaratKetentuanActivity.launchIntent(context);
                break;
            case MenuId.MENU_SUMBER_DANA_QRIS:
                SofQrisActivity.launchIntent(context);
                break;
            case MenuId.MENU_BIOMETRIC:
                DashboardIBActivity.launchIntentSearchMenu(context);
                break;
            default:
                GeneralHelper.showBottomDialog((FragmentActivity) context, Constant.COMING_SOON);
                return;
        }

        if (newIntent != null) {
            context.startActivityForResult(newIntent, Constant.REQ_PAYMENT);
        }
    }

    public static List<MenuDashboard> fetchNewMenu(boolean isNfcAvailable) {
        List<MenuDashboard> menuDashNewList = new ArrayList<>();
        List<MenuDashboard> menuDashAllList = new ArrayList<>();
        menuDashAllList.addAll(MenuConfig.fetchAllMenuWithNfcMenu(isNfcAvailable));

        for (int i = 0; i < menuDashAllList.size(); i++) {
            if (menuDashAllList.get(i) != null) {
                if (menuDashAllList.get(i).isNew()) {
                    menuDashNewList.add(menuDashAllList.get(i));
                }
            }
        }

        return menuDashNewList;
    }

    public static List<MenuKategori> fetchNewKategori() {
        List<MenuKategori> kategoriNewList = new ArrayList<>();
        List<MenuKategori> kategoriAllList = new ArrayList<>();
        kategoriAllList.addAll(MenuConfig.fetchNewKategori());

        for (int i = 0; i < kategoriAllList.size(); i++) {
            if (kategoriAllList.get(i) != null) {
                if (kategoriAllList.get(i).isStatusNew()) {
                    kategoriNewList.add(kategoriAllList.get(i));
                }
            }
        }

        return kategoriNewList;
    }

    public interface OnItemviewClickListener {

        void onMenuClick(int menuId, OnItemviewClickListener onItemviewClickListener);

        void onMenuLainClick();

        void remove(int position, boolean isMin, boolean isAdd, String tagRecycleView);
    }

    public static class OnboardingCheckpoint {
        public static final int KTP_SELFDATA = 1;
        public static final int VERIFY_HP_EMAIL = 2;
        public static final int VERIFY_WAJAH = 3;
        public static final int DATA_AR = 4;
        public static final int CREATE_BRIMO = 5;
        public static final int VALIDATE_BRIMO = 6;
    }

    public static class PFMMenuFragment {
        public static final int PFM_OUTCOME = 0;
        public static final int PFM_INCOME = 1;
        public static final int PFM_REPORT = 2;
    }
}