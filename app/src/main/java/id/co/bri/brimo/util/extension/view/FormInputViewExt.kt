package id.co.bri.brimo.util.extension.view

import android.view.View
import androidx.lifecycle.findViewTreeLifecycleOwner
import androidx.lifecycle.lifecycleScope
import id.co.bri.brimo.R
import id.co.bri.brimo.domain.extension.afterTextChanged
import id.co.bri.brimo.ui.customviews.forminput.FormInputDefaultView
import id.co.bri.brimo.util.extension.type.orZero
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch

/**
 * Adds a validation rule to this [FormInputDefaultView]'s `editText` that requires
 * a minimum of 3 characters.
 *
 * This is a convenience function that calls [addMinLengthCharValidation] with a `minLength` of 3.
 * The validation is performed with a debounce mechanism to avoid excessive checks
 * while the user is typing.
 *
 * An error message is displayed using the [FormInputDefaultView.showErrorIf] method
 * if the input length is between 1 and 2 characters (inclusive) after the debounce period.
 * The error is cleared if the input is empty or meets/exceeds 3 characters.
 *
 * The [kotlinx.coroutines.Job] associated with the debounced validation is automatically
 * cancelled when the view is detached from the window to prevent memory leaks or
 * unwanted behavior.
 *
 * @receiver [FormInputDefaultView] The view to which this validation logic will be added.
 * @param errorText The error message to display if the validation fails. Defaults to
 *                  a predefined string resource [R.string.form_validation_min3char_error_text].
 * @see addMinLengthCharValidation
 * @see FormInputDefaultView.showErrorIf
 */
fun FormInputDefaultView.addMin3CharsValidation(
    errorText: String = context.getString(R.string.form_validation_min3char_error_text)
) {
    addMinLengthCharValidation(
        minLength = 3,
        errorText = errorText
    )
}

/**
 * Adds debounced minimum length validation to this [FormInputDefaultView].
 *
 * Checks if the input text length meets `minLength` after a `debounceDelay`.
 * Shows `errorText` via [FormInputDefaultView.showErrorIf] if length is > 0 and < `minLength`.
 * Invokes `onValid` if length is 0 or >= `minLength`, otherwise `onInvalid`.
 * Debounce job is lifecycle-aware and cancelled on view detachment.
 *
 * @param minLength Minimum required characters (unless empty).
 * @param errorText Error message for invalid input.
 * @param debounceDelayOnError Delay in ms after text change before validation. Default 3000ms.
 * @param onValid Lambda for valid input (length is 0 or >= `minLength`).
 * @param onInvalid Lambda for invalid input (length > 0 and < `minLength`).
 */
fun FormInputDefaultView.addMinLengthCharValidation(
    enabled: Boolean = true,
    minLength: Int,
    errorText: String,
    debounceDelayOnError: Long = 3000L,
    onValid: () -> Unit = {},
    onInvalid: () -> Unit = {}
) {
    if (!enabled) return

    var debounceJob: Job? = null
    val scope = findViewTreeLifecycleOwner()?.lifecycleScope ?: CoroutineScope(Dispatchers.Main)

    editText.afterTextChanged { editable ->
        debounceJob?.cancel()
        val length = editable?.toString()?.length.orZero()

        if (isAttachedToWindow) {
            hideError()
        }

        val isError = length in 1 until minLength

        if (isError) {
            debounceJob = scope.launch {
                delay(debounceDelayOnError)
                if (isAttachedToWindow && (editText.text?.length ?: 0) < minLength) {
                    showError(errorText)
                    onInvalid()
                }
            }
        } else if (length >= minLength) {
            onValid()
        }
    }

    editText.addOnFocusChangeListener { _, hasFocus ->
        if (!hasFocus) {
            hideError()
        } else {
            val length = editText.text?.length.orZero()
            if (length in 1 until minLength) {
                showError(errorText)
                onInvalid()
            }
        }
    }


    addOnAttachStateChangeListener(object : View.OnAttachStateChangeListener {
        override fun onViewAttachedToWindow(v: View) {}
        override fun onViewDetachedFromWindow(v: View) {
            debounceJob?.cancel()
        }
    })
}


fun FormInputDefaultView.addDigitOnlyFilter() {
    editText.afterTextChanged { s ->
        val originalText = s.toString()
        val filteredText = originalText.filter { it.isDigit() }

        if (originalText != filteredText) {
            editText.setText(filteredText)
            editText.setSelection(filteredText.length)
        }
    }
}

fun FormInputDefaultView.addAllCapsFilter() {
    editText.afterTextChanged { s ->
        val originalText = s.toString()
        val filteredText = originalText.uppercase()

        if (originalText != filteredText) {
            editText.setText(filteredText)
            editText.setSelection(filteredText.length)
        }
    }
}