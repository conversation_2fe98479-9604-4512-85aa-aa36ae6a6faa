package id.co.bri.brimo.ui.activities.onboardingnewskin

import android.app.Activity
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import androidx.activity.result.contract.ActivityResultContracts
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.onboardingnewskin.ICreateKataKunciOnboardingPresenter
import id.co.bri.brimo.contract.IView.onboardingnewskin.ICreateKataKunciOnboardingView
import id.co.bri.brimo.databinding.ActivityBuatKataKunciBinding
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.ui.activities.base.NewSkinOnboardingBaseActivity
import id.co.bri.brimo.util.extension.view.disableCopyPaste
import id.co.bri.brimo.util.extension.view.onTextChanged
import id.co.bri.brimo.util.extension.view.preventSpaceInput
import id.co.bri.brimo.util.extension.view.togglePasswordVisibility
import id.co.bri.brimo.util.extension.view.addMinLengthCharValidation
import javax.inject.Inject

class BuatKataKunciActivity : NewSkinOnboardingBaseActivity(), ICreateKataKunciOnboardingView {

    @Inject
    lateinit var presenter: ICreateKataKunciOnboardingPresenter<ICreateKataKunciOnboardingView>

    private lateinit var binding: ActivityBuatKataKunciBinding
    private var isPasswordVisible = false
    private var isConfirmPasswordVisible = false
    private val handler = Handler(Looper.getMainLooper())

    private val launcher = registerForActivityResult(ActivityResultContracts.StartActivityForResult()) {}

    companion object {
        var refNumber: String = ""

        fun launchIntent(caller: Activity, ref: String, blockBackPress: Boolean = false) {
            refNumber = ref
            val intent = Intent(caller, BuatKataKunciActivity::class.java).apply {
                putExtra(KEY_BLOCK_BACK_PRESS, blockBackPress)
            }
            caller.startActivity(intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBuatKataKunciBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setupSecurityDetection(binding.rootCoordinator)
        injectDependencies()
        setupView()
    }

    private fun injectDependencies() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
    }

    private fun setupView() = with(binding) {
        GeneralHelperNewSkin.setToolbarWithoutNav(toolbar.toolbar, getString(R.string.buat_password))

        observeNewKataKunci()
        observeConfirmObserveNewKataKunci()
        setupTogglePasswordVisibility()

        inputPassword.preventSpaceInput()
        inputConfirmPassword.preventSpaceInput()

        inputPassword.disableCopyPaste()
        inputConfirmPassword.disableCopyPaste()

        inputConfirmPassword.editText.isEnabled = false
    }

    private fun setupTogglePasswordVisibility() = with(binding) {
        inputPassword.inputLayout.setEndIconOnClickListener {
            isPasswordVisible = !isPasswordVisible
            inputPassword.togglePasswordVisibility(isPasswordVisible)
        }

        inputConfirmPassword.inputLayout.setEndIconOnClickListener {
            isConfirmPasswordVisible = !isConfirmPasswordVisible
            inputConfirmPassword.togglePasswordVisibility(isConfirmPasswordVisible)
        }
    }


    private fun observeNewKataKunci() = with(binding) {
        inputPassword.onTextChanged { s ->
            val input = s?.toString() ?: ""
            val isValidChar = input.length >= 8
            val hasUppercase = input.any { it.isUpperCase() }
            val hasLowercase = input.any { it.isLowerCase() }
            val hasDigit = input.any { it.isDigit() }
            val hasSymbol = input.any { !it.isLetterOrDigit() }
            val noSpace = input.isNotEmpty() && !input.contains(" ")

            val isValidAlpha = hasUppercase && hasLowercase && hasDigit && hasSymbol
            val allValid = isValidChar && isValidAlpha && noSpace

            inputConfirmPassword.editText.isEnabled = allValid

            ivCharacter.setImageResource(if (isValidChar) R.drawable.tick_circle else R.drawable.tick_circle_disable)
            ivCapital.setImageResource(if (isValidAlpha) R.drawable.tick_circle else R.drawable.tick_circle_disable)
            ivSpace.setImageResource(if (noSpace) R.drawable.tick_circle else R.drawable.tick_circle_disable)

            if (allValid) {
                handler.postDelayed({ presenter.confirmKataKunci() }, 500)
            } else {
                btnNext.isEnabled = false
            }
        }

        inputPassword.addMinLengthCharValidation(
            minLength = 8,
            errorText = "Minimal harus 8 digit angka",
            debounceDelayOnError = 3000L
        )
    }

    private fun observeConfirmObserveNewKataKunci() = with(binding) {
        inputConfirmPassword.onTextChanged { s ->
            val confirm = s?.toString() ?: ""
            val pass = inputPassword.getText()
            val isMatch = confirm == pass && confirm.isNotEmpty()

            ivSame.setImageResource(if (isMatch) R.drawable.tick_circle else R.drawable.tick_circle_disable)
            btnNext.isEnabled = isMatch

            if (isMatch) {
                btnNext.setOnClickListener {
//                    CreatePinOnboardingActivity.launchIntent(this@BuatKataKunciActivity, refNumber, launcher)
                }
            }
        }
    }

    override fun getNewObserveNewKataKunci(): String = binding.inputPassword.getText()
    override fun getConfirmObserveNewKataKunci(): String = binding.inputConfirmPassword.getText()
    override fun getRefNumber(): String = refNumber
    override fun onSubmitSuccess(refNumber: String, cellNumber: String) {}
    override fun onException93(description: String) {}

    override fun onKataKunciConfirmationFailed() = with(binding) {
        ivSame.setImageResource(R.drawable.tick_circle_disable)
        btnNext.isEnabled = false
    }

    override fun onKataKunciConfirmationSuccess() = with(binding) {
        ivSame.setImageResource(R.drawable.tick_circle)
        inputConfirmPassword.editText.isEnabled = true
        btnNext.isEnabled = true
    }

    override fun isScreenshotDisabled(): Boolean = true
}