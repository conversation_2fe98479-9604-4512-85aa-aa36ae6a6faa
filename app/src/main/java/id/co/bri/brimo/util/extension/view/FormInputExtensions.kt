package id.co.bri.brimo.util.extension.view

import android.text.Editable
import android.text.InputFilter
import android.text.InputType
import android.text.TextWatcher
import id.co.bri.brimo.R
import id.co.bri.brimo.ui.customviews.forminput.FormInputDefaultView

fun FormInputDefaultView.togglePasswordVisibility(isVisible: <PERSON><PERSON>an) {
    val inputType = InputType.TYPE_CLASS_TEXT or
            if (isVisible) InputType.TYPE_TEXT_VARIATION_VISIBLE_PASSWORD
            else InputType.TYPE_TEXT_VARIATION_PASSWORD
    this.inputType = inputType

    val icon = if (isVisible) R.drawable.ic_new_skin_eye_open else R.drawable.ic_new_skin_eye_close
    this.setIconDrawable(icon)
}

fun FormInputDefaultView.disableCopyPaste() {
    val editText = this.editText

    editText.isLongClickable = false
    editText.setTextIsSelectable(false)

    val emptyCallback = object : android.view.ActionMode.Callback {
        override fun onCreateActionMode(mode: android.view.ActionMode?, menu: android.view.Menu?) =
            false

        override fun onPrepareActionMode(mode: android.view.ActionMode?, menu: android.view.Menu?) =
            false

        override fun onActionItemClicked(
            mode: android.view.ActionMode?,
            item: android.view.MenuItem?
        ) = false

        override fun onDestroyActionMode(mode: android.view.ActionMode?) {}
    }

    editText.customInsertionActionModeCallback = emptyCallback
    editText.customSelectionActionModeCallback = emptyCallback

    editText.setOnLongClickListener { true }
    editText.setOnClickListener { it.clearFocus() }
}

fun FormInputDefaultView.preventSpaceInput() {
    val noSpaceFilter = InputFilter { source, _, _, _, _, _ ->
        if (source.contains(" ")) "" else null
    }
    this.filters = arrayOf(noSpaceFilter)
}

fun FormInputDefaultView.onTextChanged(onTextChanged: (CharSequence?) -> Unit) {
    this.addTextChangedListener(object : TextWatcher {
        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
            onTextChanged(s)
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
        override fun afterTextChanged(s: Editable?) {}
    })
}

fun FormInputDefaultView.setupBasicValidation(
    enable: Boolean = true,
    minLength: Int = 3,
    errorText: String = "Minimal harus 3 karakter",
    blockNumbers: Boolean = true,
    onValidInput: (String) -> Unit,
    onValidChange: (Boolean) -> Unit
) {
    if (minLength > 0) {
        addMinLengthCharValidation(
            enabled = enable,
            minLength = minLength,
            errorText = errorText,
            debounceDelayOnError = 3000L
        )
    }

    restrictCharacter(blockNumbers, onValidInput = { input ->
        onValidInput(input)
        onValidChange(input.length >= minLength)
    }, onValidChange = null)
}

fun FormInputDefaultView.restrictCharacter(
    blockNumbers: Boolean = true,
    regex: Regex? = null,
    onValidInput: (String) -> Unit,
    onValidChange: ((Boolean) -> Unit)? = null
) {
    editText.addTextChangedListener(object : TextWatcher {
        private var isEditing = false

        private val allowedRegex = regex ?: if (blockNumbers) {
            Regex("^[a-zA-Z ]*\$")
        } else {
            Regex("^[a-zA-Z0-9 ]*\$")
        }

        override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}

        override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}

        override fun afterTextChanged(s: Editable?) {
            if (isEditing) return

            s?.let {
                var fixed = it.toString()

                fixed = fixed.trimStart().replace("\\s{2,}".toRegex(), " ")

                fixed = fixed.filter { char -> allowedRegex.matches(char.toString()) }

                if (fixed != it.toString()) {
                    isEditing = true
                    editText.setText(fixed)
                    editText.setSelection(fixed.length.coerceAtMost(editText.text?.length ?: 0))
                    isEditing = false
                } else {
                    editText.adjustTypeface(fixed)
                    onValidInput(fixed)
                    onValidChange?.invoke(fixed.isNotEmpty())
                }
            }
        }
    })
}
