package id.co.bri.brimo.ui.fragments

import androidx.appcompat.widget.AppCompatButton
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin

open class NewSkinBaseFragment : BaseFragment() {
    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(baseActivity, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(
                Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM,
                message
            )
        ) GeneralHelperNewSkin.showErrorBottomDialog(requireActivity(), message)
        else GeneralHelperNewSkin.showGeneralErrorBottomDialog(requireActivity())
    }

    fun updateButtonState(isEnabled: Boolean, button: AppCompatButton) {
        button.apply {
            this.isEnabled = isEnabled
        }
    }

    override fun showProgress() {
        GeneralHelperNewSkin.showLoadingDialog(baseActivity)
    }

    override fun hideProgress() {
        GeneralHelperNewSkin.dismissLoadingDialog()
    }
}