package id.co.bri.brimo.ui.activities.bukarekening

import android.app.Activity
import android.content.Intent
import android.content.IntentFilter
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowManager
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import androidx.appcompat.widget.Toolbar
import androidx.core.content.ContextCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import butterknife.Bind
import butterknife.ButterKnife
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.DetailTransaksiRevampAdapter
import id.co.bri.brimo.adapters.SumberTransaksiRevAdapter
import id.co.bri.brimo.adapters.TotalTransaksiRevAdapter
import id.co.bri.brimo.domain.ConnectionReceiver
import id.co.bri.brimo.domain.ConnectionReceiver.ConnectivityReceiverListener
import id.co.bri.brimo.contract.IPresenter.britamarencanarevamp.IKonfrimasiTabunganRevPresenter
import id.co.bri.brimo.contract.IView.britamarencanarevamp.IKonfirmasiTabunganRevView
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.SizeHelper
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.models.AccountModel
import id.co.bri.brimo.models.apimodel.response.GeneralConfirmationResponse
import id.co.bri.brimo.models.apimodel.response.PendingResponse
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import id.co.bri.brimo.models.apimodel.response.emas.GeneralConfirmationChecklistResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptEmasResponse
import id.co.bri.brimo.models.apimodel.response.emas.ReceiptGagalEmasResponse
import id.co.bri.brimo.ui.activities.GeneralSyaratActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.LupaPinFastActivity
import id.co.bri.brimo.ui.activities.RiplayActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.emas.ReceiptGagalAFTActivity
import id.co.bri.brimo.ui.activities.emas.ReceiptGagalRegistrasiActivity
import id.co.bri.brimo.ui.activities.general.GeneralSNKWithPinActivity
import id.co.bri.brimo.ui.activities.general.GeneralSNKwithChecklistActivity
import id.co.bri.brimo.ui.customviews.dialog.DialogContinueCustom
import id.co.bri.brimo.ui.customviews.dialog.DialogExitCustom
import id.co.bri.brimo.ui.fragments.PinFragment
import javax.inject.Inject

class KonfirmasiTabunganRevActivity : BaseActivity(), View.OnClickListener,IKonfirmasiTabunganRevView,
        PinFragment.SendPin, ConnectivityReceiverListener {

    @Bind(R.id.tb_konfirmasi)
    lateinit var tbKonfirmasi : Toolbar
    @Bind(R.id.ll_syarat_ketentuan)
    lateinit var llSyarat : LinearLayout
    @Bind(R.id.ly_abu)
    lateinit var lyAbu : LinearLayout
    @Bind(R.id.view_saved_konfrimasi_2)
    lateinit var lySyarat : LinearLayout
    @Bind(R.id.tv_saldo_rek)
    lateinit var tvSaldo : TextView
    @Bind(R.id.tv_alias_rek)
    lateinit var tvAlias : TextView
    @Bind(R.id.tv_no_rek)
    lateinit var tvNorek : TextView
    @Bind(R.id.rvDetail)
    lateinit var rvDetail : RecyclerView
    @Bind(R.id.rv_payment)
    lateinit var rvPayment : RecyclerView
    @Bind(R.id.rv_total_payment)
    lateinit var rvTotal : RecyclerView
    @Bind(R.id.cb_syarat)
    lateinit var cbSyarat: ImageView
    @Bind(R.id.btnSubmit)
    lateinit var btnSubmit : Button
    @Bind(R.id.iv_icon_rek)
    lateinit var ivIcon : ImageView
    @Bind(R.id.iv_icon_utama)
    lateinit var ivIconUtama : ImageView
    @Bind(R.id.img_internet_indicator)
    lateinit var imgInternetIndicator: ImageView
    @Bind(R.id.ly_payment)
    lateinit var lyPayment : LinearLayout
    @Bind(R.id.ll_riplay_msg)
    lateinit var llRiplay : LinearLayout
    @Bind(R.id.cb_riplay)
    lateinit var cbRiplay: ImageView
    @Bind(R.id.ll_riplay_wrapper)
    lateinit var llRiplayWrapper: LinearLayout

    @Inject
    lateinit var presenter : IKonfrimasiTabunganRevPresenter<IKonfirmasiTabunganRevView>
    var detailTransaksiRevampAdapter: DetailTransaksiRevampAdapter? = null
    var sumberTransaksiRevAdapter: SumberTransaksiRevAdapter? = null
    var totalTransaksiRevAdapter: TotalTransaksiRevAdapter? = null
    var isSyarat = false
    var connectionIndicator: BubbleShowCaseBuilder? = null
    var connectionReceiver: ConnectionReceiver? = null
    var dialogContinueCustom: DialogContinueCustom? = null
    private var isRiplay = false
    private var timeCon = 0f
    protected var actStatus = 0
    private val TAG = "KonfirmasiTabunganRevActivity"
    companion object{
        private lateinit var mGeneralConfirmationResponse : GeneralConfirmationResponse
        private lateinit var mGeneralConfirmationChecklistResponse : GeneralConfirmationChecklistResponse
        private lateinit var mAccountModel: AccountModel
        private lateinit var mSetoranAwal : String
        lateinit var mUrlPayment : String
        var mIsFromEmas : Boolean= false
        var mIsSnkTabungan : Boolean= false

        lateinit var mUrlPending : String
        fun launchIntentBukaRekening(caller: Activity, generalConfirmationResponse: GeneralConfirmationResponse, accountModel: AccountModel, setoranAwal :String,urlPayment : String, urlPending : String, isSnkTabungan : Boolean) {
            val intent = Intent(caller, KonfirmasiTabunganRevActivity::class.java)
            mGeneralConfirmationResponse = generalConfirmationResponse
            mAccountModel = accountModel
            mSetoranAwal = setoranAwal
            mUrlPayment =  urlPayment
            mIsFromEmas= false
            mIsSnkTabungan = isSnkTabungan
            mUrlPending = urlPending
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }

        fun launchIntent(caller: Activity, generalConfirmationResponse: GeneralConfirmationChecklistResponse, accountModel: AccountModel, setoranAwal :String,urlPayment : String, isFromEmas : Boolean) {
            val intent = Intent(caller, KonfirmasiTabunganRevActivity::class.java)
            mGeneralConfirmationChecklistResponse = generalConfirmationResponse
            mAccountModel = accountModel
            mSetoranAwal = setoranAwal
            mUrlPayment =  urlPayment
            mIsFromEmas = isFromEmas
            mIsSnkTabungan = false
            caller.startActivityForResult(intent, Constant.REQ_BUKA_REKENING)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_konfirmasi_tabungan_rev)
        ButterKnife.bind(this)

        GeneralHelper.setToolbarRevamp(this,tbKonfirmasi,getString(R.string.toolbar_konfirmasi_tabungan))

        llSyarat.setOnClickListener(this)
        llRiplay.setOnClickListener(this)
        btnSubmit.setOnClickListener(this)
        injectDependency()
        setupView()

    }


    fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlPayment(mUrlPayment)
        presenter.start()
    }
    fun setupView() {

        if (!mIsFromEmas){
            SizeHelper.setMarginsView(this, lyAbu, 0,0,0,0)
            SizeHelper.setMarginsView(this, rvTotal, 16,16,16,0)
            lyAbu.setBackgroundColor(GeneralHelper.getColor(R.color.whiteColor))
            llRiplayWrapper.visibility = View.VISIBLE
        }
        else{
            SizeHelper.setMarginsView(this, lyAbu, 0,0,0,0)
            SizeHelper.setMarginsView(this, rvTotal, 16,16,16,0)
            lyAbu.setBackgroundColor(GeneralHelper.getColor(R.color.whiteColor))
            mGeneralConfirmationResponse = GeneralConfirmationResponse()
            mGeneralConfirmationResponse.pfmCategory = 0
            mGeneralConfirmationResponse.referenceNumber = mGeneralConfirmationChecklistResponse.referenceNumber
            llRiplayWrapper.visibility = View.GONE
        }

        //set blue bar
        if (Build.VERSION.SDK_INT >= 21) {
            val window = window
            window.addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            window.clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            window.statusBarColor = resources.getColor(R.color.highlightColor)
        }

        connectionIndicator = BubbleShowCaseBuilder(this) //Activity instance
                .title(GeneralHelper.getString(R.string.status_internet)) //Any title for the bubble view
                .description(GeneralHelper.getString(R.string.status_internet_desc))
                .backgroundColor(Color.WHITE)
                .textColor(Color.BLACK)
                .targetView(imgInternetIndicator)
                .buttonTitle(GeneralHelper.getString(R.string.ok))
                .arrowPosition(BubbleShowCase.ArrowPosition.TOP)

        imgInternetIndicator.setOnClickListener(View.OnClickListener { view: View? -> connectionIndicator!!.show() })

        if (mAccountModel.isDefault  == 1){
            ivIconUtama.visibility =View.VISIBLE
        }else{
            ivIconUtama.visibility =View.GONE
        }

        if (mIsFromEmas){
            tvNorek.text =  mGeneralConfirmationChecklistResponse.sourceAccountDataView!!.subtitle
        }else{
            tvNorek.text =  mGeneralConfirmationResponse.sourceAccountDataView.subtitle
        }
        tvAlias.text = mAccountModel.alias

        if (mAccountModel.saldoReponse == null){
            tvSaldo.text =  GeneralHelper.formatNominalIDR(
                    mAccountModel.currency,
                    mSetoranAwal)

        }else{
            tvSaldo.text =  GeneralHelper.formatNominalIDR(
                    mAccountModel.currency,
                    mAccountModel.saldoReponse.balanceString)
        }

        if (mAccountModel.alias != null && !mAccountModel.alias.equals("")) {
            tvAlias.text = mAccountModel.alias
        }else{
            tvAlias.text = GeneralHelper.getString(R.string.belum_ada_alias_text)
        }

        GeneralHelper.loadImageUrl(
                this,
                mAccountModel.imagePath,
                ivIcon,
                R.drawable.bri,
                0
        )

        //Detail Nominal
        rvDetail.setHasFixedSize(true)
        rvDetail.layoutManager= LinearLayoutManager(
                applicationContext, RecyclerView.VERTICAL, false
        )

        if (mIsFromEmas){
            isSyarat = true
            isRiplay = true
            btnSubmit.text = mGeneralConfirmationChecklistResponse.buttonText
            tvNorek.text =  mGeneralConfirmationChecklistResponse.sourceAccountDataView!!.subtitle
            lySyarat.visibility = View.GONE
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mGeneralConfirmationChecklistResponse.transactionDataView, this)
            sumberTransaksiRevAdapter = SumberTransaksiRevAdapter(mGeneralConfirmationChecklistResponse.amountDataView, this)
            totalTransaksiRevAdapter = TotalTransaksiRevAdapter(mGeneralConfirmationChecklistResponse.totalDataView, this)
            setvalidasiButton()
        }else if(mIsSnkTabungan){
            isSyarat = true
            isRiplay = true
            btnSubmit.text = mGeneralConfirmationResponse.btnText
            tvNorek.text =  mGeneralConfirmationResponse.sourceAccountDataView.subtitle
            lySyarat.visibility = View.GONE
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mGeneralConfirmationResponse.transactionDataView, this)
            sumberTransaksiRevAdapter = SumberTransaksiRevAdapter(mGeneralConfirmationResponse.amountDataView, this)
            totalTransaksiRevAdapter = TotalTransaksiRevAdapter(mGeneralConfirmationResponse.totalDataView, this)
            setvalidasiButton()
        }
        else{
            isSyarat = false
            isRiplay = false
            btnSubmit.text = mGeneralConfirmationResponse.btnText
            tvNorek.text =  mGeneralConfirmationResponse.sourceAccountDataView.subtitle
            lySyarat.visibility = View.VISIBLE
            detailTransaksiRevampAdapter = DetailTransaksiRevampAdapter(mGeneralConfirmationResponse.transactionDataView, this)
            sumberTransaksiRevAdapter = SumberTransaksiRevAdapter(mGeneralConfirmationResponse.amountDataView, this)
            totalTransaksiRevAdapter = TotalTransaksiRevAdapter(mGeneralConfirmationResponse.totalDataView, this)
            setvalidasiButton()
        }

        rvDetail.adapter= detailTransaksiRevampAdapter


        rvPayment.setHasFixedSize(true)
        rvPayment.layoutManager = LinearLayoutManager(
                applicationContext, RecyclerView.VERTICAL, false
        )

        rvPayment.adapter=sumberTransaksiRevAdapter

        rvTotal.setHasFixedSize(true)
        rvTotal.layoutManager = LinearLayoutManager(
                applicationContext,
                RecyclerView.VERTICAL,
                false
        )

        rvTotal.adapter =totalTransaksiRevAdapter

        if (!mIsFromEmas){
            if (!mGeneralConfirmationResponse.amountDataView.isEmpty()){
                rvPayment.setHasFixedSize(true)
                rvPayment.layoutManager = LinearLayoutManager(
                        applicationContext, RecyclerView.VERTICAL, false)

                sumberTransaksiRevAdapter = SumberTransaksiRevAdapter(mGeneralConfirmationResponse.amountDataView, this)
                rvPayment.adapter=sumberTransaksiRevAdapter
            }
            else{
                rvPayment.visibility = View.GONE
            }

            if (!mGeneralConfirmationResponse.totalDataView.isEmpty()){
                rvTotal.setHasFixedSize(true)
                rvTotal.layoutManager = LinearLayoutManager(
                        applicationContext,
                        RecyclerView.VERTICAL,
                        false
                )

                totalTransaksiRevAdapter = TotalTransaksiRevAdapter(mGeneralConfirmationResponse.totalDataView, this)
                rvTotal.adapter =totalTransaksiRevAdapter
            }else{
                rvTotal.visibility = View.GONE
            }

            if (mGeneralConfirmationResponse.amountDataView.isEmpty() && mGeneralConfirmationResponse.totalDataView.isEmpty()){
                lyPayment.visibility = View.GONE
            }
        }

        if (mIsFromEmas){
            btnSubmit.text = mGeneralConfirmationChecklistResponse.buttonText
        }else{
            btnSubmit.text = mGeneralConfirmationResponse.btnText
        }

    }

    override fun onClick(p0: View?) {
        when(p0?.id){
            R.id.ll_syarat_ketentuan -> {
                GeneralSyaratActivity.launchIntent(this,
                        mGeneralConfirmationResponse.snk)
            }
            R.id.ll_riplay_msg -> {
                mGeneralConfirmationResponse.riplay?.let {
                    RiplayActivity.launchIntent(this, it)
                }
            }
            R.id.btnSubmit -> {
                if (mIsFromEmas){
                    GeneralSNKwithChecklistActivity.launchIntent(this, mGeneralConfirmationChecklistResponse, mUrlPayment)
                }else if(mIsSnkTabungan){
                    GeneralSNKWithPinActivity.launchIntent(this,mGeneralConfirmationResponse,mUrlPayment,true, urlPending = mUrlPending)
                }
                else{
                    openPin()
                }
            }
        }
    }

    private fun openPin() {
        val pinFragment = PinFragment(this, this)
        pinFragment.show()
    }


    fun setvalidasiButton() {
        if (isSyarat && isRiplay) {
            setButton(true)
        } else {
            setButton(false)
        }
    }


    override fun onSuccessGetPayment(paymentResponse: ReceiptRevampResponse?) {
        if (paymentResponse!!.immediatelyFlag == true){
            ReceiptTabunganActivity.launchIntent(
                    this,
                    paymentResponse,
                    false,GeneralHelper.getString(R.string.btn_receipt_tabungan))
        }
        else PendingTabunganActivity.launchIntent(
            this,
            paymentResponse,
                mUrlPending
        )
    }

    override fun onException93(message: String?) {
        val i = Intent()
        i.putExtra(Constant.TAG_ERROR_MESSAGE, message)
        setResult(RESULT_CANCELED, i)
        finish()
    }

    override fun onException01(message: String?) {
        GeneralHelper.showDialogGagalBackDescBerubah(this, Constant.TRANSAKSI_GAGAL, message)
        setResult(RESULT_OK)
    }

    override fun onSuccessGetPaymentEmas(receiptEmasResponse: ReceiptRevampResponse) {
        ReceiptGagalAFTActivity.launchIntent(this, receiptEmasResponse,false)
    }

    override fun onSuccessGetPaymentEmasFailed(receiptGagalEmasResponse: ReceiptGagalEmasResponse) {
        ReceiptGagalRegistrasiActivity.launchIntent(this, receiptGagalEmasResponse)
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_BUKA_REKENING) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null){
                this.setResult(RESULT_CANCELED, data)
                finish()
            }else if(resultCode == RESULT_FIRST_USER && data != null){
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        } else if (requestCode == Constant.REQ_FORGET_PIN) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK)
                finish()
            }
        }  else if (requestCode == Constant.REQ_PETTUNJUK1 && data != null) {
            if (resultCode == RESULT_OK) {
                isSyarat = java.lang.Boolean.parseBoolean(data.getStringExtra("checkbox"))
                if (isSyarat) cbSyarat.setBackgroundResource(R.drawable.checkbox_on) else cbSyarat.setBackgroundResource(
                        R.drawable.checkbox_off
                )
                setvalidasiButton()
            }
        }else if (requestCode == Constant.REQ_PETTUNJUK2 && data != null) {

        }else if (requestCode == Constant.REQ_PAYMENT) {
            if (resultCode == RESULT_OK) {
                this.setResult(RESULT_OK, data)
                finish()
            } else if (resultCode == RESULT_CANCELED && data != null) {
                this.setResult(RESULT_CANCELED, data)
                finish()
            } else if (resultCode == RESULT_FIRST_USER && data != null) {
                this.setResult(RESULT_FIRST_USER, data)
                finish()
            }
        } else if (requestCode == Constant.REQ_RIPLAY && data != null){
            if (resultCode == RESULT_OK) {
                isRiplay = java.lang.Boolean.parseBoolean(data.getStringExtra("checkbox_riplay"))
                if (isRiplay) cbRiplay.setBackgroundResource(R.drawable.checkbox_on)
                else cbRiplay.setBackgroundResource(R.drawable.checkbox_off)
                setvalidasiButton()
            }
        }

    }

    override fun onSendPinComplete(pin: String?) {
            presenter.getDataPayment(
                    pin,
                    "",
                    mGeneralConfirmationResponse,
                    isFromFastMenu
            )
    }

    override fun onLupaPin() {
        //TO DO routing
        if (isFromFastMenu) LupaPinFastActivity.launchIntent(this) else LupaPinActivity.launchIntent(
                this
        )
    }

    override fun onBackPressed() {
        if (mIsFromEmas){
            finish()
        }else{
            val dialogExitCustom = DialogExitCustom({ this.cancelTransaction() }, GeneralHelper.getString(R.string.title_dialog_exit_konfirmasi), GeneralHelper.getString(R.string.content_dialog_exit_konfirmasi)
            )
            val ft = this.supportFragmentManager.beginTransaction()
            ft.add(dialogExitCustom, null)
            ft.commitAllowingStateLoss()
        }
    }
    private fun cancelTransaction() {
        super.onBackPressed()
    }
    fun setButton(boolean: Boolean){
        if (boolean){
            btnSubmit.isEnabled = true
            btnSubmit.setTextColor(GeneralHelper.getColor(R.color.whiteColor))
            btnSubmit.background = ContextCompat.getDrawable(this, R.drawable.button_primary_bg)
        }else{
            btnSubmit.isEnabled = false
            btnSubmit.setTextColor(GeneralHelper.getColor(R.color.neutral_light60))
            btnSubmit.background = ContextCompat.getDrawable(this, R.drawable.rounded_button_disabled_revamp)
        }
    }

    override fun onNetworkConnectionChanged(time: Float) {
        timeCon = time
        runOnUiThread {
            if (time < 1000 && time != 0f) {
                imgInternetIndicator.setBackgroundResource(R.drawable.internet_indicator_online)
            } else {
                imgInternetIndicator.setBackgroundResource(R.drawable.internet_indicator_offline)
            }
        }
    }

    private fun connectionStatus() {
        connectionReceiver = ConnectionReceiver()
        connectionReceiver!!.setConnectivityReceiverListener(this, 0)
        val filter = IntentFilter("android.net.conn.CONNECTIVITY_CHANGE")
        registerReceiver(connectionReceiver, filter)
    }

    override fun onStart() {
        super.onStart()
        actStatus = 0
        if (actStatus != 1) connectionStatus()
    }

    override fun onStop() {
        super.onStop()
        actStatus = 1

        try {
            connectionReceiver!!.setConnectivityReceiverListener(null, 0)
            unregisterReceiver(connectionReceiver)
        } catch (e: IllegalArgumentException) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, e.message!!)
            }
        }
    }

    override fun onPause() {
        super.onPause()
        actStatus = 1
        try {
            connectionReceiver!!.setConnectivityReceiverListener(null, 0)
            unregisterReceiver(connectionReceiver)
        } catch (e: IllegalArgumentException) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, e.message!!)
            }
        }
    }

}