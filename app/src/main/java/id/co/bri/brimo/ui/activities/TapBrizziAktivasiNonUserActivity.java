package id.co.bri.brimo.ui.activities;


import android.app.Activity;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.nfc.NfcAdapter;
import android.nfc.NfcManager;
import android.nfc.Tag;
import android.nfc.tech.IsoDep;
import android.os.Build;
import android.os.Bundle;

import javax.inject.Inject;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IPresenter.brizzi.ITapAktivasiBrizziNonPresenter;
import id.co.bri.brimo.contract.IView.brizzi.ITapAktivasiBrizziNonView;
import id.co.bri.brimo.databinding.ActivityTapBrizziAktivasiBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.apimodel.response.AktivasiBrizziResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;
import id.co.bri.brizzi.Brizzi;
import id.co.bri.brizzi.RCOptions;

public class TapBrizziAktivasiNonUserActivity extends BaseActivity implements ITapAktivasiBrizziNonView {

    private ActivityTapBrizziAktivasiBinding binding;

    private NfcManager nfcManager;
    private NfcAdapter adapter;
    private String RCNulisKartu;

    protected static AktivasiBrizziResponse mAktivasi;
    protected static Integer mState;
    protected static String mJourneyType = Constant.CIAType.TYPE_BRIZZI_TOP_UP_OTHER;

    Brizzi mBrizzi;


    @Inject
    ITapAktivasiBrizziNonPresenter<ITapAktivasiBrizziNonView> presenter;

    public static void launchIntent(Activity caller, Integer state) {
        Intent intent = new Intent(caller, TapBrizziAktivasiNonUserActivity.class);
        mState = state;
        caller.startActivityForResult(intent, Constant.REQ_PAYMENT);
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        binding = ActivityTapBrizziAktivasiBinding.inflate(getLayoutInflater());
        setContentView(binding.getRoot());

        GeneralHelper.setToolbar(this, binding.tbBriva.toolbar, "BRIZZI");

        injectDependency();
//      Glide.with(getApplicationContext()).load(R.drawable.cek_brizzi).into(imageView);
        nfcManager = (NfcManager) getApplicationContext().getSystemService(Context.NFC_SERVICE);
        adapter = nfcManager.getDefaultAdapter();

        if (adapter == null) {
            adapter = NfcAdapter.getDefaultAdapter(this);
            if (adapter == null) {
                showSnackbarErrorMessage(GeneralHelper.getString(R.string.brizzi_device_tidak_support_nfc), ALERT_ERROR, this, false);
            } else {
                if (!adapter.isEnabled()) {
                    showSnackbarErrorMessage("NFC tidak aktif. Silakan Aktifkan NFC.", ALERT_ERROR, this, false);
                } else {
                    onNewIntent(getIntent());
                }
            }

        }
    }

    protected void injectDependency() {
        getActivityComponent().inject(this);

        if (presenter != null) {
            presenter.setView(this);
            presenter.start();
            presenter.setInquiryUrl(GeneralHelper.getString(R.string.url_non_user_check_top_up_brizzi));
            presenter.setPaymentUrl(GeneralHelper.getString(R.string.url_non_user_aktivasi_top_up_brizzi));
            presenter.setValidateUrl(GeneralHelper.getString(R.string.url_non_user_check_validate_brizzi));
        }
    }

    @Override
    protected void onResume() {
        super.onResume();

        if (adapter != null) {
            Intent intent = new Intent(this, TapBrizziAktivasiNonUserActivity.class);
            intent.setFlags(Intent.FLAG_RECEIVER_REPLACE_PENDING);

            //Perbaikan pendingIntent android 31
            int currentFlags = Build.VERSION.SDK_INT >= Build.VERSION_CODES.S ? PendingIntent.FLAG_MUTABLE | PendingIntent.FLAG_UPDATE_CURRENT : 0;
            PendingIntent pendingIntent = PendingIntent.getActivity(this, 0, intent, currentFlags);

            IntentFilter[] filters = new IntentFilter[]{};
            adapter.enableForegroundDispatch(this, pendingIntent, filters, null);
        }
    }


    @Override
    protected void onPause() {
        super.onPause();
        adapter.disableForegroundDispatch(this);
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        try {
            Tag tag = intent.getParcelableExtra(NfcAdapter.EXTRA_TAG);
            IsoDep brizziTag = IsoDep.get(tag);
            mBrizzi = new Brizzi(brizziTag);
            presenter.initUpdateBalancePres(mBrizzi, isFromFastMenu);
        } catch (Exception e) {
            showSnackbarErrorMessage("Kartu Gagal Terbaca", ALERT_ERROR, this, false);
        }

    }

    @Override
    public void showErrorMessage(String message) {
        showSnackbarErrorMessage(message, ALERT_ERROR, this, false);
    }


    /**
     * Method berhasil setelah checkbalance lanjut masuk ke tahap commit
     */
    @Override
    public void onSuccesGetCommitAktivasi(AktivasiBrizziResponse aktivasiBrizziResponse) {
        mAktivasi = aktivasiBrizziResponse;
        presenter.commitContinuePres(aktivasiBrizziResponse, mBrizzi, isFromFastMenu);
    }

    /**
     * Method berhasil setelah commit lanjut ke receipt
     */
    @Override
    public void onSuccesGetValidateAtktivasi() {
        RecieptBrizziActivty.launchIntent(this, mAktivasi, mJourneyType);
    }

    @Override
    public void onException93(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException01(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }


    @Override
    public void onException12(String message) {
        Intent returnIntent = new Intent();

        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }


//    @Override
//    public void onFailurPayment(String message) {
//        RCNulisKartu = mBrizzi.commitUpdateBalance(mBrizzi.rcOptions.RC_ERROR_TIMEOUT);
//
//        if (RCNulisKartu.equals(mBrizzi.rcOptions.RC_SUCCESS)) {
//            presenter.getValidateBrizzi(new ValidateRequest(mBrizzi.getCardData().getCardNumber(), mBrizzi.getCardData().getValidateRandom(), mBrizzi.getCardData().getCardBalance(), mAktivasi.getReff(), mAktivasi.getReferenceNumber()), isFromFastMenu, true);
//        } else {
//            presenter.getValidateBrizzi(new ValidateRequest(mBrizzi.getCardData().getCardNumber(), mBrizzi.getCardData().getValidateRandom(), mBrizzi.getCardData().getCardBalance(), mAktivasi.getReff(), mAktivasi.getReferenceNumber()), isFromFastMenu, false);
//
//        }
//    }

    @Override
    public void onException93Validate(String message) {
        Intent returnIntent = new Intent();
        presenter.commitUpdateBalancePres(mBrizzi, RCOptions.RC_ERROR + RCOptions.RC_ERROR, isFromFastMenu);
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException01Validate(String message) {
        Intent returnIntent = new Intent();
        presenter.commitUpdateBalancePres(mBrizzi, RCOptions.RC_ERROR + RCOptions.RC_ERROR, isFromFastMenu);
        returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI, message);

        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    public void onException12Validate(String message) {
        Intent returnIntent = new Intent();
        presenter.commitUpdateBalancePres(mBrizzi, RCOptions.RC_ERROR + RCOptions.RC_ERROR, isFromFastMenu);

        if (message.contains("PIN")) {
            returnIntent.putExtra(Constant.TAG_ERROR_PIN_BRIZZI, message);
        } else {
            returnIntent.putExtra(Constant.TAG_ERROR_MESSAGE_AKTIVASI_BRIZZI, message);
        }
        this.setResult(RESULT_CANCELED, returnIntent);
        this.finish();
    }

    @Override
    protected void onDestroy() {
        presenter.stop();
        binding = null;
        super.onDestroy();
    }

    @Override
    public void onBackPressed() {
        presenter.stop();
        super.onBackPressed();
    }

}