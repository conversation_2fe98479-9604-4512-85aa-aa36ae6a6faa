package id.co.bri.brimo.models.apimodel.response;

import android.os.Parcel;
import android.os.Parcelable;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class GeneralOtpResponse implements Parcelable {
    @SerializedName("description")
    private String description;
    @SerializedName("otp_expired_in_second")
    private Integer otpExpiredInSecond;
    @SerializedName("otp_resend_in_second")
    private Integer otpResendInSecond;
    @SerializedName("reference_number")
    private String referenceNumber;

    @SerializedName("phone")
    private String phone;

    public GeneralOtpResponse(String description, Integer otpExpiredInSecond, Integer otpResendInSecond, String referenceNumber, String phone) {
        this.description = description;
        this.otpExpiredInSecond = otpExpiredInSecond;
        this.otpResendInSecond = otpResendInSecond;
        this.referenceNumber = referenceNumber;
        this.phone = phone;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Integer getOtpExpiredInSecond() {
        return otpExpiredInSecond;
    }

    public void setOtpExpiredInSecond(Integer otpExpiredInSecond) {
        this.otpExpiredInSecond = otpExpiredInSecond;
    }

    public Integer getOtpResendInSecond() {
        return otpResendInSecond;
    }

    public void setOtpResendInSecond(Integer otpResendInSecond) {
        this.otpResendInSecond = otpResendInSecond;
    }

    public String getReferenceNumber() {
        return referenceNumber;
    }

    public void setReferenceNumber(String referenceNumber) {
        this.referenceNumber = referenceNumber;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    @Override
    public int describeContents() {
        return 0;
    }

    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(this.description);
        dest.writeValue(this.otpExpiredInSecond);
        dest.writeValue(this.otpResendInSecond);
        dest.writeString(this.referenceNumber);
        dest.writeString(this.phone);
    }

    public void readFromParcel(Parcel source) {
        this.description = source.readString();
        this.otpExpiredInSecond = (Integer) source.readValue(Integer.class.getClassLoader());
        this.otpResendInSecond = (Integer) source.readValue(Integer.class.getClassLoader());
        this.referenceNumber = source.readString();
        this.phone = source.readString();
    }

    public GeneralOtpResponse() {
    }

    protected GeneralOtpResponse(Parcel in) {
        this.description = in.readString();
        this.otpExpiredInSecond = (Integer) in.readValue(Integer.class.getClassLoader());
        this.otpResendInSecond = (Integer) in.readValue(Integer.class.getClassLoader());
        this.referenceNumber = in.readString();
        this.phone = in.readString();
    }

    public static final Parcelable.Creator<GeneralOtpResponse> CREATOR = new Parcelable.Creator<GeneralOtpResponse>() {
        @Override
        public GeneralOtpResponse createFromParcel(Parcel source) {
            return new GeneralOtpResponse(source);
        }

        @Override
        public GeneralOtpResponse[] newArray(int size) {
            return new GeneralOtpResponse[size];
        }
    };
}
