package id.co.bri.brimo.models.apimodel.response.notificationsetting

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailData

data class NotificationSettingResponse(
    @Expose @SerializedName("title") val title: String,
    @Expose @SerializedName("subtitle") val subTitle: String,
    @Expose @SerializedName("notification_icon") val notificationIcon: String,
    @Expose @SerializedName("notification_options") val notificationOptions: MutableList<NotificationOptions>
)

data class NotificationOptions(
    @Expose @SerializedName("type") val type: String,
    @Expose @SerializedName("icon") val icon: String,
    @Expose @SerializedName("label") val label: String,
    @Expose @SerializedName("status") var status: Boolean,
    @Expose @SerializedName("status_string") val statusString: String,
    @Expose @SerializedName("details") var details: NotificationDetail
)

data class NotificationDetail(
    @Expose @SerializedName("title") val title: String,
    @Expose @SerializedName("data_view") val dataView: MutableList<DetailData>,
    @Expose @SerializedName("status") val status: Boolean,
    @Expose @SerializedName("subtitle") val subtitle: String,
    @Expose @SerializedName("notification_info_text") val notificationInfoText: String,
    @Expose @SerializedName("notification_status_label") val notificationStatusLabel: String,
    @Expose @SerializedName("account_number") val accountNumber: String,
    @Expose @SerializedName("current_amount") val currentAmount: String,
    @Expose @SerializedName("amount_minimum") val amountMinimum: String,
    @Expose @SerializedName("amount_minimum_string") val amountMinimumString: String,
    @Expose @SerializedName("save_button_label") val saveButtonLabel: String,
    @Expose @SerializedName("confirmation") val confirmation: Confirmation
)

data class Confirmation(
    @Expose @SerializedName("icon") val icon: String,
    @Expose @SerializedName("title") val title: String,
    @Expose @SerializedName("subtitle") var subtitle: String,
    @Expose @SerializedName("submit_label") val submitLabel: String,
    @Expose @SerializedName("cancel_label") val cancelLabel: String
)
