package id.co.bri.brimo.adapters.profilerevamp

import android.graphics.Color
import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemMenuInfoProfileActionBinding
import id.co.bri.brimo.databinding.ItemMenuInfoProfileSwitchBinding
import id.co.bri.brimo.domain.extension.gone
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.profilerevamp.ListInfoProfile

class CardProfileInfoAdapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {
    companion object {
        private const val VIEW_TYPE_SWITCH = 0
        private const val VIEW_TYPE_ACTION = 1
    }

    private var profileInfoCardList = mutableListOf<ListInfoProfile>()

    private var switchListener: (Int, Boolean, String) -> Unit = { _ , _ , _ -> }
    private var actionListener: (Int, String) -> Unit = { _ , _ ->}

    override fun getItemViewType(position: Int): Int {
        val profileInfo = profileInfoCardList[position]
        return if (profileInfo.type == "switch") {
            VIEW_TYPE_SWITCH
        } else {
            VIEW_TYPE_ACTION
        }
    }

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (viewType) {
            VIEW_TYPE_SWITCH -> {
                val binding = ItemMenuInfoProfileSwitchBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SettingCardSwitchHolder(binding)
            }

            VIEW_TYPE_ACTION -> {
                val binding = ItemMenuInfoProfileActionBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
                SettingCardActionHolder(binding)
            }

            else -> throw IllegalArgumentException("Invalid view type")
        }
    }

    override fun getItemCount(): Int = profileInfoCardList.size

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val infoProfile = profileInfoCardList[position]
        when (holder) {
            is SettingCardSwitchHolder -> {
                holder.bindData(infoProfile)
                if (itemCount-1 == position)
                    holder.binding.view.gone()
            }

            is SettingCardActionHolder -> holder.bindData(infoProfile)
        }
    }

    fun clickSwitchListener(callBack: (Int, Boolean, String) -> Unit) {
        switchListener = callBack
    }

    fun clickActionListener(callBack: (Int, String) -> Unit) {
        actionListener = callBack
    }

    inner class SettingCardSwitchHolder(val binding: ItemMenuInfoProfileSwitchBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bindData(profile: ListInfoProfile) {
            binding.tvName.text = profile.titleInfo
            GeneralHelper.loadIconTransaction(
                    binding.root.context,
                    "",
                    "",
                    binding.ivInfoProfile,
                    GeneralHelper.getImageId(binding.root.context, profile.iconInfo))
            binding.swAction.isChecked = profile.status

            binding.swAction.setOnCheckedChangeListener { _, isChecked ->
                binding.swAction.isChecked = profile.status
                switchListener(profile.id, isChecked, profile.titleInfo)
            }

            binding.ivInfoProfile.setPadding(0, 0, 0, 0)
        }
    }

    inner class SettingCardActionHolder(val binding: ItemMenuInfoProfileActionBinding) :
        RecyclerView.ViewHolder(binding.root) {

        fun bindData(profile: ListInfoProfile) {
            binding.tvName.text = profile.titleInfo
            GeneralHelper.loadIconTransaction(
                    binding.root.context,
                    "",
                    "",
                    binding.ivInfoProfile,
                    GeneralHelper.getImageId(binding.root.context, profile.iconInfo))
            binding.clAction.setOnClickListener {
                actionListener(profile.id, profile.titleInfo)
            }
            binding.ivInfoProfile.setPadding(0, 0, 0, 0)

            if (profile.titleInfo.lowercase() == binding.view.context.getString(R.string.text_logout).lowercase()) {
                binding.tvName.setTextColor(Color.parseColor("#E84040"))
                binding.ivArrow.setImageResource(R.drawable.ic_arrow_right_red)
            } else {
                binding.tvName.setTextColor(Color.parseColor("#181C21"))
                binding.ivArrow.setImageResource(R.drawable.ic_arrow_right_black)
            }
        }
    }

    fun updateData(newItems: List<ListInfoProfile>) {
        profileInfoCardList.clear()
        profileInfoCardList.addAll(newItems)
    }
}
