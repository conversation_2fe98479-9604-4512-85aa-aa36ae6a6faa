package id.co.bri.brimo.ui.fragments;

import android.animation.ObjectAnimator;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.view.KeyEvent;
import android.view.ViewTreeObserver;

import androidx.annotation.NonNull;

import id.co.bri.brimo.R;
import id.co.bri.brimo.contract.IView.IMvpView;
import id.co.bri.brimo.di.components.ActivityComponent;
import id.co.bri.brimo.domain.helpers.blurwindow.BlurPopupWindow;
import id.co.bri.brimo.models.apimodel.response.EmptyStateResponse;
import id.co.bri.brimo.models.apimodel.response.ExceptionResponse;
import id.co.bri.brimo.models.apimodel.response.GeneralResponse;
import id.co.bri.brimo.ui.activities.base.BaseActivity;

public class BasePopUpFragment extends BlurPopupWindow implements IMvpView {

    private BaseActivity mActivity;

    protected Boolean isShow = false;

    public BasePopUpFragment(@NonNull Context context) {
        super(context);
        if (context instanceof BaseActivity) {
            BaseActivity activity = (BaseActivity) context;
            this.mActivity = activity;
        }
    }

    public ActivityComponent getActivityComponent() {
        if (mActivity != null) {
            return mActivity.getActivityComponent();
        }
        return null;
    }

    public BaseActivity getBaseActivity() {
        return mActivity;
    }


    public boolean isShowing() {
        return isShow;
    }

    @Override
    protected void onShow() {
        super.onShow();
        isShow = true;
        getContentView().getViewTreeObserver().addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                getViewTreeObserver().removeGlobalOnLayoutListener(this);

                getContentView().setVisibility(VISIBLE);
                int height = getContentView().getMeasuredHeight();
                ObjectAnimator.ofFloat(getContentView(), "translationY", height, 0).setDuration(200).start();
            }
        });

    }

    @Override
    protected ObjectAnimator createDismissAnimator() {
        isShow = false;
        int height = getContentView().getMeasuredHeight();
        return ObjectAnimator.ofFloat(getContentView(), "translationY", 0, height).setDuration(200);
    }



    @Override
    protected ObjectAnimator createShowAnimator() {
        return null;
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        if(keyCode == KeyEvent.KEYCODE_BACK){
            dismiss();
            return true;
        }else {
            return super.onKeyDown(keyCode, event);
        }
    }


    @Override
    public void onRootedDevice() {
        AlertDialog.Builder alertDialog = new AlertDialog.Builder(mActivity)
                .setTitle("Peringatan")
                .setMessage("Maaf, Anda tidak diijinkan untuk masuk ke aplikasi BRImo karena perangkat Anda terindikasi telah di-root")
                .setNeutralButton("Close", new DialogInterface.OnClickListener() {
                    @Override
                    public void onClick(DialogInterface dialogInterface, int i) {
                        mActivity.finish();
                    }
                })
                .setIcon(R.drawable.ic_close_black_24dp)
                .setCancelable(false);
        alertDialog.show();
    }

    @Override
    public void showProgress() {

    }

    @Override
    public void hideProgress() {

    }

    @Override
    public void onSessionEnd(String message) {

    }

    @Override
    public void onException(String message) {

    }

    @Override
    public void onExceptionRevamp(String message) {

    }

    @Override
    public void onException06(ExceptionResponse response) {

    }

    @Override
    public void onException99(String message) {

    }

    @Override
    public void onExceptionFO(EmptyStateResponse response) {

    }

    @Override
    public void onExceptionLimitExceed(GeneralResponse response) {
        // do nothing
    }

    @Override
    public void onExceptionStatusNotMatch() {
        // do nothing
    }

    @Override
    public void onExceptionNoBackAction(String message) {

    }
}

