package id.co.bri.brimo.domain.helpers.reskin

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.os.Environment
import android.view.LayoutInflater
import android.view.View
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.calendar.CalendarHelper
import id.co.bri.brimo.models.apimodel.response.ReceiptRevampResponse
import java.io.File
import java.io.FileOutputStream

fun ReceiptRevampResponse.mappingReceipt(
    type: ReceiptType
): ReceiptEntity {
    val token = voucherDataView?.firstOrNull()?.value ?: ""

    val transactionType = if(type == ReceiptType.OTHER) billingDetail.subtitle else "Top Up E-Wallet"
    val contentList = if(type == ReceiptType.OTHER) {
        println("transactionDataView")
        if(transactionDataView!=null) transactionDataView.filterNot { data ->
            when (data.name) {
                "NO REF" -> {
                    true
                }
                "STROOM/TOKEN" -> {
                    true
                }
                else -> false
            }
        }.map { data ->
            ContentDetail(title = data.name, content = data.value)
        }.toMutableList() else mutableListOf()
    } else {
        println("hello")
        listOf(ContentDetail(title = "Jenis E-Wallet", content = billingDetail.subtitle)).toMutableList()
    }

    val headerDataView = headerDataView.filterNot { data ->
        when (data.name) {
            "Tanggal" -> {
                true
            }
            else -> false
        }
    }.toMutableList()

    return ReceiptEntity(
        nominalCount = amountDataView[0].value, token = token,
        nominalPayment = totalDataView[0].value, adminFee = amountDataView!![1].value,
        dateTime = dateTransaction, transactionType = transactionType,
        prefNumber = headerDataView[0].value,
        detailTransaction = listOf(
            DataTransactionDetail(
                iconUrl = "",
                name = sourceAccountDataView.title,
                content = "${sourceAccountDataView.subtitle} - ${sourceAccountDataView.description}"
            ),
            DataTransactionDetail(
                iconUrl = "",
                name = if(transactionDataView!!.size > 0) transactionDataView[0].value else billingDetail.title,
                content = if(transactionDataView!!.size > 0) transactionDataView[2].value else "${billingDetail.subtitle} - ${billingDetail.description}"
            )
        ).toMutableList(),
        contentList = contentList
    )
}

enum class ReceiptType {
    EWALLET, OTHER
}

data class ReceiptEntity(
    val nominalCount: String,
    var token: String = "",
    val dateTime: String,
    var prefNumber: String = "",
    var transactionType: String = "",
    val nominalPayment: String = "",
    val adminFee: String,
    var detailTransaction: MutableList<DataTransactionDetail> = mutableListOf(),
    var contentList: MutableList<ContentDetail> = mutableListOf()
)

data class DataTransactionDetail(
    var iconUrl: String = "",
    var name: String = "",
    var content: String = "",
)

data class ContentDetail(
    val title: String,
    val content: String
)

class ImageHelperReskin {
    companion object {
        @JvmStatic
        fun captureFullView(view: View): Bitmap {
            view.measure(
                View.MeasureSpec.makeMeasureSpec(view.width, View.MeasureSpec.EXACTLY),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            view.layout(0, 0, view.measuredWidth, view.measuredHeight)

            val bitmap = Bitmap.createBitmap(view.measuredWidth, view.measuredHeight, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)

            val bg = view.background
            if (bg != null) {
                bg.draw(canvas)
            } else {
                canvas.drawColor(Color.WHITE)
            }

            view.draw(canvas)

            return bitmap
        }

        @JvmStatic
        fun captureFullView(context: Context, layoutResId: Int): Bitmap {
            val inflater = LayoutInflater.from(context)
            val view = inflater.inflate(layoutResId, null)

            // Ukur dan layout secara manual
            view.measure(
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED),
                View.MeasureSpec.makeMeasureSpec(0, View.MeasureSpec.UNSPECIFIED)
            )
            view.layout(0, 0, view.measuredWidth, view.measuredHeight)

            // Buat bitmap dan canvas
            val bitmap = Bitmap.createBitmap(view.measuredWidth, view.measuredHeight, Bitmap.Config.ARGB_8888)
            val canvas = Canvas(bitmap)

            // Gambar background jika ada
            view.background?.draw(canvas) ?: canvas.drawColor(Color.WHITE)

            // Gambar view ke canvas
            view.draw(canvas)

            return bitmap
        }

        private fun generateNameReceipt(): String {
            var tag: String
            val dateTime = CalendarHelper.getCurrentTimeReceipt()
            tag = try {
                Constant.TAG_START_NAME + dateTime + Constant.TAG_END_NAME
            } catch (e: Exception) {
                Constant.TAG_START_NAME + Constant.TAG_END_NAME
            }
            return tag
        }

        @JvmStatic
        fun saveBitmap(bm: Bitmap, res: (File) -> Unit) {
            val path = Environment.getExternalStorageDirectory().absolutePath + Constant.URI_DOWNLOAD
            val dir = File(path)
            if (!dir.exists()) dir.mkdirs()

            val file = File(dir, generateNameReceipt())
            try {
                FileOutputStream(file).use { fOut ->
                    bm.compress(Bitmap.CompressFormat.PNG, 90, fOut)
                    fOut.flush()
                }
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) {
                    // Tambahkan log jika diperlukan
                }
            }

            res.invoke(file)
        }
    }
}