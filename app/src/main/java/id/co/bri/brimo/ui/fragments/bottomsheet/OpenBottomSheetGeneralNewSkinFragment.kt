package id.co.bri.brimo.ui.fragments.bottomsheet

import android.graphics.drawable.Drawable
import androidx.fragment.app.FragmentManager

object OpenBottomSheetGeneralNewSkinFragment {

    fun showDialogConfirmationWithButtons(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        firstBtnTxt: String = "Coba Lagi",
        secondBtnTxt: String = "Atur Ulang Password",
        isClickableOutside: Boolean = false,
        onFirstButtonClicked: () -> Unit = {},
        onSecondButtonClicked: () -> Unit = {}
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment().apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONNOOUTLINEBTN) // atau buat DialogType baru jika perlu
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
//            withBackgroundSecondBtn = false // Agar jadi tombol outline
            setOnBtnFirst {
                onFirstButtonClicked()
                dismiss()
            }
            setOnBtnSecond {
                onSecondButtonClicked()
                dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }


    fun showDialogInformation(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATION)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
            }
            setOnDismiss {
                bottomSheet.dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    fun showDialogInformationNfcPayment(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTopTxt: String,
        titleTopDsc: String,
        btnFirstFunction: () -> Unit = {},
        onBtnSecondClicked: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        isSecondBtnBordered: Boolean = false,
    ) {
        if (fragmentManager.isDestroyed) return

        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.ACTIVEINACTIVENFC)
            imagePath = imgPath
            imageName = imgName
            titleTopText = titleTopTxt
            titleTopDesc = titleTopDsc
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setBtnSecondBorder(isSecondBtnBordered)

            setOnBtnFirst {
                btnFirstFunction.invoke()
                dismiss()
            }
            setOnBtnSecond {
                onBtnSecondClicked.invoke()
                dismiss()
            }
            if (!fragmentManager.isDestroyed) {
                show(fragmentManager, "")
            }
            isClickable = isClickableOutside
        }
    }

    fun showDialogInformationWithOutImage(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONWITHOUTIMAGE)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnDismiss {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    fun showDialogInformationWithAction(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnThirdFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        showCloseButton: Boolean,
        firstBtnTxt: String = "",
        thirdBtnTxt: String = "",
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONNOOUTLINEBTN)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            thirdBtnText = thirdBtnTxt
            closeButtonVisible = showCloseButton
            setOnBtnFirst {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            setOnDismiss {
                btnThirdFunction()
                bottomSheet.dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }


    fun showDialogInformation(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnThirdFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        thirdBtnTxt: String = "",
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONNOOUTLINEBTN)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            thirdBtnText = thirdBtnTxt
            setOnDismiss {
                bottomSheet.dismiss()
            }
            setOnBtnFirst(btnFirstFunction)
            setOnBtnThird(btnThirdFunction)
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    fun showDialogConfirmation(
        fragmentManager: FragmentManager,
        imgDrawable: Int,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnSecondFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        secondBtnTxt: String = "",
        withBgSecondBtn: Boolean = true,
        showCloseButton: Boolean = false
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.CONFIRMATION)
            imageDrawable = imgDrawable
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            secondBtnText = secondBtnTxt
            setOnDismiss {
                bottomSheet.dismiss()
            }
            setOnBtnFirst(btnFirstFunction)
            setOnBtnSecond(btnSecondFunction)
            isClickable = isClickableOutside
            withBackgroundSecondBtn = withBgSecondBtn
            closeButtonVisible = showCloseButton
            show(fragmentManager, "")
        }
    }

    fun showDialogInformationWithNoImage(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONWITHOUTIMAGE)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnDismiss {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    fun showDialogInformationHtmlSubtitle(
        fragmentManager: FragmentManager,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = ""
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONHTML)
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            setOnDismiss {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

    fun showDialogInformationWithActionAndTopText(
        fragmentManager: FragmentManager,
        txtTopTitle: String,
        txtTopDesc: String,
        imgPath: String,
        imgName: String,
        titleTxt: String,
        subTitleTxt: String,
        btnFirstFunction: () -> Unit = {},
        btnThirdFunction: () -> Unit = {},
        isClickableOutside: Boolean = true,
        firstBtnTxt: String = "",
        thirdBtnTxt: String = "",
    ) {
        val bottomSheet = BottomSheetGeneralNewSkinFragment()
        bottomSheet.apply {
            setFieldType(BottomSheetGeneralNewSkinFragment.DialogType.INFORMATIONWITHTOPTEXT)
            titleTopText = txtTopTitle
            titleTopDesc = txtTopDesc
            imagePath = imgPath
            imageName = imgName
            titleText = titleTxt
            subtitleText = subTitleTxt
            firstBtnText = firstBtnTxt
            thirdBtnText = thirdBtnTxt
            setOnBtnFirst {
                btnFirstFunction()
                bottomSheet.dismiss()
            }
            setOnDismiss {
                btnThirdFunction()
                bottomSheet.dismiss()
            }
            show(fragmentManager, "")
            isClickable = isClickableOutside
        }
    }

}