package id.co.bri.brimo.adapters.pinadapter

import android.content.Context
import android.text.Editable
import android.text.TextWatcher
import android.view.KeyEvent
import android.view.LayoutInflater
import android.view.ViewGroup
import android.widget.EditText
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.databinding.OtpRevamp2Binding
import id.co.bri.brimo.databinding.OtpRevampBinding
import id.co.bri.brimo.util.extension.setOnPasteListener

class OtpRevampAdapter(context: Context, private var layoutItem: Int) : BasePinAdapter(context) {

    var onCompleteOtpListener: ((String) -> Unit)? = null

    private val otpArray = CharArray(6) { ' ' }
    private var isCompleteOtp = false

    var recyclerView: RecyclerView? = null

    override fun onAttachedToRecyclerView(recyclerView: RecyclerView) {
        super.onAttachedToRecyclerView(recyclerView)
        this.recyclerView = recyclerView
    }

    override fun getItemCount(): Int = otpArray.size

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
        return when (layoutItem) {
            1 -> OtpRevampViewHolder(
                OtpRevampBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            2 -> OtpRevamp2ViewHolder(
                OtpRevamp2Binding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )

            else -> OtpViewHolder(
                OtpRevampBinding.inflate(
                    LayoutInflater.from(parent.context),
                    parent,
                    false
                )
            )
        }
    }

    override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
        val viewHolder = holder as OtpViewHolder
        val editText = viewHolder.binding.tvOtpNumber

        editText.removeTextChangedListener(viewHolder.textWatcher)
        editText.setOnKeyListener(null)

        editText.setText(if (otpArray[position] == ' ') "" else otpArray[position].toString())
        editText.setSelection(editText.text.length)

        editText.showSoftInputOnFocus = false
        editText.setBackgroundResource(R.drawable.selector_input_field_ns)

        if (position == otpArray.indexOfFirst { it == ' ' }) {
            editText.post {
                editText.requestFocus()
                editText.isSelected = true
            }
        } else {
            editText.clearFocus()
            editText.isSelected = false
        }

        editText.setOnKeyListener { _, keyCode, event ->
            if (event.action == KeyEvent.ACTION_DOWN && keyCode == KeyEvent.KEYCODE_DEL) {
                if (otpArray[position] != ' ') {
                    otpArray[position] = ' '
                    editText.setText("")
                    editText.requestFocus()
                } else if (position > 0) {
                    otpArray[position - 1] = ' '
                    notifyItemChanged(position - 1)
                    recyclerView?.post {
                        (recyclerView?.findViewHolderForAdapterPosition(position - 1) as? OtpViewHolder)
                            ?.binding?.tvOtpNumber?.requestFocus()
                    }
                }
                return@setOnKeyListener true
            }
            false
        }

        editText.setOnPasteListener { pastedText ->
            pastedText.forEachIndexed { index, char ->
                if (index < otpArray.size && char.isDigit()) {
                    otpArray[index] = char
                }
            }
            notifyDataSetChanged()
            checkCompleteOtp()
        }

        editText.addTextChangedListener(viewHolder.textWatcher)
    }

    fun getOtp(): String = String(otpArray).trim()

    fun setOtp(otp: String) {
        deleteAllInsertedPin()
        otp.toCharArray().forEachIndexed { index, char ->
            if (index < otpArray.size) {
                otpArray[index] = char
            }
        }
        notifyDataSetChanged()
        checkCompleteOtp()
    }

    fun addInsertedPin(pin: String) {
        for (i in otpArray.indices) {
            if (otpArray[i] == ' ') {
                otpArray[i] = pin.first()
                notifyItemChanged(i)
                recyclerView?.post {
                    if (i + 1 < otpArray.size) {
                        (recyclerView?.findViewHolderForAdapterPosition(i + 1) as? OtpViewHolder)
                            ?.binding?.tvOtpNumber?.requestFocus()
                    }
                }
                checkCompleteOtp()
                break
            }
        }
    }

    fun deleteInsertedPin() {
        isCompleteOtp = false
        for (i in otpArray.indices.reversed()) {
            if (otpArray[i] != ' ') {
                otpArray[i] = ' '
                notifyItemChanged(i)
                recyclerView?.post {
                    (recyclerView?.findViewHolderForAdapterPosition(i) as? OtpViewHolder)
                        ?.binding?.tvOtpNumber?.requestFocus()
                }
                break
            }
        }
    }

    fun deleteAllInsertedPin() {
        isCompleteOtp = false
        otpArray.fill(' ')
        notifyDataSetChanged()
    }

    private fun checkCompleteOtp() {
        if (otpArray.none { it == ' ' }) {
            isCompleteOtp = true
            onCompleteOtpListener?.invoke(getOtp())
        }
    }

    inner class OtpRevampViewHolder(val binding: OtpRevampBinding) :
        RecyclerView.ViewHolder(binding.root)

    inner class OtpRevamp2ViewHolder(val binding: OtpRevamp2Binding) :
        RecyclerView.ViewHolder(binding.root)

    inner class OtpViewHolder(val binding: OtpRevampBinding) : RecyclerView.ViewHolder(binding.root) {
        val textWatcher = object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun afterTextChanged(s: Editable?) {}

            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {
                val pos = bindingAdapterPosition
                if (pos == RecyclerView.NO_POSITION) return
                if (!s.isNullOrEmpty()) {
                    otpArray[pos] = s[0]

                    if (pos < otpArray.size - 1) {
                        recyclerView?.post {
                            (recyclerView?.findViewHolderForAdapterPosition(pos + 1) as? OtpViewHolder)
                                ?.binding?.tvOtpNumber?.requestFocus()
                        }
                    } else {
                        checkCompleteOtp()
                        binding.tvOtpNumber.clearFocus()
                    }
                }
            }
        }
    }
}
