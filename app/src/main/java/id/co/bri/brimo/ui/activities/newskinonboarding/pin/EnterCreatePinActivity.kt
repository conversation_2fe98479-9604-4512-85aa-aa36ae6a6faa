package id.co.bri.brimo.ui.activities.newskinonboarding.pin

import android.annotation.SuppressLint
import android.content.Intent
import android.os.Bundle
import androidx.activity.result.contract.ActivityResultContracts
import id.co.bri.brimo.databinding.ActivityEnterPinBinding
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.fragments.pin.PinEntryFragment
import id.co.bri.brimo.ui.fragments.pin.PinEntryListener

class EnterCreatePinActivity : NewSkinBaseActivity(), PinEntryListener {

    private lateinit var binding: ActivityEnterPinBinding

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    @SuppressLint("CommitTransaction")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEnterPinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val pinFragment = PinEntryFragment().apply {
            headerTitle = "PIN"
            descriptionText = "Masukkan PIN baru"
            infoText = "PIN ini akan dipakai untuk konfirmasi transaksi.\n" +
                    "Gunakan PIN yang unik dan sulit ditebak."
            isForgotPinVisible = false
            setPinEntryListener(this@EnterCreatePinActivity)
        }

        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainerPin.id, pinFragment)
            .commit()
    }

    override fun onPinComplete(pin: String) {
        val intent = Intent(this, EnterCreatePinConfirmActivity::class.java).apply {
            putExtra("EXTRA_CREATED_PIN", pin)
        }
        launcher.launch(intent)
    }

    override fun onPinError(errorMessage: String) {
    }

    override fun onForgotPinClicked() {
    }
}