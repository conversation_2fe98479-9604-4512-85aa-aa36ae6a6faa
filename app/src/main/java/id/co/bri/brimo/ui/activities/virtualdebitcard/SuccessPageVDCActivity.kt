package id.co.bri.brimo.ui.activities.virtualdebitcard

import android.app.Activity
import android.content.Intent
import android.graphics.Color
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.DiskCacheStrategy
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.virtualdebitcard.DetailDataVDCAdapter
import id.co.bri.brimo.adapters.virtualdebitcard.DetailDataVDCVerticalAdapter
import id.co.bri.brimo.contract.IPresenter.virtualdebitcard.ISuccessPageVDCPresenter
import id.co.bri.brimo.contract.IView.virtualdebitcard.ISuccessPageVDCView
import id.co.bri.brimo.databinding.ActivitySuccessPageVirtualCardBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.CheckStatusCreateVDCResponse
import id.co.bri.brimo.models.apimodel.response.virtualdebitcard.DetailVDCResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import javax.inject.Inject

class SuccessPageVDCActivity : BaseActivity(), ISuccessPageVDCView {

    @Inject
    lateinit var presenter: ISuccessPageVDCPresenter<ISuccessPageVDCView>

    private lateinit var binding: ActivitySuccessPageVirtualCardBinding

    private lateinit var product: CheckStatusCreateVDCResponse
    private val detailDataVDCAdapter = DetailDataVDCAdapter()
    private val detailDataCardVDCAdapter = DetailDataVDCVerticalAdapter()
    private val gson = Gson()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivitySuccessPageVirtualCardBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val gson = Gson()
        product = gson.fromJson(
            intent.getStringExtra(Constant.TAG_CONTENT),
            CheckStatusCreateVDCResponse::class.java
        )

        this.window.apply {
            clearFlags(WindowManager.LayoutParams.FLAG_TRANSLUCENT_STATUS)
            addFlags(WindowManager.LayoutParams.FLAG_DRAWS_SYSTEM_BAR_BACKGROUNDS)
            decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
            statusBarColor = Color.TRANSPARENT
        }

        binding.animationView.playAnimation()
        binding.confetti.playAnimation()

        injectDependency()
        setupView()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
    }

    private fun setupView() {
        binding.titleTxt.text = product.headerData.title
        binding.subTitleTxt.text = product.headerData.subTitle
        binding.dateTxt.text = product.dateTransaction
        Glide.with(this)
            .load(product.virtualCardData.imageCardVertical)
            .diskCacheStrategy(DiskCacheStrategy.NONE)
            .skipMemoryCache(true)
            .placeholder(R.drawable.bg_vdc_vertical)
            .into(binding.dataView.ivCard)
        binding.footerTxt.text = product.footer
        GeneralHelper.setWebViewStandart(
            binding.wvFooter,
            "",
            product.footerHtml
        )
        setupAdapterCard()
        setupAdapterAccount()
        setupExpandData()
        binding.btnSubmit.setOnClickListener { getDetailVDC() }
    }

    private fun setupExpandData() {
        val params = binding.ivDot2.layoutParams as ConstraintLayout.LayoutParams
        binding.clCollapse.setOnClickListener {
            binding.clExpand.visibility = View.VISIBLE
            binding.clCollapse.visibility = View.GONE

            params.topToBottom = R.id.cl_expand
            params.verticalBias = 0f
            binding.ivDot2.layoutParams = params
        }

        binding.llExpand.setOnClickListener {
            binding.clExpand.visibility = View.GONE
            binding.clCollapse.visibility = View.VISIBLE

            params.topToBottom = R.id.cl_collapse
            params.verticalBias = 0f
            binding.ivDot2.layoutParams = params
        }
    }

    private fun getDetailVDC() {
        presenter.setUrlDetailVDC(getString(R.string.url_v1_detail_vdc))
        presenter.getDetailVDC(cardNumber = product.cardNumber)
    }

    private fun setupAdapterCard() {
        with(binding.dataView.rvData) {
            layoutManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = detailDataCardVDCAdapter.apply {
                detailData = product.detailCardDataView.toMutableList()
            }
        }
    }

    private fun setupAdapterAccount() {
        binding.rvAccountData.apply {
            layoutManager =
                LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
            adapter = detailDataVDCAdapter
        }
        detailDataVDCAdapter.detailData = product.detailAccountDataView.toMutableList()
    }

    override fun onDestroy() {
        super.onDestroy()
        presenter.stop()
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            if (result.resultCode == Activity.RESULT_OK) {
                // There are no request codes
                // val data: Intent? = result.data
            } else {
                if (result.data != null) {
                    setResult(RESULT_CANCELED, result.data)
                    finish()
                }
            }
        }

    override fun onSuccessGetDetailVDC(response: DetailVDCResponse) {
        val responseJson = gson.toJson(response)
        val intent = Intent(this, DetailVDCActivity::class.java)
        intent.putExtra(Constant.TAG_TYPE, "fromSuccess")
        intent.putExtra(Constant.TAG_CONTENT, responseJson)
        startActivityIntent.launch(intent)
    }
}