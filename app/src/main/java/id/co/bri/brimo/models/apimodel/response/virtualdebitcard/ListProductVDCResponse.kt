package id.co.bri.brimo.models.apimodel.response.virtualdebitcard

import com.google.gson.annotations.Expose
import com.google.gson.annotations.SerializedName
import id.co.bri.brimo.models.AccountModel

data class ListProductVDCResponse(
    @field:Expose @field:SerializedName("account") val account: AccountModel,
    @field:Expose @field:SerializedName("list_virtual_card_data") val virtualCardList: ArrayList<VirtualCard>,
    @field:Expose @field:SerializedName("reference_number") val referenceNumber: String
)

data class VirtualCard(
    @field:Expose @field:SerializedName("id_card_type") val cardTypeId: String,
    @field:Expose @field:SerializedName("name") val name: String,
    @field:Expose @field:SerializedName("image_card") val imageCard: String,
    @field:Expose @field:SerializedName("image_card_vertical") val imageCardVertical: String,
    @field:Expose @field:SerializedName("promo_card") val promoCardList: List<PromoCard>,
    @field:Expose @field:SerializedName("specification_card") val specificationCardList: List<SpecificationCard>,
    @field:Expose @field:SerializedName("image_product_type") val imageProductType: String,
    @field:Expose @field:SerializedName("expired_date") val expiredDate: String,
    @field:Expose @field:SerializedName("product_brief") val productBrief: String
)

data class PromoCard(
    @field:Expose @field:SerializedName("title") val title: String,
    @field:Expose @field:SerializedName("subtitle") val subtitle: String,
    @field:Expose @field:SerializedName("icon_name") val iconName: String,
    @field:Expose @field:SerializedName("icon_path") val iconPath: String
)

data class SpecificationCard(
    @field:Expose @field:SerializedName("title") val title: String,
    @field:Expose @field:SerializedName("subtitle") val subtitle: String,
    @field:Expose @field:SerializedName("icon_name") val iconName: String,
    @field:Expose @field:SerializedName("icon_path") val iconPath: String
)