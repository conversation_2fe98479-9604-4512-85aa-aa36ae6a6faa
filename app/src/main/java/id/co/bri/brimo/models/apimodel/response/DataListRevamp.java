package id.co.bri.brimo.models.apimodel.response;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class DataListRevamp {

    @SerializedName("id")
    @Expose
    private String id;
    @SerializedName("title")
    @Expose
    private String title;
    @SerializedName("description")
    @Expose
    private String description;
    @SerializedName("detail")
    @Expose
    private String detail;
    @SerializedName("amount_string")
    @Expose
    private String amountString;

    @SerializedName("amount")
    @Expose
    private Integer amount;

    @SerializedName("admin_fee")
    @Expose
    private String adminFee;

    @SerializedName("strip_amount_string")
    @Expose
    private String stripAmountString;

    @SerializedName("strip_amount")
    @Expose
    private String stripAmount;

    @SerializedName("has_promo_banner")
    @Expose
    private Boolean hasPromoBanner;

    private boolean isSelected = false;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDetail() {
        return detail;
    }

    public void setDetail(String detail) {
        this.detail = detail;
    }

    public String getAmountString() {
        return amountString;
    }

    public void setAmountString(String amountString) {
        this.amountString = amountString;
    }

    public boolean isSelected() {
        return isSelected;
    }

    public void setSelected(boolean selected) {
        isSelected = selected;
    }

    public String getAdminFee() {
        return adminFee;
    }

    public void setAdminFee(String adminFee) {
        this.adminFee = adminFee;
    }

    public Integer getAmount() {
        return amount;
    }

    public void setAmount(Integer amount) {
        this.amount = amount;
    }

    public String getStripAmountString() {
        return stripAmountString;
    }

    public void setStripAmountString(String stripAmountString) {
        this.stripAmountString = stripAmountString;
    }

    public String getStripAmount() {
        return stripAmount;
    }

    public void setStripAmount(String stripAmount) {
        this.stripAmount = stripAmount;
    }

    public Boolean getHasPromoBanner() {
        return hasPromoBanner;
    }

    public void setHasPromoBanner(Boolean hasPromoBanner) {
        this.hasPromoBanner = hasPromoBanner;
    }
}
