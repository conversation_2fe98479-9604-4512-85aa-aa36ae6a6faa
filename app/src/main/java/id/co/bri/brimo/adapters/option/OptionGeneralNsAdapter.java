package id.co.bri.brimo.adapters.option;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import com.bumptech.glide.Glide;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ItemOptionNsBinding;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.optionmodel.OptionGeneralModel;

public class OptionGeneralNsAdapter extends RecyclerView.Adapter<OptionGeneralNsAdapter.ViewHolder> {

    private List<OptionGeneralModel> updateModels;
    private Context mContext;
    private OptionClickListener optionClickListener;
    private static final int ICON_NOTE_PADDING = 10;
    private static final int ICON_DEFAULT_PADDING = 0;


    public OptionGeneralNsAdapter(Context context, List<OptionGeneralModel> historyResponses, OptionClickListener clickListener) {
        this.mContext = context;
        this.updateModels = historyResponses;
        this.optionClickListener = clickListener;
    }

    @NonNull
    @Override
    public OptionGeneralNsAdapter.ViewHolder onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new ViewHolder(ItemOptionNsBinding.inflate(LayoutInflater.from(mContext),
                parent, false));
    }

    @Override
    public void onBindViewHolder(@NonNull OptionGeneralNsAdapter.ViewHolder holder, int position) {
        OptionGeneralModel optionUpdateModel = updateModels.get(position);

        int iconResource = optionUpdateModel.getDefaultIcon();
        String iconPath = optionUpdateModel.getOptionIconPath();
        String iconname = optionUpdateModel.getOptionIconName();
        String title = optionUpdateModel.getOptionName();

        holder.binding.ivOptionIcon.setVisibility(View.VISIBLE);

        //load image by glide
        if (!optionUpdateModel.getOptionIconName().isEmpty() || !optionUpdateModel.getOptionIconPath().isEmpty()) {
            GeneralHelper.loadIconTransaction(mContext, iconPath, iconname, holder.binding.ivOptionIcon, iconResource);
        } else if (iconResource > 0) {
            Glide.with(holder.binding.ivOptionIcon.getContext()).clear(holder.binding.ivOptionIcon);
            holder.binding.ivOptionIcon.setImageResource(iconResource);
        } else {
            holder.binding.ivOptionIcon.setVisibility(View.GONE);
            holder.binding.tvOptionName.setPadding(ICON_DEFAULT_PADDING, ICON_DEFAULT_PADDING, ICON_DEFAULT_PADDING, ICON_DEFAULT_PADDING);
        }

        int padding = (iconResource == R.drawable.note) ? ICON_NOTE_PADDING : ICON_DEFAULT_PADDING;
        holder.binding.ivOptionIcon.setPadding(padding, padding, padding, padding);

        holder.binding.llOptionView.setOnClickListener(view -> {
            if (optionClickListener != null) {
                optionClickListener.onOptionClick(position, optionUpdateModel);
            }
        });

        holder.binding.tvOptionName.setText(title);
    }

    public static class ViewHolder extends RecyclerView.ViewHolder {
        ItemOptionNsBinding binding;

        public ViewHolder(@NonNull ItemOptionNsBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @Override
    public int getItemCount() {
        return updateModels.size();
    }

    public interface OptionClickListener {
        void onOptionClick(int item, OptionGeneralModel model);
    }


}