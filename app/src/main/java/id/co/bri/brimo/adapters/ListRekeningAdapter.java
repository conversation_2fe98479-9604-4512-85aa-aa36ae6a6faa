package id.co.bri.brimo.adapters;

import android.content.Context;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Filter;
import android.widget.Filterable;

import androidx.annotation.NonNull;
import androidx.recyclerview.widget.RecyclerView;

import id.co.bri.brimo.R;
import id.co.bri.brimo.databinding.ListItemRekeningBinding;
import id.co.bri.brimo.domain.config.Constant;
import id.co.bri.brimo.domain.helpers.AnimateHelper;
import id.co.bri.brimo.domain.helpers.GeneralHelper;
import id.co.bri.brimo.models.AccountModel;
import id.co.bri.brimo.models.apimodel.response.ListRekeningResponse;
import id.co.bri.brimo.models.apimodel.response.SaldoReponse;

import java.util.ArrayList;
import java.util.List;

import io.rmiri.skeleton.master.AdapterSkeleton;

public class ListRekeningAdapter extends AdapterSkeleton<ListRekeningResponse.Account, ListRekeningAdapter.MyViewHolderRekening>
        implements
        AnimateHelper.AnimateHelperListener,
        Filterable {

    //param for animate
    private static int saldo = 0;
    private static int name = 1;
    private int lastPosition = -1;
    private int lastPositionName = -1;
    private AnimateHelper animateHelper;

    //param for data
    private List<AccountModel> itemAccounts;
    private List<AccountModel> listfilterd;


    String accoutString;
    String nama;
    String acountbiasa;
    String saldoDetail;
    String currency;
    Context mContext;
    ClickItem clickItem;

    public interface ClickItem {
        void onClickItem(AccountModel accountModel);
    }

    public ListRekeningAdapter(Context context, List<AccountModel> items, ClickItem clickItem) {
        this.setItems(items);
        this.mContext = context;
        listfilterd = items;
        this.clickItem = clickItem;
        this.animateHelper = new AnimateHelper(this);
    }

    @NonNull
    @Override
    public MyViewHolderRekening onCreateViewHolder(@NonNull ViewGroup parent, int viewType) {
        return new MyViewHolderRekening(ListItemRekeningBinding.inflate(LayoutInflater.from(parent.getContext()), parent, false));
    }


    @Override
    public void onBindViewHolder(@NonNull MyViewHolderRekening holder, int position) {
        final int pos = position;
        AccountModel account = getItems().get(position);
        SaldoReponse saldoReponse = getItems().get(position).getSaldoReponse();
        nama = listfilterd.get(position).getName();
        accoutString = listfilterd.get(position).getAcoountString();
        acountbiasa = listfilterd.get(position).getAcoount();


        holder.binding.tvDetailNoRekening.setText(account.getAcoountString());

        //cek tag alias
        if (account.getAlias() != null) {
            //jika tidak kosong tampilkan alias rekening
            if (account.getAlias().equalsIgnoreCase("")) {
                holder.binding.tvNamaPemilikRekening.setText(account.getName());
            } else
                holder.binding.tvNamaPemilikRekening.setText(account.getAlias());
        } else {
            holder.binding.tvNamaPemilikRekening.setText(account.getName());
        }

        if (account.getIsDefault() == 1)
            holder.binding.ivRekeningDefault.setVisibility(View.VISIBLE);
        else
            holder.binding.ivRekeningDefault.setVisibility(View.GONE);

        if (saldoReponse != null) {
            setAnimationSaldo(holder.binding.tvRekeningSaldo, position, saldo);
            setAnimationSaldo(holder.binding.tvNamaPemilikRekening, position, name);

            // cek saldo tertahan
            if (saldoReponse.isOnHold())
                holder.binding.ivAlertSaldo.setVisibility(View.VISIBLE);
            else holder.binding.ivAlertSaldo.setVisibility(View.GONE);

            if (saldoReponse.getBalanceString().equalsIgnoreCase("TO")) {
                holder.binding.pbSaldo.setVisibility(View.GONE);
                holder.binding.tvRekeningSaldo.setText("Gagal Memuat");
                holder.binding.llRekening.setEnabled(false);
            } else if (saldoReponse.getBalanceString().equalsIgnoreCase("12")) {
                holder.binding.pbSaldo.setVisibility(View.GONE);
                holder.binding.tvRekeningSaldo.setText("-");
                holder.binding.llRekening.setEnabled(false);
                holder.binding.tvNamaPemilikRekening.setText(saldoReponse.getName());
            } else if (saldoReponse.getBalanceString().equalsIgnoreCase("05")) {
                holder.binding.pbSaldo.setVisibility(View.GONE);
                holder.binding.tvRekeningSaldo.setText("-");
                holder.binding.llRekening.setEnabled(false);
                holder.binding.tvNamaPemilikRekening.setText(saldoReponse.getName());
            } else {
                saldoDetail = saldoReponse.getBalanceString();
                currency = saldoReponse.getCurrency();

                if (!saldoReponse.getSignedBalanceString().isEmpty()) {
                    holder.binding.tvRekeningSaldo.setText(saldoReponse.getSignedBalanceString());
                } else if (currency != null)
                    holder.binding.tvRekeningSaldo.setText(GeneralHelper.formatNominalIDR(saldoReponse.getCurrency(), saldoReponse.getBalanceString()));
                else
                    holder.binding.tvRekeningSaldo.setText(saldoReponse.getBalanceString());

                holder.binding.pbSaldo.setVisibility(View.GONE);
                holder.binding.llRekening.setOnClickListener(v -> clickItem.onClickItem(listfilterd.get(pos)));
            }
        } else {
            holder.binding.tvRekeningSaldo.setText("");
            holder.binding.pbSaldo.setVisibility(View.VISIBLE);
        }
        holder.binding.iconMore.setVisibility(View.GONE);

        if (account.getAccountType().equalsIgnoreCase("CC")) {
            holder.binding.ivJenisRekening.setVisibility(View.VISIBLE);
            if (account.getImageName().equalsIgnoreCase("cc_default_crop")) {
                holder.binding.ivRekening.setImageResource(R.drawable.cc_default_crop);
            } else {
                GeneralHelper.loadIconTransaction(
                        mContext,
                        account.getImagePath(),
                        account.getImageName(),
                        holder.binding.ivRekening,
                        R.drawable.cc_default_crop);
            }
        } else {
            holder.binding.ivRekening.setImageResource(R.drawable.kartu_brimo_no_logo);
            holder.binding.ivJenisRekening.setVisibility(View.GONE);
        }
        /*
        if (account.getCurrency().equalsIgnoreCase("IDR")) {
            if (position % 2 == 0)
                holder.Gambar.setImageResource(R.drawable.kartu_bri_biru);
            else
                holder.Gambar.setImageResource(R.drawable.kartu_bri_hitam);
        } else
            holder.Gambar.setImageResource(R.drawable.kartu_bri_hijau);
         */
    }

    @Override
    public int getItemCount() {
        return listfilterd.size();
    }

    public List<AccountModel> getItems() {
        return listfilterd;
    }

    public void setItems(List<AccountModel> items) {
        this.itemAccounts = items;
        this.listfilterd = items;
    }

    @Override
    public Filter getFilter() {
        return new Filter() {
            @Override
            protected FilterResults performFiltering(CharSequence charSequence) {
                if (charSequence.toString().isEmpty())
                    listfilterd = itemAccounts;
                else {
                    List<AccountModel> daftarSimpans = new ArrayList<>();
                    for (AccountModel a : itemAccounts) {
                        if (a.getCurrency().equalsIgnoreCase(charSequence.toString()))
                            daftarSimpans.add(a);
                    }
                    listfilterd = daftarSimpans;
                }
                FilterResults filterResults = new FilterResults();
                filterResults.values = listfilterd;
                return filterResults;
            }

            @Override
            protected void publishResults(CharSequence charSequence, FilterResults filterResults) {
                listfilterd = (List<AccountModel>) filterResults.values;
                double totalSaldo = 0.0;
                for (AccountModel account : listfilterd) {
                    if (account.getSaldoReponse() != null) {
                        totalSaldo = totalSaldo + account.getSaldoReponse().getBalance();
                    }
                }
                notifyDataSetChanged();
            }
        };
    }

    /**
     * Here is the key method to apply the animation
     */
    private void setAnimationSaldo(View viewToAnimate, int position, int type) {
        // If the bound view wasn't previously displayed on screen, it's animated
        if (type == saldo && position > lastPosition) {
            animateHelper.onAnimatorShow(viewToAnimate, true, Constant.REQUEST_SALDO);
            lastPosition = position;
        }

        // If the bound view wasn't previously displayed on screen, it's animated
        if (type == name && position > lastPositionName) {
            animateHelper.onAnimatorShow(viewToAnimate, true, Constant.REQUEST_SALDO);
            lastPositionName = position;
        }

    }


    public static class MyViewHolderRekening extends RecyclerView.ViewHolder {
        ListItemRekeningBinding binding;

        public MyViewHolderRekening(@NonNull ListItemRekeningBinding binding) {
            super(binding.getRoot());
            this.binding = binding;
        }
    }

    @Override
    public void onAnimatorShowEnd(String tagId) {
        // do nothing
    }

    @Override
    public void onAnimatorFadeEnd(String tagId) {
        // do nothing
    }
}
