package id.co.bri.brimo.adapters.onboardingnewskin

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import id.co.bri.brimo.R
import id.co.bri.brimo.databinding.ItemLanguagesListBinding
import id.co.bri.brimo.models.optionmodel.languages.OptionSelectLanguages
import id.co.bri.brimo.util.extension.setButtonColorState

class OTPChannelListAdapters(
    private var selectedChannel: String,
    private val onSelectionChanged: (String) -> Unit
) : ListAdapter<OptionSelectLanguages, OTPChannelListAdapters.ViewHolder>(LANGUAGE_DIFF_CALLBACK) {

    private var lastSelectedPosition: Int = RecyclerView.NO_POSITION
//    private var lastSelectedPosition: Int = if(selectedChannel == Constant.LANGUAGE_INDONESIA) 0 else 1

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ViewHolder {
        return ViewHolder(
            ItemLanguagesListBinding.inflate(
                LayoutInflater.from(parent.context), parent, false
            )
        )
    }

    override fun onBindViewHolder(holder: ViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    fun updateSelectedChannel(newChannel: String) {
        val previousPos = lastSelectedPosition
        selectedChannel = newChannel
        lastSelectedPosition = currentList.indexOfFirst { it.id == newChannel }

        if (previousPos != RecyclerView.NO_POSITION) {
            notifyItemChanged(previousPos)
        }
        if (lastSelectedPosition != RecyclerView.NO_POSITION) {
            notifyItemChanged(lastSelectedPosition)
        }
    }

    fun getSelectedChannel(): String = selectedChannel

    inner class ViewHolder(private val binding: ItemLanguagesListBinding) :
        RecyclerView.ViewHolder(binding.root) {
        fun bind(data: OptionSelectLanguages) {
            binding.apply {
                imgLanguage.setImageResource(data.imgLanguagePhoto)
                tvLanguageName.text = data.languageName

                rbSelectedLanguage.setOnCheckedChangeListener(null)
                rbSelectedLanguage.isChecked = selectedChannel == data.id
                rbSelectedLanguage.setButtonColorState(
                    root.context.getColor(R.color.black_ns_300),
                    root.context.getColor(R.color.ns_primary600)
                )

                val listener = {
                    if (selectedChannel != data.id) {
                        updateSelectedChannel(data.id)
                        onSelectionChanged.invoke(data.id)
                    }
                }

                imgLanguage.setOnClickListener { listener() }
                rbSelectedLanguage.setOnClickListener { listener() }
                tvLanguageName.setOnClickListener { listener() }
            }
        }
    }

    companion object {
        val LANGUAGE_DIFF_CALLBACK = object : DiffUtil.ItemCallback<OptionSelectLanguages>() {
            override fun areItemsTheSame(
                oldItem: OptionSelectLanguages,
                newItem: OptionSelectLanguages
            ): Boolean = oldItem == newItem

            override fun areContentsTheSame(
                oldItem: OptionSelectLanguages,
                newItem: OptionSelectLanguages
            ): Boolean = oldItem.id == newItem.id
        }
    }
}