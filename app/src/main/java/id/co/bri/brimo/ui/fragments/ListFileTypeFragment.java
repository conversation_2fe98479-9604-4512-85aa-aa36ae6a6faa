package id.co.bri.brimo.ui.fragments;

import android.content.Context;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.DialogFragment;
import androidx.recyclerview.widget.LinearLayoutManager;

import com.google.android.material.bottomsheet.BottomSheetDialogFragment;

import java.util.List;

import id.co.bri.brimo.R;
import id.co.bri.brimo.adapters.FileTypeAdapter;
import id.co.bri.brimo.databinding.FragmentListFileTypeBinding;
import id.co.bri.brimo.models.TransactionTypeModel;

public class ListFileTypeFragment extends BottomSheetDialogFragment implements FileTypeAdapter.ClickItemListener {

    private FragmentListFileTypeBinding binding;
    private Context context;
    private FileTypeAdapter adapter;
    private List<TransactionTypeModel> fileTypeList;
    private SelectFileTypeInterface fileTypeInterface;
    private String selectedType;

    public ListFileTypeFragment(Context mContext, List<TransactionTypeModel> fileType, SelectFileTypeInterface typeInterface, String mSelectedType) {
        this.context = mContext;
        this.fileTypeList = fileType;
        this.fileTypeInterface = typeInterface;
        this.selectedType = mSelectedType;
    }

    @Override
    public void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setStyle(DialogFragment.STYLE_NORMAL, R.style.CustomBottomSheetDialogTheme);
    }

    @Override
    public View onCreateView(LayoutInflater inflater, ViewGroup container,
                             Bundle savedInstanceState) {
        binding = FragmentListFileTypeBinding.inflate(inflater, container, false);
        return binding.getRoot();
    }

    @Override
    public void onViewCreated(@NonNull View view, @Nullable Bundle savedInstanceState) {
        super.onViewCreated(view, savedInstanceState);

        binding.rvListFileType.setHasFixedSize(true);
        binding.rvListFileType.setLayoutManager(new LinearLayoutManager(getActivity()));
        adapter = new FileTypeAdapter(fileTypeList, getActivity(), this, selectedType);
        adapter.setList(fileTypeList);
        binding.rvListFileType.setAdapter(adapter);
    }

    @Override
    public void onClickItem(TransactionTypeModel fileTypeModel) {
        dismiss();
        fileTypeInterface.onSelectType(fileTypeModel);
    }

    public interface SelectFileTypeInterface {
        void onSelectType(TransactionTypeModel model);
    }
}