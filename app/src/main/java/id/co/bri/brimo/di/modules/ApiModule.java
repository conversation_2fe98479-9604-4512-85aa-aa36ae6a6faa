package id.co.bri.brimo.di.modules;

import android.content.Context;

import com.google.gson.Gson;

import javax.inject.Singleton;

import dagger.Module;
import dagger.Provides;
import id.co.bri.brimo.BuildConfig;
import id.co.bri.brimo.data.api.ApiClient;
import id.co.bri.brimo.data.api.ApiHelper;
import id.co.bri.brimo.data.api.ApiInterface;
import id.co.bri.brimo.data.api.ApiSource;
import id.co.bri.brimo.data.preference.BRImoPrefSource;
import id.co.bri.brimo.domain.config.AppConfig;

import javax.inject.Singleton;

import dagger.Module;
import dagger.Provides;
import id.co.bri.brimo.util.singalarity.helper.IC2KeyHelper;
import id.co.bri.brimo.util.BodyDecryptor;
import okhttp3.OkHttpClient;
import okhttp3.logging.HttpLoggingInterceptor;
import retrofit2.Retrofit;
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory;
import retrofit2.converter.gson.GsonConverterFactory;

@Module
public class ApiModule {

    Context context;

    public ApiModule(Context context) {
        this.context = context;
    }

    @Singleton
    @Provides
    public OkHttpClient provideClient(BodyDecryptor bodyDecryptor) {
        HttpLoggingInterceptor interceptor = new HttpLoggingInterceptor();

        if (BuildConfig.DEBUG) {
            interceptor.setLevel(HttpLoggingInterceptor.Level.BODY);
        } else {
            interceptor.setLevel(HttpLoggingInterceptor.Level.NONE);
        }

        OkHttpClient okHttpClient = null;
        if (BuildConfig.DEBUG){
            okHttpClient = ApiClient.getOkHttpClientWithSSLPinner(
                    interceptor,
                    context,
                    bodyDecryptor
            );
        } else {
            okHttpClient = ApiClient.getOkHttpClientTrustSSLPinner(
                    interceptor,
                    context,
                    bodyDecryptor
            );
        }


        return okHttpClient;


    }

    @Provides
    public Retrofit provideRetrofit(OkHttpClient client) {
        return new Retrofit.Builder()
                .baseUrl(AppConfig.getBaseUrl())
                .addConverterFactory(GsonConverterFactory.create())
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .client(client)
                .build();

    }

    @Provides
    public ApiInterface provideApiClient(BodyDecryptor bodyDecryptor) {
        return provideRetrofit(provideClient(bodyDecryptor)).create(ApiInterface.class);
    }


    @Singleton
    @Provides
    public ApiSource provideApiSource(BRImoPrefSource brImoPrefSource, ApiInterface apiInterface, Gson gson, IC2KeyHelper dynamicKeyHelper) {
        return new ApiHelper(brImoPrefSource, apiInterface, gson, dynamicKeyHelper);
    }

    @Singleton
    @Provides
    public BodyDecryptor provideChuckerDecryptor() {
        return new BodyDecryptor();
    }
}