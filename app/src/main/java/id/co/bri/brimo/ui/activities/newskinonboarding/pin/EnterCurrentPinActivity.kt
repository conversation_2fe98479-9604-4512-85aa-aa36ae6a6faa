package id.co.bri.brimo.ui.activities.newskinonboarding.pin

import android.annotation.SuppressLint
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import id.co.bri.brimo.databinding.ActivityEnterPinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.pass.EnterPassActivity
import id.co.bri.brimo.ui.fragments.pin.PinEntryFragment
import id.co.bri.brimo.ui.fragments.pin.PinEntryListener

class EnterCurrentPinActivity : NewSkinBaseActivity(), PinEntryListener {

    private lateinit var binding: ActivityEnterPinBinding

    private val correctPin = "121212"

    private val launcher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_OK) {
            setResult(RESULT_OK)
            finish()
        }
    }

    @SuppressLint("CommitTransaction")
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityEnterPinBinding.inflate(layoutInflater)
        setContentView(binding.root)

        val pinFragment = PinEntryFragment().apply {
            headerTitle = "Ubah PIN"
            descriptionText = "Masukkan PIN kamu saat ini"
            infoText = ""
            isForgotPinVisible = true
            setPinEntryListener(this@EnterCurrentPinActivity)
        }

        supportFragmentManager.beginTransaction()
            .replace(binding.fragmentContainerPin.id, pinFragment)
            .commit()
    }

    override fun onPinComplete(pin: String) {
        if (pin == correctPin) {
            val intent = Intent(this, EnterCreatePinActivity::class.java)
            launcher.launch(intent)
        } else {
            val fragment = supportFragmentManager.findFragmentById(binding.fragmentContainerPin.id)
            if (fragment is PinEntryFragment) {
                fragment.setErrorText("PIN salah")
            }
        }
    }

    override fun onPinError(errorMessage: String) {
    }

    override fun onForgotPinClicked() {
        EnterPassActivity.launchIntent(this)
    }

    companion object {
        fun createLaunchIntent(context: Context): Intent {
            return Intent(context, EnterCurrentPinActivity::class.java)
        }
    }
}
