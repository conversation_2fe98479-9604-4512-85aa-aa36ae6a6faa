package id.co.bri.brimo.models.apimodel.request;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

public class InquiryPlnRequest {

    @SerializedName("pln_type_code")
    @Expose
    private String billingType;
    @SerializedName("payment_number")
    @Expose
    private String billingNumber;

    public InquiryPlnRequest( String billingType,String billingNumber) {
        this.billingNumber = billingNumber;
        this.billingType = billingType;
    }

    public String getPlnTypeCode() {
        return billingType;
    }

    public void setPlnTypeCode(String plnTypeCode) {
        this.billingType = plnTypeCode;
    }

    public String getPaymentNumber() {
        return billingNumber;
    }

    public void setPaymentNumber(String paymentNumber) {
        this.billingNumber = paymentNumber;
    }

}
