package id.co.bri.brimo.ui.activities.newskinonboarding

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Intent
import android.content.IntentFilter
import android.os.Build
import android.os.Bundle
import android.os.CountDownTimer
import android.os.Handler
import android.os.Looper
import android.util.Log
import android.view.KeyEvent
import android.view.View
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.text.HtmlCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.GridLayoutManager
import com.google.android.gms.auth.api.phone.SmsRetriever
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.PinNumberAdapterNewSkin
import id.co.bri.brimo.adapters.baseadapter.base.BasePinAdapter
import id.co.bri.brimo.adapters.onboardingnewskin.OtpNumberAdapter
import id.co.bri.brimo.adapters.pinadapter.OtpRevampAdapter
import id.co.bri.brimo.contract.IPresenter.onboardingrevamp.IOnboardingOtpPresenter
import id.co.bri.brimo.contract.IView.onboardingrevamp.IOnboardingOtpView
import id.co.bri.brimo.databinding.ActivityOnboardingOtpNewskinBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.extension.boldSpecificText
import id.co.bri.brimo.domain.extension.recolorSpecificText
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.calendar.getColorCompat
import id.co.bri.brimo.models.apimodel.request.onboardingrevamp.OnboardingResendReq
import id.co.bri.brimo.models.apimodel.response.OtpPhoneResponse
import id.co.bri.brimo.models.apimodel.response.SendPhoneResponse
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.OnboardingOtpRes
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.base.NewSkinBaseActivity
import id.co.bri.brimo.ui.activities.onboardingrevamp.OnboardingVerifyEmailActivity
import id.co.bri.brimo.ui.customviews.pin.InsertPinNumbers
import id.co.bri.brimo.ui.fragments.bottomsheet.NewSkinOTPChannel
import id.co.bri.brimo.ui.fragments.registrasi.BottomDialogKirimUlangFragment
import id.co.bri.brimo.ui.fragments.registrasi.BottomDialogKirimUlangNewSkinFragment
import id.co.bri.brimo.util.broadcastreceiver.smsretriever.SmsRetrieverReceiver
import id.co.bri.brimo.util.getParcelableCompat
import id.co.bri.brimo.util.getSavedLanguage
import javax.inject.Inject

class OnboardingOtpNewSkinActivity : NewSkinBaseActivity(),
    IOnboardingOtpView,
    View.OnKeyListener,
    BasePinAdapter.PinAdapterListener,
    OtpNumberAdapter.OnPinNumberListener,
    BottomDialogKirimUlangNewSkinFragment.OnBackPressUlangi {

    private val binding by lazy(LazyThreadSafetyMode.NONE) {  ActivityOnboardingOtpNewskinBinding.inflate(layoutInflater) }

    @Inject
    lateinit var presenter: IOnboardingOtpPresenter<IOnboardingOtpView>

    private var onboardingOtpResponse: OnboardingOtpRes? = null
    private var countDownTimer: CountDownTimer? = null
    private val second = 1000
    private var expiredSecond = 0

    private var methodCheck: String? = null

    private var onboardingId: String = ""

    private lateinit var otpRevampAdapter: OtpRevampAdapter
    private lateinit var pinNumberAdapter: OtpNumberAdapter
    private lateinit var pinOtpLayoutManager: GridLayoutManager
    private lateinit var pinPadLayoutManager: GridLayoutManager
    private var isPinChange: Boolean = false
    private var sendBy: String = ""

    private val smsRetrieverReceiver = SmsRetrieverReceiver()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(binding.root)

        onBackPressedDispatcher.addCallback(this, onBackPressedCallback)
        injectDependency()
        intentExtra()
        setupView()
        setEventAppsFlyerFirstInit()
        runSmsRetriever()
        setOtpSmsRetriever()
    }

    private val data: OtpPhoneResponse? by lazy {
        intent?.extras?.getParcelableCompat(RESP_DATA_EXTRA)
    }

    private val sourceStatus by lazy {
        intent?.extras?.getString(Constant.REQ_INFO_PAGE_SOURCE).orEmpty()
    }

    companion object {
        private const val TAG = "OnboardingOtpActivity"
        const val RESP_DATA_EXTRA = "resp_data_extra"

        fun launchIntent(caller: Activity, isChangePin: Boolean, method: String) {
            val intent = Intent(caller, OnboardingOtpNewSkinActivity::class.java)
            intent.putExtra("isPinChange", isChangePin)
            intent.putExtra("method", method)
            caller.startActivityForResult(intent, Constant.REQ_UBAH_PIN)
        }

        fun launchIntent(caller: Activity, method: String, data: SendPhoneResponse? = null) {
            val intent = Intent(caller, OnboardingOtpNewSkinActivity::class.java)
            intent.putExtra("method", method)
            intent.putExtra(RESP_DATA_EXTRA, data)
            caller.startActivityForResult(intent, Constant.REQ_UBAH_PIN)
        }
    }

    private fun setEventAppsFlyerFirstInit() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("openaccount_otp_phonenumber", eventValue)
    }

    private fun intentExtra() {
        onboardingOtpResponse = Gson().fromJson(intent.getStringExtra(Constant.GENRES), OnboardingOtpRes::class.java)
        isPinChange = intent.getBooleanExtra("isPinChange", false)
        sendBy = intent.getStringExtra("method").toString()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.start()
        presenter.setUrlResend(GeneralHelper.getString(R.string.url_onboarding_resend_sms_v3))
        presenter.setUrlSend(GeneralHelper.getString(R.string.url_onboarding_validate_sms_v3))
        onboardingId = presenter.getDeviceId()
    }

    private fun setupView() {
        GeneralHelperNewSkin.setToolbar(
            this@OnboardingOtpNewSkinActivity,
            binding.toolbar.toolbar,
            GeneralHelper.getString(R.string.verifikasi_nomor_hp)
        )
        val originalText = "Masukkan kode verifikasi OTP yang dikirim ke nomor +6281345602***"
        val styledText = originalText
            .recolorSpecificText("+6281345602***", getColorCompat(R.color.black_ns_main))
            .boldSpecificText(data?.phone.orEmpty())

        binding.tvDescOtp.text = styledText

        otpRevampAdapter = OtpRevampAdapter(this@OnboardingOtpNewSkinActivity, 3)
        otpRevampAdapter.onCompleteOtpListener = { otp ->
            Handler(Looper.getMainLooper()).postDelayed({
                OnboardingVerifyEKYCActivity.launchIntent(this)
            }, 500)
        }


        pinNumberAdapter = OtpNumberAdapter(InsertPinNumbers.getPinNumberList(this@OnboardingOtpNewSkinActivity))
        pinOtpLayoutManager = GridLayoutManager(this@OnboardingOtpNewSkinActivity, 6)
        pinPadLayoutManager = GridLayoutManager(this@OnboardingOtpNewSkinActivity, 3)

        pinNumberAdapter.onPinNumberListener = this
        otpRevampAdapter.setListener(this@OnboardingOtpNewSkinActivity)
        binding.rvBox.layoutManager = pinOtpLayoutManager
        binding.rvBox.adapter = otpRevampAdapter
        binding.rvInputOtp.layoutManager = pinPadLayoutManager
        binding.rvInputOtp.adapter = pinNumberAdapter

        setTextTimer(onboardingOtpResponse?.expiredIinSecond ?: 3)

        binding.tvResendOtp.setOnClickListener {
            NewSkinOTPChannel(
                this@OnboardingOtpNewSkinActivity,
                supportFragmentManager,
                getSavedLanguage(this@OnboardingOtpNewSkinActivity),
            ){
                otpRevampAdapter.deleteAllInsertedPin()
            }
        }
    }

    private fun runSmsRetriever() {
        val client = SmsRetriever.getClient(this).startSmsRetriever()
        client.addOnSuccessListener {
            if (!GeneralHelper.isProd()) Log.e(TAG, "Successfully running sms retriever")
        }

        client.addOnFailureListener {
            if (!GeneralHelper.isProd()) Log.e(TAG, "Failed running sms retriever")
        }
    }

    private fun setOtpSmsRetriever() {
        smsRetrieverReceiver.onOtpReceived = { result ->
            when (result) {
                is SmsRetrieverReceiver.OTPResult.OTPReceived -> otpRevampAdapter?.setOtp(result.otp)
                is SmsRetrieverReceiver.OTPResult.OTPNotReceived -> Log.e(TAG, "collectOtpResult: ${result.error}")
            }
        }
    }

    private fun setTextTimer(timer: Int) {
        binding.tvTimer.isVisible = true
        val countDown: Int = second * timer
        countDownTimer?.cancel() // Cancel any existing count down
        countDownTimer = object : CountDownTimer(countDown.toLong(), second.toLong()) {
            override fun onTick(millisUntilFinished: Long) {
                val seconds: Int = millisUntilFinished.toInt() / second
                expiredSecond = seconds
                val timeFormat = GeneralHelper.getTimeFormat(seconds)
                binding.tvTimer.text = String.format(
                    resources.getString(R.string.countdown_bracket_otp00_00),
                    timeFormat[1], timeFormat[2]
                )

                binding.tvResendOtp.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.neutral_light60
                    )
                )
                binding.tvResendOtp.isEnabled = false
            }

            override fun onFinish() {
                expiredSecond = 0
                binding.tvTimer.isVisible = false
                binding.tvResendOtp.setTextColor(
                    ContextCompat.getColor(
                        applicationContext,
                        R.color.ns_primary600
                    )
                )
                binding.tvResendOtp.isEnabled = true
            }
        }.start()
    }

    private fun checkWaOrSms(method: String) {
        val descStringRes = when (method) {
            Constant.WHATSAPP -> R.string.desc_regis_otp_wa
            else -> R.string.desc_regis_otp_sms
        }

//        binding.tvDescOtp.text = HtmlCompat.fromHtml(
//            String.format(
//                GeneralHelper.getString(descStringRes),
//                onboardingOtpResponse?.phone
//            ), HtmlCompat.FROM_HTML_MODE_LEGACY
//        )

        runSmsRetriever()
    }

    override fun onSuccessResend(onboardingOtpResponse: OnboardingOtpRes) {
        if (countDownTimer != null) countDownTimer?.cancel()
        this.onboardingOtpResponse = onboardingOtpResponse
        checkWaOrSms(onboardingOtpResponse.method.orEmpty())
        setTextTimer(onboardingOtpResponse.expiredIinSecond)

    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        when (requestCode) {
            Constant.REQ_UBAH_KATA_KUNCI -> {
                if (resultCode == Activity.RESULT_OK) {
                    setResult(Activity.RESULT_OK)
                    finish()
                }
            }
            Constant.REQ_UBAH_PIN -> {
                if (resultCode == Activity.RESULT_OK) {
                    setResult(Activity.RESULT_OK)
                    finish()
                }
            }
        }
    }

    override fun onSuccessSend(stringResponse: String) {
        setEventAppsFlyerSuccessSendOTP()
        val intent = Intent(this, OnboardingVerifyEmailActivity::class.java)
        intent.putExtra(Constant.GENRES, stringResponse)
        startActivityIntent.launch(intent)
    }

    private fun setEventAppsFlyerSuccessSendOTP() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("openaccount_otp_phonenumber_success", eventValue)
    }

    override fun deletePin() {
        otpRevampAdapter.deleteAllInsertedPin()
    }

    override fun onKey(v: View?, keyCode: Int, event: KeyEvent?): Boolean {
        return false
    }

    override fun onPinClicked(pinNumber: Int) {
        otpRevampAdapter.addInsertedPin(pinNumber.toString())
    }

    override fun onDeleteClicked() {
        otpRevampAdapter.deleteInsertedPin()
    }

    @SuppressLint("NotifyDataSetChanged")
    override fun notifyChanges() {
        otpRevampAdapter.notifyDataSetChanged()
    }

    override fun onComplete(string: String) {
        // do nothing
    }

    override fun itemResending(method: String) {
        methodCheck = method
        presenter.sendResendOtp(OnboardingResendReq(onboardingId, method))
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == RESULT_CANCELED && result.data != null) {
            setResult(RESULT_CANCELED, result.data)
            finish()
        }
    }

    private val onBackPressedCallback: OnBackPressedCallback =
        object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {
                val intent = Intent()
                intent.putExtra(Constant.CHECK_POINT, 3)
                setResult(RESULT_CANCELED, intent)
                finish()
            }
        }

    override fun onStart() {
        super.onStart()

        val intentFilter = IntentFilter(SmsRetriever.SMS_RETRIEVED_ACTION)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            registerReceiver(smsRetrieverReceiver, intentFilter, RECEIVER_EXPORTED)
        } else {
            @Suppress("UnspecifiedRegisterReceiverFlag")
            registerReceiver(smsRetrieverReceiver, intentFilter)
        }
    }

    override fun onStop() {
        super.onStop()
        unregisterReceiver(smsRetrieverReceiver)
    }

    override fun onDestroy() {
        presenter.stop()
        countDownTimer?.cancel()
        super.onDestroy()
    }
}