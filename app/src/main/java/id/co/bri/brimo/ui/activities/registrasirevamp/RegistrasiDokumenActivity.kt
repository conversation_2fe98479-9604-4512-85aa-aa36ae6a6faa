package id.co.bri.brimo.ui.activities.registrasirevamp

import android.Manifest
import android.app.Activity
import android.content.ActivityNotFoundException
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import androidx.activity.result.contract.ActivityResultContracts
import androidx.annotation.RequiresApi
import androidx.core.app.ActivityCompat
import com.google.gson.Gson
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IPresenter.registrasirevamp.IRegistrasiDokumenPresenter
import id.co.bri.brimo.contract.IView.registrasirevamp.IRegistrasiDokumenView
import id.co.bri.brimo.databinding.ActivityRegistrasiDokumenBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.models.NotifikasiModel
import id.co.bri.brimo.models.apimodel.response.onboardingrevamp.ForceUpdateResponse
import id.co.bri.brimo.models.apimodel.response.registrasi.RegisProgressResponse
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment
import java.util.HashMap
import javax.inject.Inject

class RegistrasiDokumenActivity : BaseActivity(), IRegistrasiDokumenView {

    @Inject
    lateinit var presenter: IRegistrasiDokumenPresenter<IRegistrasiDokumenView>

    private lateinit var binding: ActivityRegistrasiDokumenBinding

    private val permissionsRegis = arrayOf(
        Manifest.permission.WRITE_EXTERNAL_STORAGE,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO
    )

    @RequiresApi(Build.VERSION_CODES.TIRAMISU)
    private val permissionsRegis33 = arrayOf(
        Manifest.permission.READ_MEDIA_IMAGES,
        Manifest.permission.READ_MEDIA_AUDIO,
        Manifest.permission.READ_MEDIA_VIDEO,
        Manifest.permission.CAMERA,
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.POST_NOTIFICATIONS
    )

    private fun permissionsCheck(): Array<String> {
        val permission: Array<String> = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            permissionsRegis33
        } else {
            permissionsRegis
        }
        return permission
    }

    private var permissionsAccess = permissionsCheck()

    private val permissionsAll = 1

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityRegistrasiDokumenBinding.inflate(layoutInflater)

        setContentView(binding.root)

        injectDependency()
        intentExtra()
        checkPermission()
        setupView()
    }

    private fun intentExtra() {
        if (intent.hasExtra(Constant.TAG_NOTIF))
            parseDataNotifForeground(intent)
    }

    private fun checkPermission() {
        if (!hasPermissions(this, *permissionsAccess)) {
            ActivityCompat.requestPermissions(this, permissionsAccess, permissionsAll)
        }
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        presenter.view = this
        presenter.setUrlProgress(GeneralHelper.getString(R.string.url_registration_check_progress))
        presenter.start()
    }

    private fun setupView() {
        GeneralHelper.setToolbarRevamp(
            this,
            binding.toolbarRevamp.toolbar,
            GeneralHelper.getString(R.string.pendaftaran_brimo)
        )

        binding.btnSubmit.setOnClickListener { presenter.getProgressRegis() }
    }

    override fun onSuccessGetProgress() {
        setEventAppsFlyerClickedButton()
        val intent = Intent(this, RegistrasiCameraActivity::class.java)
        startActivityIntent.launch(intent)
    }

    private fun setEventAppsFlyerClickedButton() {
        val eventValue: MutableMap<String, Any> = HashMap()
        eventValue[Constant.CUSTOMER_ID] = presenter.persistenceId
        trackAppsFlyerAnalyticEvent("start_regis_brimo", eventValue)
    }

    override fun onSuccessCheckPoint(
        responseString: String,
        regisProgressResponse: RegisProgressResponse
    ) {
        val intent = Intent(this, RegistrasiCheckPointActivity::class.java)
        intent.putExtra(Constant.CHECK_POINT, regisProgressResponse.status)
        intent.putExtra(Constant.GENRES, responseString)
        startActivityIntent.launch(intent)
    }

    override fun onUpdateVersion(forceUpdate: ForceUpdateResponse) {
        OpenBottomSheetGeneralFragment.showDialogInformation(
            supportFragmentManager,
            "",
            "ic_forced_update",
            forceUpdate.title,
            forceUpdate.description,
            { openPlaystore() },
            false,
            forceUpdate.button
        )
    }

    private fun openPlaystore() {
        try {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("market://details?id=${Constant.APP_PACKAGE_NAME}")
                )
            )
        } catch (e: ActivityNotFoundException) {
            startActivity(
                Intent(
                    Intent.ACTION_VIEW,
                    Uri.parse("https://play.google.com/store/apps/details?id=${Constant.APP_PACKAGE_NAME}")
                )
            )
        }
    }

    override fun parseDataNotifForeground(intent: Intent?) {
        if (intent != null) {
            try {
                // Get data from notifikasi
                val extras = intent.extras
                if (extras != null) {
                    try {
                        val notifikasiString = extras.getString(Constant.TAG_NOTIF)
                        val notifikasiModel =
                            Gson().fromJson(notifikasiString, NotifikasiModel::class.java)
                        if (notifikasiModel != null) {
                            if (notifikasiModel.type == Constant.REGISTRATION_BRIMO) {
                                presenter.getProgressRegis()
                            }
                        }
                    } catch (e: Exception) {
                        if (!GeneralHelper.isProd()) Log.e(
                            "TestNotif", "parseDataNotif: ", e
                        )
                    }
                }
            } catch (e: Exception) {
                if (!GeneralHelper.isProd()) Log.e(
                    "TestNotif", "parseDataNotif: ", e
                )
            }
        }
    }

    private var startActivityIntent: ActivityResultLauncher<Intent> = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            // There are no request codes
            // val data: Intent? = result.data
        } else {
            if (result.data != null) {
                setResult(RESULT_CANCELED, result.data)
                finish()
            }
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<String?>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        var grantAll = true
        if (grantResults.isNotEmpty()) {
            for (i in grantResults.indices) {
                if (grantResults[i] != PackageManager.PERMISSION_GRANTED) {
                    grantAll = false
                    break
                }
            }
        }
        if (!grantAll) {
            showAlertFinish(getString(R.string.notes_need_permission))
        } else {
            checkPermission()
        }
    }

    override fun onDestroy() {
        presenter.stop()
        super.onDestroy()
    }
}