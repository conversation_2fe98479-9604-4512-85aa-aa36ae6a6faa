package id.co.bri.brimo.ui.activities.base

import android.app.Activity
import android.app.AlertDialog
import android.content.DialogInterface
import android.content.Intent
import android.net.Uri
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.provider.Settings
import android.view.View
import android.view.WindowManager
import androidx.activity.OnBackPressedCallback
import androidx.appcompat.widget.AppCompatButton
import id.co.bri.brimo.R
import id.co.bri.brimo.contract.IView.base.INewSkinBaseView
import id.co.bri.brimo.domain.SnackBarType
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.util.SecurityDetector

open class NewSkinBaseActivity: BaseActivity() {
    open fun isScreenshotDisabled(): Boolean = false

    companion object {
        const val KEY_SHOULD_SHOW_DIALOG = "should_show_dialog"
        const val KEY_SHOULD_SHOW_FAILURE_DIALOG = "should_show_failure_dialog"
        const val KEY_ERROR_RESPONSE = "error_response"
        const val KEY_ERROR_TYPE = "error_type"
        const val KEY_BLOCK_BACK_PRESS = "block_back_press"
    }

    private var isSecurityWarningVisible = false
    private lateinit var screenshotDetector: SecurityDetector
    private var securityRootView: View? = null

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (isScreenshotDisabled()) {
            window.setFlags(
                WindowManager.LayoutParams.FLAG_SECURE,
                WindowManager.LayoutParams.FLAG_SECURE
            )
        }

        val blockBackPress = intent?.getBooleanExtra(KEY_BLOCK_BACK_PRESS, false) == true
        if (blockBackPress) setBlockBackNavigation()
    }

    open fun setupSecurityDetection(rootView: View) {
        this.securityRootView = rootView
        screenshotDetector = SecurityDetector(
            applicationContext,
            onScreenshotTaken = { showSecurityWarning() },
            onScreenRecordingDetected = { showSecurityWarning() }
        )
    }

    private fun showSecurityWarning() {
        if (!isSecurityWarningVisible && securityRootView != null) {
            isSecurityWarningVisible = true
            runOnUiThread {
                GeneralHelperNewSkin.showCustomSnackBar(
                    securityRootView!!,
                    "Aksi ini dibatasi demi keamanan pengguna kami.",
                    SnackBarType.ERROR
                )
                Handler(Looper.getMainLooper()).postDelayed({
                    isSecurityWarningVisible = false
                }, 2000)
            }
        }
    }

    override fun onResume() {
        super.onResume()
        if (::screenshotDetector.isInitialized) {
            screenshotDetector.start()
        }
    }

    override fun onPause() {
        super.onPause()
        if (::screenshotDetector.isInitialized) {
            screenshotDetector.stop()
        }
    }

    open fun setBlockBackNavigation() {
        onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
            override fun handleOnBackPressed() {

            }
        })
    }

    override fun onException(message: String) {
        GeneralHelperNewSkin.triggerVibration(this, Constant.VIBRATE_ERROR)
        if (GeneralHelperNewSkin.isContains(Constant.LIST_TYPE_GAGAL_GANGGUAN_SISTEM, message)) {
            GeneralHelperNewSkin.showErrorBottomDialog(this, message)
        } else {
            GeneralHelperNewSkin.showGeneralErrorBottomDialog(this)
        }
    }

    fun updateButtonState(isEnabled: Boolean, button: AppCompatButton) {
        button.isEnabled = isEnabled
    }

    fun showSnackbar(message: String, type: Int) {
        GeneralHelperNewSkin.showSnackBar(findViewById(android.R.id.content),
            message = message, messageType = type
        )
    }

    override fun showSnackbarErrorMessage(
        message: String?,
        messageType: Int,
        activity: Activity?,
        isFragment: Boolean
    ) {
        try {
            // Try to find R.id.content first, if not found, use the root view
            val parentView = findViewById<View>(R.id.content) ?: window.decorView.rootView

            // Use the consolidated snackbar function
            GeneralHelperNewSkin.showSnackBar(parentView, message, messageType)
        } catch (e: Exception) {
            //do nothing
        }
    }

    override fun showProgress() {
        GeneralHelperNewSkin.showLoadingDialog(this)
    }

    override fun hideProgress() {
        GeneralHelperNewSkin.dismissLoadingDialog()
    }

    override fun showAlertPermission(msg: String?) {
        val builder = AlertDialog.Builder(this)
        builder.setMessage(msg)
            .setPositiveButton(GeneralHelper.getString(R.string.ok)) { _, _ ->
                val intent =
                    Intent(Settings.ACTION_APPLICATION_DETAILS_SETTINGS)
                val uri = Uri.fromParts("package", packageName, null)
                intent.setData(uri)
                startActivity(intent)
            }
            .setNegativeButton(
                GeneralHelper.getString(R.string.batal2)
            ) { dialog: DialogInterface, _ ->
                dialog.dismiss()
            }
            .setCancelable(false)
            .show()
    }
}