package id.co.bri.brimo.ui.fragments.profilerevamp

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.graphics.Color
import android.location.LocationManager
import android.os.Build
import android.os.Bundle
import android.provider.Settings
import android.util.Log
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ScrollView
import androidx.activity.result.contract.ActivityResultContracts
import androidx.appcompat.app.AppCompatActivity
import androidx.core.app.ActivityCompat
import androidx.core.view.isVisible
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.swiperefreshlayout.widget.SwipeRefreshLayout.OnRefreshListener
import com.ethanhua.skeleton.Skeleton
import com.ethanhua.skeleton.SkeletonScreen
import id.co.bri.brimo.R
import id.co.bri.brimo.adapters.profilerevamp.ListProfileInfoAdapter
import id.co.bri.brimo.contract.IPresenter.dashboard.IProfilePresenter
import id.co.bri.brimo.contract.IView.dashboard.IProfileView
import id.co.bri.brimo.data.preference.BRImoPrefRepository
import id.co.bri.brimo.databinding.FragmentProfileRevampBinding
import id.co.bri.brimo.domain.config.Constant
import id.co.bri.brimo.domain.config.MenuConfig
import id.co.bri.brimo.domain.helpers.BiometricUtils.Companion.hasBiometricEnrolled
import id.co.bri.brimo.domain.helpers.GeneralHelper
import id.co.bri.brimo.domain.helpers.GeneralHelperNewSkin
import id.co.bri.brimo.domain.helpers.GpsTracker
import id.co.bri.brimo.domain.helpers.biometric.BiometricCallback
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility.displayPromptForEnroll
import id.co.bri.brimo.domain.helpers.biometric.BiometricUtility.setBiometricType
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCase
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseBuilder
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseListener
import id.co.bri.brimo.domain.helpers.bubbleShowCaseView.BubbleShowCaseSequence
import id.co.bri.brimo.models.apimodel.request.RevokeSessionRequest
import id.co.bri.brimo.models.apimodel.response.BripoinResponse
import id.co.bri.brimo.models.apimodel.response.BripointDetailAccountResponse
import id.co.bri.brimo.models.apimodel.response.ChatBankingResponse
import id.co.bri.brimo.models.apimodel.response.ProfileResponse
import id.co.bri.brimo.models.apimodel.response.biometric.EnrollBiometricResponse
import id.co.bri.brimo.models.apimodel.response.bripoin.BripoinCouponResponse
import id.co.bri.brimo.models.apimodel.response.profilerevamp.InfoTetangBrimoResp
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferConfirmAccBinding
import id.co.bri.brimo.models.apimodel.response.smarttransfer.SmartTransferUserConsentData
import id.co.bri.brimo.models.apimodel.response.voip.CategoryVoipRes
import id.co.bri.brimo.ui.activities.ChatBankingActivity
import id.co.bri.brimo.ui.activities.DashboardIBActivity.TAG_SWITCH_PROFILE
import id.co.bri.brimo.ui.activities.DashboardIBActivity.r
import id.co.bri.brimo.ui.activities.EditFastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.EditFastMenuRevampActivity
import id.co.bri.brimo.ui.activities.FastMenuNewSkinActivity
import id.co.bri.brimo.ui.activities.FormChatBankingActivity
import id.co.bri.brimo.ui.activities.InfoKursActivity
import id.co.bri.brimo.ui.activities.InfoSahamActivity
import id.co.bri.brimo.ui.activities.KontakKamiActivity
import id.co.bri.brimo.ui.activities.ListRekeningCategoryActivity
import id.co.bri.brimo.ui.activities.LupaPinActivity
import id.co.bri.brimo.ui.activities.PilihAtmActivity
import id.co.bri.brimo.ui.activities.PilihanKantorTerdekatActivity
import id.co.bri.brimo.ui.activities.SyaratKetentuanActivity
import id.co.bri.brimo.ui.activities.base.BaseActivity
import id.co.bri.brimo.ui.activities.bripoin.DetailAkunActivity
import id.co.bri.brimo.ui.activities.bripoin.SNKBripoinActivity
import id.co.bri.brimo.ui.activities.ccqrismpm.SofQrisActivity
import id.co.bri.brimo.ui.activities.changepinnewskin.ChangePinActivity
import id.co.bri.brimo.ui.activities.changepassnewskin.ChangePassAccountActivity
import id.co.bri.brimo.ui.activities.changepinnewskin.ChangeNewPinActivity
import id.co.bri.brimo.ui.activities.infobrimo.InfoBrimoActivity
import id.co.bri.brimo.ui.activities.infopage.OpenGeneralInfoPageActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.pass.EnterPassActivity
import id.co.bri.brimo.ui.activities.newskinonboarding.pin.EnterCurrentPinActivity
import id.co.bri.brimo.ui.activities.pengelolaankartu.PengelolaanKartuNewActivity
import id.co.bri.brimo.ui.activities.smarttransfer.DashboardSmartTransferActivity
import id.co.bri.brimo.ui.activities.smarttransfer.RekomendasiRekeningSmartTransferActivity
import id.co.bri.brimo.ui.activities.ssc.SelfServiceActivity
import id.co.bri.brimo.ui.activities.transactionlimitinformation.TransactionLimitInformationActivity
import id.co.bri.brimo.ui.activities.voip.CategoryVoipActivity
import id.co.bri.brimo.ui.customviews.BilingualUI
import id.co.bri.brimo.ui.fragments.BaseFragment
import id.co.bri.brimo.ui.fragments.NewSkinBaseFragment
import id.co.bri.brimo.ui.fragments.SmartTransferStoryFragment
import id.co.bri.brimo.ui.fragments.biometric.PinBiometricFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.BottomSheetCustomViewGeneralFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralFragment.showDialogInformation
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment
import id.co.bri.brimo.ui.fragments.bottomsheet.OpenBottomSheetGeneralNewSkinFragment.showDialogConfirmation
import id.co.bri.brimo.ui.fragments.login.BottomSheet2ButtonLogin
import id.co.bri.brimo.util.getSavedLanguage
import org.chromium.base.ThreadUtils.runOnUiThread
import java.lang.Boolean.FALSE
import java.lang.Boolean.TRUE
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import javax.inject.Inject

class ProfileRevampFragment :
    NewSkinBaseFragment(),
    OnRefreshListener,
    IProfileView,
    BubbleShowCaseListener,
    View.OnClickListener,
    BiometricCallback,
    ListProfileInfoAdapter.ItemProfileInfoClick,
    PinBiometricFragment.SendPin,
    BottomSheet2ButtonLogin.DialogDefaultListener {


    @Inject
    lateinit var profilePresenter: IProfilePresenter<IProfileView>

    private var _binding: FragmentProfileRevampBinding? = null
    private val viewBinding get() = _binding!!

    private var isLoading: Boolean = false

    private var skeletonProfilBripoin: SkeletonScreen? = null
    private var skeletonBripoin: SkeletonScreen? = null
    private var skeletonBripoinCoupon: SkeletonScreen? = null
    private var errorMessage: String? = null
    private var langtitude: Double? = null
    private var longtitude: Double? = null
    private var gpsEnabled = false
    private var networkEnabled = false
    // untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//    private var isVoiceAssistant = false
    private lateinit var gpsTracker: GpsTracker
    private lateinit var locationManager: LocationManager
    private val PERMISSIONS_MAP = arrayOf(Manifest.permission.ACCESS_FINE_LOCATION)
    private var bripointDetailAccountResponse: BripointDetailAccountResponse? = null
    private var briPoinResponse: BripoinResponse? = null
    private var bripoinCouponResponse: BripoinCouponResponse? = null

    var listProfileInfoAdapter: ListProfileInfoAdapter? = null
    private var session: String? = null
    private var userName: String? = null
    private var tokenKey: String? = null

    private lateinit var bubbleDetailAkun: BubbleShowCaseBuilder
    private lateinit var bubblePengaturan: BubbleShowCaseBuilder
    private lateinit var bubbleKeamanan: BubbleShowCaseBuilder
    private lateinit var bubbleKontakBRI: BubbleShowCaseBuilder
    private lateinit var bubbleInformasi: BubbleShowCaseBuilder
    private lateinit var bubbleSmartTransfer: BubbleShowCaseBuilder
    private lateinit var bubbleShowCaseSequence: BubbleShowCaseSequence

    private lateinit var lyManager: LinearLayoutManager
    private val brImoPrefRepository by lazy(LazyThreadSafetyMode.NONE) { BRImoPrefRepository(context) }
    private var isClickLocation = "";

    private var btnForgetPin: Runnable = Runnable {
        val intent = EnterPassActivity.launchIntent(requireActivity())
        startActivityForResult(intent, Constant.REQ_UBAH_PIN)
    }


    var secBtnFunction: Runnable = Runnable {

    }

    private var bottomSheetSelectLanguage: BottomSheetCustomViewGeneralFragment? = null
    companion object {
        @JvmStatic
        fun newInstance(sMsg: String?): ProfileRevampFragment {
            val fragment = ProfileRevampFragment()
            val args = Bundle()
            args.putString(MESSAGE_SUCCESS, sMsg)
            fragment.arguments = args
            return fragment
        }

        private const val TAG = "ProfileRevampFragment"
        private const val MESSAGE_SUCCESS = "message"
    }

    override fun onCreateView(
        inflater: LayoutInflater, container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        // Inflate the layout for this fragment
        _binding = FragmentProfileRevampBinding.inflate(inflater, container, false)
        return viewBinding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setSkeletonView()

        injectDependency()

        setupView()

        setupListener()

        setupTransparentStatusBar()
    }

    private fun injectDependency() {
        activityComponent.inject(this)
        if (profilePresenter != null) {
            profilePresenter.setView(this)
            profilePresenter.setFormAkunUrl(GeneralHelper.getString(R.string.url_profile))
            profilePresenter.setLogoutUrl(GeneralHelper.getString(R.string.url_logout))
            profilePresenter.setUrlProfilBripoint(GeneralHelper.getString(R.string.url_user_profil_bripoint))
            profilePresenter.setUrlBripoint(GeneralHelper.getString(R.string.url_bripoin))
            profilePresenter.setUrlChatBanking(GeneralHelper.getString(R.string.url_chat_banking_status))
            profilePresenter.setUrlVoip(GeneralHelper.getString(R.string.url_get_list_voip))
            profilePresenter.setUrlRevoke(GeneralHelper.getString(R.string.url_revoke_session))
            profilePresenter.getInitiateResource()
            profilePresenter.setUrlEnrollBiometric(GeneralHelper.getString(R.string.url_aktivasi_biometric))
            profilePresenter.setUrlRemoveBiometric(GeneralHelper.getString(R.string.url_remove_biometric))
            profilePresenter.setUrlCheckSmartTransfer(GeneralHelper.getString(R.string.smart_transfer_user_consent_url))
            profilePresenter.setUrlSmartTransferManageUserConsent(GeneralHelper.getString(R.string.smart_transfer_manage_user_consent_url))
            profilePresenter.setUrlTentangBrimo(GeneralHelper.getString(R.string.url_tentang_brimo))
            profilePresenter.setUrlPrefrences(GeneralHelper.getString(R.string.url_prefrences_bilingual))

            // untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//            profilePresenter.setUrlUpdateAktivasiVoiceAssistant(getString(R.string.url_set_aktivasi_voice_assistant))
            profilePresenter.start()
        }
    }

    private fun setSkeletonView() {
        isLoading = true

        skeletonProfilBripoin = Skeleton.bind(viewBinding.llAkun)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.item_skeleton_akun_bripoint)
            .show()

        skeletonBripoin = Skeleton.bind(viewBinding.lyPoint)
                .shimmer(true)
                .angle(20)
                .duration(1200)
                .load(R.layout.item_skeleton_bripoint)
                .show()

        skeletonBripoinCoupon = Skeleton.bind(viewBinding.cvCoupon)
            .shimmer(true)
            .angle(20)
            .duration(1200)
            .load(R.layout.item_skeleton_coupon_bripoin)
            .show()
    }

    private fun setupListener() {
        viewBinding.swipeRefresh.setOnRefreshListener(this)
        viewBinding.llAkun.setOnClickListener(this)
        viewBinding.clLogout.setOnClickListener(this)
    }

    private fun setupView() {
        //show version App
        viewBinding.tvVersi.text = String.format(
            GeneralHelper.getString(R.string.version_text),
            GeneralHelper.getLastAppVersion()
        )
        viewBinding.llAkun.isEnabled = false

        setBiometricType(requireActivity())
        val infoProfile = MenuConfig.getMenuProfileRevamp(profilePresenter.onGetBiometricType(), profilePresenter.getBioChanged())

        listProfileInfoAdapter = ListProfileInfoAdapter(infoProfile,this)

        checkStatusBio()

        lyManager = LinearLayoutManager(context, LinearLayoutManager.VERTICAL, false)
        viewBinding.rvListInfo.apply {
            layoutManager = lyManager
            adapter = listProfileInfoAdapter
        }

        initBripoinCoupon()
        handleFromSearchMenu()
    }

    private fun handleFromSearchMenu(){
        if (requireActivity().intent.getBooleanExtra(TAG_SWITCH_PROFILE, false)){
            viewBinding.scrollView.post{
                val height = viewBinding.rvListInfo.findViewHolderForAdapterPosition(1)?.itemView?.height ?: 0
                viewBinding.scrollView.scrollTo(0, viewBinding.rvListInfo.top + height)
            }
        }
    }

    private fun initBripoinCoupon() {
        with(viewBinding.clBripoinCoupon) {
            isVisible = false //true
            setOnClickListener(null)
        }

        skeletonBripoinCoupon?.show()
        profilePresenter.apply {
            setUrlBripoinCoupon(GeneralHelper.getString(R.string.url_info_kupon_brimo_festival_v3))
            getBripoinCoupon()
        }
    }

    override fun onSuccessBripoinCoupon(response: BripoinCouponResponse?) {
        if (response == null) return

        this.bripoinCouponResponse = response

        val openUntil = bripoinCouponResponse?.openUntil
        if (openUntil.isNullOrEmpty()) {
            viewBinding.apply {
                clBripoinCoupon.isVisible = false
                cvCoupon.isVisible = false
            }
        } else {
            brImoPrefRepository.saveOpenCampaign(openUntil)
            showBripoinCouponView()
        }
    }

    override fun onFailedBripoinCoupon(code: String?) {
        viewBinding.apply {
            skeletonBripoinCoupon?.hide()
            if (isCampaignValid()) {
                clBripoinCoupon.isVisible = false //true
                cvCoupon.isVisible = true
                visibilityCoupon.isVisible = false
                visibilityFailedLoadData.isVisible = true

                // Handle click event
                clBripoinCoupon.setOnClickListener(null)
                imgReloadBripoin.setOnClickListener {
                    initBripoinCoupon()
                }
            } else {
                clBripoinCoupon.isVisible = false
                cvCoupon.isVisible = false
                visibilityCoupon.isVisible = false
                visibilityFailedLoadData.isVisible = false
            }
        }
    }

    override fun onSuccessGetListVoip(response: CategoryVoipRes?) {
        response?.let { CategoryVoipActivity.launchIntent(baseActivity, it) }
    }

    override fun onSuccessGetSmartTransferFirstVisit(tnc: String) {
        onFirstLaunchSmartTransfer(tnc)
    }

    override fun onSuccessGetSmartTransferUserConsent(smartTransferUserConsentData: SmartTransferUserConsentData?) {
        activity?.let { DashboardSmartTransferActivity.launchIntent(it) }
    }

    override fun onFailedGetSmartTransferUserConsent() {
        showDialogRekeningTidakTerdeteksi()
    }

    override fun onSuccessSmartTransferManageUserConsent(smartTransferConfirmAccBinding: SmartTransferConfirmAccBinding?) {
        activity?.let { RekomendasiRekeningSmartTransferActivity.launchIntent(it) }
    }

    private fun showBripoinCouponView() {
        viewBinding.apply {
            skeletonBripoinCoupon?.hide()
            bripoinCouponResponse?.let { response ->
                showBripoinCoupon(isCampaignValid())

                GeneralHelper.loadImageUrl(
                    context,
                    response.iconUrl,
                    imgCoupon,
                    0,
                    0
                )

                tvCouponCountInfo.text = response.couponCountText
                tvCouponLastUpdate.text = response.lastUpdateText

                if (isCampaignValid()) {
                    clBripoinCoupon.setOnClickListener {
                        SNKBripoinActivity.launchIntent(
                            requireActivity(),
                            response.snkTitle,
                            response.snkText,
                        )
                    }
                }
            }
        }
    }

    private fun parseDate(dateString: String?): Date? {
        return try {
            val format = SimpleDateFormat(Constant.DATE_FORMAT_YYYY_MM_DD, Locale.getDefault())
            dateString?.let { format.parse(it) }
        } catch (e: Exception) {
            e.printStackTrace()
            null
        }
    }

    private fun showBripoinCoupon(isShow: Boolean) {
        viewBinding.apply {
            clBripoinCoupon.isVisible = false //isShow
            cvCoupon.isVisible = isShow
            visibilityCoupon.isVisible = isShow
            visibilityFailedLoadData.isVisible = !isShow
        }
    }

    private fun isCampaignValid(): Boolean {
        val getOpenCampaignEvent = brImoPrefRepository.openCampaignEvent
        val openUntilDate = parseDate(getOpenCampaignEvent)
        val currentDate = Calendar.getInstance().apply {
            set(Calendar.HOUR_OF_DAY, 0)
            set(Calendar.MINUTE, 0)
            set(Calendar.SECOND, 0)
            set(Calendar.MILLISECOND, 0)
        }.time

        return openUntilDate != null && currentDate <= openUntilDate
    }

    private fun onClickMenuInfo(id: Int, isSwitchChecked: Boolean){
        when(id){
//            Constant.ProfileInfoRevamp.VOICE_ASSISTANT -> {
//                isVoiceAssistant = true
//                openPin()
//            }
            Constant.ProfileInfoRevamp.FASTMENU -> {
//                EditFastMenuRevampActivity.launchIntent(activity, false, null, null)
                EditFastMenuNewSkinActivity.launchIntent(activity, false)
            }

            Constant.ProfileInfoRevamp.UPDATE_REKENING -> {
                ListRekeningCategoryActivity.launchIntent(activity)
            }

            Constant.ProfileInfoRevamp.PENGELOLAAN_KARTU -> {
                activity?.let { PengelolaanKartuNewActivity.launchIntent(it) }
            }

            Constant.ProfileInfoRevamp.UBAH_PIN -> {
                activity?.let {

                    showDialogConfirmation(
                        parentFragmentManager,
                        R.drawable.lock_password_new,
                        "ic_account_saved",
                        "Yakin kamu ingin mengubah PIN?",
                        "Jika kamu ubah PIN di aplikasi ini, maka otomatis menggantikan PIN yang digunakan di BRImo. Lanjut ubah PIN?",
                        BaseActivity.createKotlinFunction0(btnForgetPin),
                        BaseActivity.createKotlinFunction0(secBtnFunction),
                        false,
                        "Lanjut Ubah PIN",
                        resources.getString(R.string.btn_cancel),
                        false,
                        showCloseButton = true
                    )
                }
            }

            Constant.ProfileInfoRevamp.UBAH_KATA_KUNCI -> {
                ChangePassAccountActivity.launchIntent(requireActivity())
            }

            Constant.ProfileInfoRevamp.BIOMETRIC -> {
//                isVoiceAssistant = false
                if (!isSwitchChecked) {
                    openPin()
                } else {
                    if (TRUE == hasBiometricEnrolled(requireActivity().baseContext)) {
                        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                            setBiometricType(requireActivity())
                            displayPromptForEnroll(requireActivity(), true)
                        }
                    } else {
                        showDialogFinger()
                    }
                }
            }

            Constant.ProfileInfoRevamp.PUSAT_BANTUAN -> {
                SelfServiceActivity.launchIntent(activity)
            }

            Constant.ProfileInfoRevamp.CHAT_BANKING -> {
                profilePresenter.onGetChatBanking()
            }

            Constant.ProfileInfoRevamp.LAYANAN_BEBAS_PULSA -> {
                profilePresenter.getVoip()
            }

            Constant.ProfileInfoRevamp.KONTAK_KAMI -> {
                KontakKamiActivity.launchIntent(activity)
            }

            Constant.ProfileInfoRevamp.JENIS_DAN_LIMIT -> {
                val newIntent = Intent(activity, TransactionLimitInformationActivity::class.java)
                startActivity(newIntent)
            }

            Constant.ProfileInfoRevamp.INFO_KURS -> {
                InfoKursActivity.launchIntent(activity)
            }

            Constant.ProfileInfoRevamp.INFO_SAHAM -> {
                InfoSahamActivity.launchIntent(activity)
            }

            Constant.ProfileInfoRevamp.LOKASI_ATM -> {
                isClickLocation = "ATM"
                requestPermission(activity, *PERMISSIONS_MAP)
            }

            Constant.ProfileInfoRevamp.LOKASI_KANTOR -> {
                isClickLocation = "KANTOR"
                requestPermission(activity, *PERMISSIONS_MAP)
            }

            Constant.ProfileInfoRevamp.SNK -> {
                SyaratKetentuanActivity.launchIntent(activity)
            }

            Constant.ProfileInfoRevamp.QRIS -> {
                activity?.let { SofQrisActivity.launchIntent(it) }
            }
            Constant.ProfileInfoRevamp.TENTANG_BRIMO -> {
                profilePresenter.getDataTentangBrimo()
            }

            Constant.ProfileInfoRevamp.LANGUAGE -> {
                BilingualUI(
                    requireActivity(),
                    requireActivity().supportFragmentManager,
                    getSavedLanguage(requireActivity())
                ) { value ->
                     profilePresenter.updatePrefrencesLanguage(value)
                }
            }
            Constant.ProfileInfoRevamp.SMART_TRANSFER -> {
                profilePresenter.checkSmartTransfer()
            }
            Constant.ProfileInfoRevamp.LOGOUT -> {
                logOut()
            }
        }
    }

    fun openPin() {
        val pinFragment = PinBiometricFragment(requireActivity(), this)
        pinFragment.show()
    }

    private fun checkStatusBio() {
        if (TRUE == profilePresenter.getStatusAktivasi()) {
            listProfileInfoAdapter!!.isSwitchChecked(5, true)
        } else {
            listProfileInfoAdapter!!.isSwitchChecked(5, false)
        }
    }

// untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//    private fun checkStatusVoiceAssistant() {
//        listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.VOICE_ASSISTANT, profilePresenter.aktivasiVoiceAssistant)
//    }

    private fun viewPoin(gagal: Boolean, msg: String, poin: Double) {
        if (gagal) {
            viewBinding.tvPoin.text = msg
            viewBinding.tvPoin.setTextColor(GeneralHelper.getColor(R.color.black3))
        } else {
            viewBinding.tvPoin.text = GeneralHelper.formatNominalBiasa(poin)
            viewBinding.tvPoin.setTextColor(GeneralHelper.getColor(R.color.colorTextBlueBri))
        }

        viewBinding.llAkun.isEnabled = true
    }

    private fun settingBubbleTutorial() {
        val listBubble: MutableList<BubbleShowCaseBuilder> = ArrayList()
        bubbleDetailAkun = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.detail_akun_bubble_title))
            .description(GeneralHelper.getString(R.string.detail_akun_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(viewBinding.llAkun)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(this)

        val viewHolder0 = viewBinding.rvListInfo.findViewHolderForAdapterPosition(0)
        val pengaturanView = viewHolder0 as ListProfileInfoAdapter.ViewHolder
        this.bubblePengaturan = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.pengaturan_bubble_title))
            .description(GeneralHelper.getString(R.string.pengaturan_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(pengaturanView.binding.llItem)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(this)

        bubbleSmartTransfer = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.title_smart_transfer))
            .description(GeneralHelper.getString(R.string.desc_bubble_smart_transfer))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(pengaturanView.binding.llItem)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(this)

        val viewHolder1 = viewBinding.rvListInfo.findViewHolderForAdapterPosition(1)
        val keamananView = viewHolder1 as ListProfileInfoAdapter.ViewHolder
        bubbleKeamanan = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.keamanan_bubble_title))
            .description(GeneralHelper.getString(R.string.keamanan_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(keamananView.binding.llItem)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(
                object : BubbleShowCaseListener {
                    override fun onTargetClick(bubbleShowCase: BubbleShowCase) {

                    }

                    override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
                        viewBinding.scrollView.scrollTo(0, viewBinding.scrollView.bottom / 2)
                    }

                    override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {
                        // do nothing
                    }

                    override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {
                        // do nothing
                    }

                    override fun onSkipActionClick(bubbleShowCase: BubbleShowCase) {
                        // do nothing
                    }

                }
            )

        val viewHolder2 = viewBinding.rvListInfo.findViewHolderForAdapterPosition(2)
        val kontakBRIView = viewHolder2 as ListProfileInfoAdapter.ViewHolder
        bubbleKontakBRI = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.kontak_bri_bubble_title))
            .description(GeneralHelper.getString(R.string.kontak_bri_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(kontakBRIView.binding.llItem)
            .buttonTitle(GeneralHelper.getString(R.string.berikutnya_txt))
            .arrowPosition(BubbleShowCase.ArrowPosition.BOTTOM)
            .textViewLewati(GeneralHelper.getString(R.string.lewati_txt))
            .enableViewButtonSkip(true)
            .listener(
                object : BubbleShowCaseListener {
                    override fun onTargetClick(bubbleShowCase: BubbleShowCase) {

                    }

                    override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
                        viewBinding.scrollView.fullScroll(ScrollView.FOCUS_DOWN)
                    }

                    override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {
                        // do nothing
                    }

                    override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {
                        // do nothing
                    }

                    override fun onSkipActionClick(bubbleShowCase: BubbleShowCase) {
                        // do nothing
                    }

                }
            )

        val viewHolder3 = viewBinding.rvListInfo.findViewHolderForAdapterPosition(3)
        val informasiView = viewHolder3 as ListProfileInfoAdapter.ViewHolder
        bubbleInformasi = BubbleShowCaseBuilder(requireActivity())
            .title(GeneralHelper.getString(R.string.informasi_bubble_title))
            .description(GeneralHelper.getString(R.string.informasi_bubble_desc))
            .backgroundColor(Color.WHITE)
            .textColor(GeneralHelper.getColor(R.color.neutral_dark10))
            .targetView(informasiView.binding.llItem)
            .buttonTitle(GeneralHelper.getString(R.string.finish))
            .enableLewati(true)
            .arrowPosition(BubbleShowCase.ArrowPosition.TOP)
            .enableViewButtonSkip(false)
            .listener(this)

        listBubble.add(bubbleDetailAkun)
        listBubble.add(bubblePengaturan)
        listBubble.add(bubbleSmartTransfer)
        listBubble.add(bubbleKeamanan)
        listBubble.add(bubbleKontakBRI)
        listBubble.add(bubbleInformasi)
        r.addShowCases(listBubble)

    }

    override fun onRefresh() {
        if (!isLoading) {
            skeletonProfilBripoin?.show()
            skeletonBripoin?.show()
            skeletonBripoinCoupon?.show()
            showBripoinCoupon(isCampaignValid())

            profilePresenter.onGetProfilBripoint()
            profilePresenter.onGetBripoint()
            profilePresenter.getBripoinCoupon()

            // untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//            profilePresenter.aktivasiVoiceAssistant

            viewBinding.ivPoin.visibility = View.VISIBLE
            viewBinding.icChevron.visibility = View.VISIBLE
            isLoading = true
        }
    }

    private fun showDialogRekeningTidakTerdeteksi() {
        showDialogInformation(
            requireActivity().supportFragmentManager,
            "",
            "mutation_not_found",
            GeneralHelper.getString(R.string.title_smart_transfer_tidak_terdeteksi),
            GeneralHelper.getString(R.string.desc_smart_transfer_tidak_terdeteksi),
            { null },
            true,
            GeneralHelper.getString(R.string.mengerti)
        )
    }

    private fun onFirstLaunchSmartTransfer(tnc: String) {
        val imgResPath = listOf(
            R.drawable.highlight_smart_transfer_1,
            R.drawable.highlight_smart_transfer_2,
            R.drawable.highlight_smart_transfer_3
        )
        val storyFragment = SmartTransferStoryFragment(
            requireActivity(),
            imgResPath,
            tnc
        ) { profilePresenter.smartTransferManageUserConsent(it) }
        storyFragment.show(parentFragmentManager, storyFragment.tag)
    }

    override fun onSuccessGetProfile(profileResponse: ProfileResponse?) {
        isLoading = false
        viewBinding.swipeRefresh.isRefreshing = false
    }

    override fun onLogOut(message: String?) {
//        FastMenuActivity.launchIntent(activity)
        FastMenuNewSkinActivity.launchIntent(activity)
    }

    override fun onSuccessLoadProfilBripoint(detailAccountResponse: BripointDetailAccountResponse?) {
        if (skeletonProfilBripoin != null) skeletonProfilBripoin!!.hide()

        bripointDetailAccountResponse = detailAccountResponse

        viewBinding.namaAkun.text = bripointDetailAccountResponse!!.profileData.name
        viewBinding.tvInisial.text =
            GeneralHelper.formatInitialName(bripointDetailAccountResponse!!.profileData.name)

        viewBinding.ivProfilePict.visibility = View.GONE
        viewBinding.icProfile.visibility = View.VISIBLE

        isLoading = false
        this.viewBinding.swipeRefresh.isRefreshing = false
        this.viewBinding.swipeRefresh.setEnabled(true)

    }

    override fun onGetPoint(response: BripoinResponse) {
        if (skeletonBripoin != null) skeletonBripoin!!.hide()

        briPoinResponse = response

        if (response.bripoin.pointString.isNullOrEmpty()) {
            response.bripoin.pointString = GeneralHelper.getString(R.string.empty)
        }

        viewPoin(
            TRUE == briPoinResponse!!.bripoin.boldText,
            response.bripoin.pointString,
            response.bripoin.point
        )

        GeneralHelper.loadIconTransaction(
            context,
            briPoinResponse!!.bripoin.iconPath,
            briPoinResponse!!.bripoin.iconName,
            viewBinding.ivPoin,
            R.drawable.ic_bripoin
        )

        isLoading = false
        viewBinding.llAkun.isEnabled = true

        settingBubbleTutorial()
        if (FALSE == brImoPrefRepository.profileRevampBubble) {
            bubbleShowCaseSequence = r
            bubbleShowCaseSequence.show()
            brImoPrefRepository.saveProfileRevampBubble(true)
        }
    }

    override fun onRefreshSwipe() {
        isLoading = false
        this.viewBinding.swipeRefresh.isRefreshing = false
        this.viewBinding.swipeRefresh.setEnabled(true)
    }

    override fun onExceptionProfil(message: String?) {
    }

    override fun onSuccessChatBanking(chatBankingResponse: ChatBankingResponse?) {
        if (chatBankingResponse!!.isNew) ChatBankingActivity.launchIntent(
            activity,
            chatBankingResponse
        )
        else FormChatBankingActivity.launchIntent(activity, chatBankingResponse)
    }

    override fun onException12(message: String?) {
        if (GeneralHelper.isContains(
                Constant.LIST_TYPE_GAGAL,
                message
            )
        ) GeneralHelper.showDialogGagalBack(activity, message)
        else showSnackbarErrorMessage(message, ALERT_ERROR, this, false)
    }

    override fun onClick(view: View) {
        when (view.id) {
            R.id.ll_akun -> {
                if (bripointDetailAccountResponse != null) {
                    DetailAkunActivity.launchIntent(
                        activity,
                        bripointDetailAccountResponse,
                        briPoinResponse
                    )
                }
            }

            R.id.cl_logout -> {
                logOut()
            }
        }
    }

    override fun clickItem(id: Int, isSwitchChecked: Boolean) {
        onClickMenuInfo(id, isSwitchChecked)
    }

    override fun onExceptionBriPoin(message: String?) {
        if (viewBinding == null) return
        if (skeletonProfilBripoin != null) skeletonProfilBripoin?.hide()

        viewPoin(true, message ?: GeneralHelper.getString(R.string.txt_failed_load),0.0)
        viewBinding.swipeRefresh.isRefreshing = false
        viewBinding.swipeRefresh.setEnabled(true)
        isLoading = false
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)

        try {
            if (requestCode == Constant.REQUEST_LOCATION_MAP) {
                getLocation()
                PilihAtmActivity.launchIntent(activity)
            }
        } catch (e: Exception) {
            if (!GeneralHelper.isProd()) {
                Log.e(TAG, "onRequestPermissionsResult: ", e)
            }
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == Constant.REQ_NON_PAYMENT && data != null) {
            if (resultCode == Activity.RESULT_CANCELED) {
                errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                //menampilkan snacknar error
                showSnackbarErrorMessageRevampAkun(errorMessage, ALERT_ERROR_AKUN)
                errorMessage = null
            }
        } else if (requestCode == Constant.REQ_EDIT_SAVED && data != null) {
            if (resultCode == Activity.RESULT_OK) {
                val result = data.getStringExtra(Constant.SUCCESS_EDIT_FAST_MENU)
                showSnackbarErrorMessageRevampAkun(resources.getString(R.string.fast_menu_berhasil_diubah), ALERT_CONFIRM)
            }

            if (resultCode == Constant.RESULT_BACK) {
                val resultIntent = data.getStringExtra(Constant.SUCCESS_GET_UNAVAILABLE_FAST_MENU)
                showSnackbarErrorMessageRevampAkun(resources.getString(R.string.fast_menu_sedang_disesuaikan), ALERT_CONFIRM)
            }

            if (resultCode == Constant.RESULT_KENDALA_SISTEM) {
                val resultIntent = data.getStringExtra(Constant.KENDALA_SISTEM)
                showSnackbarErrorMessageRevampAkun(resultIntent, ALERT_ERROR)
            }

            if (resultCode == Activity.RESULT_CANCELED) {
                errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                //menampilkan snacknar error
                showSnackbarErrorMessageRevampAkun(errorMessage, ALERT_ERROR)
                errorMessage = null
            }
        } else if (requestCode == Constant.REQ_KATEGORI && resultCode == Activity.RESULT_OK && data != null) {
            if (!isLoading) {
                profilePresenter.onGetProfilBripoint()
                profilePresenter.onGetBripoint()
                viewBinding.ivPoin.visibility = View.VISIBLE
                skeletonProfilBripoin!!.show()
                skeletonBripoin!!.show()
                viewBinding.icChevron.visibility = View.VISIBLE
                isLoading = true
            }
        } else if (requestCode == Constant.REQ_CARD) {
            if (resultCode == Activity.RESULT_CANCELED && data != null) {
                errorMessage = data.getStringExtra(Constant.TAG_ERROR_MESSAGE)
                showSnackbarErrorMessageRevampAkun(errorMessage, ALERT_ERROR)
            }
        }

        if (requestCode == Constant.REQ_NEED_REVOKE) {
            profilePresenter.revokeSession(request())
        }

        if (requestCode == Constant.REQ_UBAH_KATA_KUNCI && resultCode == Activity.RESULT_OK) {
            GeneralHelperNewSkin.showCustomSnackBar(
                requireActivity().findViewById(R.id.contentProfile),
                getString(R.string.txt_password_berhasil_diubah)
            )
            checkStatusBio()
        }
        if (requestCode == Constant.REQ_UBAH_PIN && resultCode == Activity.RESULT_OK) {
            GeneralHelperNewSkin.showCustomSnackBar(
                requireActivity().findViewById(R.id.contentProfile),
                getString(R.string.txt_pin_berhasil_diubah)
            )
        }
    }

    override fun onInitiateResourceSuccess(username: String, tokenKey: String?) {
        userName = username
        this.tokenKey = tokenKey
    }

    override fun onException12Bio(message: String?) {
        if (listProfileInfoAdapter!!.getStatusSwitch()!!) {
            listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, true)
            profilePresenter.updateStatusAktivasi(true)
            showSnackbarErrorMessageRevampAkun(message, ALERT_ERROR)
        } else {
            listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, false)
            profilePresenter.updateStatusAktivasi(false)
            showSnackbarErrorMessageRevampAkun(message, ALERT_ERROR)
        }
    }

    override fun onExceptionBio(message: String?) {
        if (listProfileInfoAdapter!!.getStatusSwitch()!!) {
            listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, false)
            profilePresenter.updateStatusAktivasi(false)
            showSnackbarErrorMessageRevampAkun(message, ALERT_ERROR)
        } else {
            listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, true)
            profilePresenter.updateStatusAktivasi(true)
            showSnackbarErrorMessageRevampAkun(message, ALERT_ERROR)
        }
        if (GeneralHelper.isContains(Constant.LIST_TYPE_GAGAL, message)) GeneralHelper.showBottomDialog(requireActivity(), message)
        else showSnackbarErrorMessageRevampAkun(message, ALERT_ERROR)
    }

    override fun onExceptionAktivasiVoiceAssistant(newStatus: Boolean) {
        if (newStatus) {
            showSnackbarErrorMessageRevamp(getString(R.string.txt_failed_activated_voice_assistant), ALERT_ERROR, requireActivity(), false)
        } else {
            showSnackbarErrorMessageRevamp(getString(R.string.txt_failed_deactivated_voice_assistant), ALERT_ERROR, requireActivity(), false)
        }
    }

    override fun onSuccessRemoveBiometric() {
        listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, false)
        profilePresenter.updateStatusAktivasi(false)
        profilePresenter.updateBioChanged(false)
    }

    override fun onSuccessAktifBiometric(enrollBiometricResponse: EnrollBiometricResponse?) {
        val bioType = String.format(
            GeneralHelper.getString(R.string.success_biometric_setting),
            profilePresenter.onGetBiometricType()
        )
        listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, true)
        profilePresenter.updateStatusAktivasi(true)
        profilePresenter.updateBioChanged(false)
        profilePresenter.updateStatusUpdateBio(true)
        showSnackbarErrorMessageRevampAkun(bioType, ALERT_CONFIRM)
    }

    override fun onSuccessSetAktivasiVoiceAssistant(newStatus: Boolean?) {
        if (newStatus == true) {
            showSnackbarErrorMessageRevamp(getString(R.string.txt_voice_assistant_activated), ALERT_CONFIRM, requireActivity(), true)
        } else {
            showSnackbarErrorMessageRevamp(getString(R.string.txt_voice_assistant_deactivated), ALERT_CONFIRM, requireActivity(), true)
        }

    // untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//        checkStatusVoiceAssistant()
    }

    private fun request(): RevokeSessionRequest {
        return RevokeSessionRequest(
            userName,
            tokenKey,
            session
        )
    }

    override fun onSuccessChangeLanguage() {
        restartActivity();
    }

    fun getLocation() {
        gpsTracker = GpsTracker(context)
        if (gpsTracker.canGetLocation()) {
            langtitude = gpsTracker.getLatitude()
            longtitude = gpsTracker.getLongitude()

            locationManager = activity!!.getSystemService(Context.LOCATION_SERVICE) as LocationManager
            gpsEnabled = locationManager!!.isProviderEnabled(LocationManager.GPS_PROVIDER)
            networkEnabled = locationManager!!.isProviderEnabled(LocationManager.NETWORK_PROVIDER)

            if (isClickLocation == "ATM"){
                if (!gpsEnabled && !networkEnabled) {
                    getLocation()
                } else PilihAtmActivity.launchIntent(activity)
            } else if (isClickLocation == "KANTOR"){
                if (!gpsEnabled && !networkEnabled) {
                    getLocation()
                } else PilihanKantorTerdekatActivity.launchIntent(activity)
            }
        } else {
            showDialogEnableLocation()
        }
    }

    fun logOut() {
        showDialogConfirmation(
            parentFragmentManager,
            R.drawable.ic_notice_hexagon_new_skin,
            "",
            "Yakin ingin keluar dari aplikasi?",
            "Setelah keluar, kamu perlu login lagi untuk mengakses akun dan melanjutkan aktivitas.",
            createKotlinFunction0(firstBtnFunction),
            createKotlinFunction0(secondBtnFunction),
            false,
            GeneralHelper.getString(R.string.keluar),
            GeneralHelper.getString(R.string.batal2),
            false
        )
    }

    var firstBtnFunction: Runnable = Runnable {
        profilePresenter.logOut()
    }

    var secondBtnFunction: Runnable = Runnable {

    }

    protected fun requestPermission(activity: Activity?, vararg permissions: String?) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.M) {
            if (!hasPermissions(activity, *permissions)) {
                ActivityCompat.requestPermissions(
                    requireActivity(),
                    permissions,
                    Constant.REQUEST_LOCATION_MAP
                )
            } else {
                getLocation()
            }
        } else {
            getLocation()
        }
    }

    override fun onAuthenticationError(errorCode: Int, errString: CharSequence) {
        when (errorCode) {
            Constant.ERROR_USER_CANCELED, Constant.ERROR_NEGATIVE_BUTTON -> runOnUiThread(Runnable {
                listProfileInfoAdapter!!.isSwitchChecked(
                    Constant.ProfileInfoRevamp.BIOMETRIC,
                    false
                )
            })

            Constant.ERROR_CANCELED, Constant.ERROR_LOCKOUT -> onTooManyAttemps()
            Constant.ERROR_NO_BIOMETRICS -> showDialogFinger()
            else -> {}
        }
    }

    fun onTooManyAttemps() {
        runOnUiThread {
            listProfileInfoAdapter!!.isSwitchChecked(Constant.ProfileInfoRevamp.BIOMETRIC, false)
        }
        GeneralHelper.showSnackBarRevamp(
            requireActivity().findViewById(R.id.content),
            GeneralHelper.getString(R.string.too_many_attemps_snackbar)
        )
    }

    fun showDialogFinger() {
        val title = String.format(
            GeneralHelper.getString(R.string.title_go_setting),
            profilePresenter.onGetBiometricType()
        )

        val bottomSheet2ButtonLogin = BottomSheet2ButtonLogin.newInstance(
            GeneralHelper.getString(R.string.image_gagal_brimo),
            title,
            getString(R.string.desc_ke_setting_akun),
            GeneralHelper.getString(R.string.ke_pengaturan_biometric),
            GeneralHelper.getString(R.string.atur_nanti),
            this, false
        )
        bottomSheet2ButtonLogin.isCancelable = false
        bottomSheet2ButtonLogin.show(requireActivity().supportFragmentManager, "")
    }

    override fun onAuthenticationFailed() {
        //do nothing
    }

    override fun onAuthenticationSuccess() {
        runOnUiThread { this.openPin() }
    }

    override fun onBiometricChanged() {
        // do nothing
    }

    override fun onClickOk() {
        try {
            startActivity(Intent(Settings.ACTION_SETTINGS))
            if (!GeneralHelper.isProd()) {
                Log.d("testdebug1244", "openintnentelse3: ")
            }
        } catch (e: java.lang.Exception) {
            if (!GeneralHelper.isProd()) {
                Log.d("testdebug083", "onClickElse3: $e")
            }
        }
        try {
            startActivity(Intent(Constant.PACKAGE_OPLUS_SETTING_FEATURE_HOMEPAGE))
            if (!GeneralHelper.isProd()) {
                Log.d("testdebug1244", "openintnentelse4: ")
            }
        } catch (e: java.lang.Exception) {
            if (!GeneralHelper.isProd()) {
                Log.d("testdebug083", "onClickElse4: $e")
            }
        }
        runOnUiThread {
            listProfileInfoAdapter!!.isSwitchChecked(
                Constant.ProfileInfoRevamp.BIOMETRIC, false
            )
        }
    }

    override fun onClickCancel() {
        runOnUiThread {
            listProfileInfoAdapter!!.isSwitchChecked(
                Constant.ProfileInfoRevamp.BIOMETRIC, false
            )
        }
    }

    override fun onClickLogin() {
        //do nothing
    }

    override fun onSendPinComplete(pin: String?) {
        // untuk sementara di komen menunggu konfirmasi fitur ini release kapan
//        if (isVoiceAssistant) {
//            profilePresenter.updateAktivasiVoiceAssistant(!profilePresenter.aktivasiVoiceAssistant, pin)
//        } else {
            if (listProfileInfoAdapter!!.getStatusSwitch()!!) {
                profilePresenter.getRemoveBiometric(pin, profilePresenter.getValueKeyBiometric())
            } else {
                profilePresenter.getDataBiometric(pin, profilePresenter.getValueKeyBiometric())
            }
//        }
    }

    override fun onLupaPin() {
        LupaPinActivity.launchIntent(requireActivity())
    }

    override fun onBackBiometric() {
        if (listProfileInfoAdapter!!.getStatusSwitch()!!) {
            listProfileInfoAdapter!!.isSwitchChecked(
                Constant.ProfileInfoRevamp.BIOMETRIC, true
            )
            profilePresenter.updateStatusAktivasi(true)
        } else {
            listProfileInfoAdapter!!.isSwitchChecked(
                Constant.ProfileInfoRevamp.BIOMETRIC, false
            )
            profilePresenter.updateStatusAktivasi(false)
        }
    }

    override fun onTargetClick(bubbleShowCase: BubbleShowCase) {
        //do nothing
    }

    override fun onCloseActionImageClick(bubbleShowCase: BubbleShowCase) {
        //do nothing
    }

    override fun onBackgroundDimClick(bubbleShowCase: BubbleShowCase) {
        //do nothing
    }

    override fun onBubbleClick(bubbleShowCase: BubbleShowCase) {
        //do nothing
    }

    override fun onSkipActionClick(bubbleShowCase: BubbleShowCase) {
        //do nothing
    }

    override fun onSuccessDataTentangBrimo(infoTetangBrimoResp: InfoTetangBrimoResp?) {
        activity?.let { InfoBrimoActivity.launchIntent(it, infoTetangBrimoResp) }
    }

    override fun onHiddenChanged(hidden: Boolean) {
        super.onHiddenChanged(hidden)
        if (!hidden) {
            setupTransparentStatusBar();
        }
    }
}