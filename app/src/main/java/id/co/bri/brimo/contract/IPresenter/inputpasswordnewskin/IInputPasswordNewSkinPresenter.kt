package id.co.bri.brimo.contract.IPresenter.inputpasswordnewskin

import id.co.bri.brimo.contract.IPresenter.IMvpPresenter
import id.co.bri.brimo.contract.IView.IMvpView

interface IInputPasswordNewSkinPresenter<V : IMvpView?> : IMvpPresenter<V> {
    fun setUrlLogin(url: String)

    fun submitLogin()

    fun loginFingerprint()

    fun getLocation(location: String)

    fun getBioChanged(): Boolean

    fun getStatusUpdateBio(): Boolean

    fun updateStatusAktivasi(statusAktivasi: Boolean)

    fun getStatusAktivasi(): Boolean

    fun updateBioChange(statusBioChange: Boolean)

    fun updateStatusUpdateBio(statusUpdate: Boolean)

    fun getBioType(): String

    fun getNameOfUser(): String
}