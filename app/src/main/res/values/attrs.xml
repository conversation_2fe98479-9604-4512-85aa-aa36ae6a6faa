<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:tools="http://schemas.android.com/tools">

    <declare-styleable name="ScallopCardView">
        <attr name="scallopYRatio" format="float" />
        <attr name="useFooter" format="boolean" />
        <attr name="useDividerCenter" format="boolean" />

        <attr name="watermarkText" format="string" />
        <attr name="watermarkDrawable" format="reference" />
    </declare-styleable>

    <declare-styleable name="BaseInputView">
        <attr name="hintText" format="string" />
        <attr name="prefixText" format="string" />
        <attr name="expanded" format="boolean" />
    </declare-styleable>

    <declare-styleable name="BaseBottomSheetLayout">
        <attr name="setTitle" format="string" />
    </declare-styleable>

    <declare-styleable name="BayarButtonView">
        <attr name="labelText" format="string" />
        <attr name="amountText" format="string" />
    </declare-styleable>

    <declare-styleable name="FormInputDefaultView">
        <attr name="android:hint" />
        <attr name="android:text" />
        <attr name="android:inputType" />
        <attr name="android:maxLines" />
        <attr name="android:maxLength" />
        <attr name="android:enabled" />
        <attr name="android:imeOptions" />
        <attr name="android:nextFocusForward" />
        <attr name="android:digits" />
        <attr name="showCounter" format="boolean" />
        <attr name="iconDrawable" format="reference" />
        <attr name="iconColor" format="color" />
        <attr name="iconMode" format="enum">
            <enum name="custom" value="-1" />
            <enum name="none" value="0" />
            <enum name="password_toggle" value="1" />
            <enum name="clear_text" value="2" />
            <enum name="dropdown_menu" value="3" />
        </attr>
        <attr name="passwordIconDrawable" format="reference" />
        <attr name="passwordIconColor" format="color" />
        <attr name="addValidation" format="flags">
            <flag name="min3Char" value="0x01" />
        </attr>
        <attr name="addFilter" format="flags">
            <flag name="digitOnly" value="0x01" />
            <flag name="allCaps" value="0x02" />
        </attr>
    </declare-styleable>

    <declare-styleable name="TagLayout">
        <!-- set the max select count;
        the default value is 1, it means you can select one item at the same time.-->
        <attr name="maxSelectCount" format="integer|enum">
            <enum name="all" value="-1" />
            <enum name="single" value="1" />
            <enum name="none" value="0" />
        </attr>
        <!-- is the all item in one line, the default value is false -->
        <attr name="singleLine" format="boolean" />
        <!-- set the max item in one line the default is -1,
        that means view will auto calculate the child in one line.-->
        <attr name="maxItemsInOneLine" format="integer" />
        <!-- if the item has equal width, the default is false.
        this property only when you set maxItemsInOneLine property,
        then this will effect-->
        <attr name="isItemsEqualWidth" format="boolean" />
    </declare-styleable>

    <declare-styleable name="RatingBar" tools:ignore="ResourceName">
        <attr name="starImageSize" format="dimension" />
        <attr name="starImageWidth" format="dimension" />
        <attr name="starImageHeight" format="dimension" />
        <attr name="starImagePadding" format="dimension" />
        <attr name="starCount" format="integer" />
        <attr name="starNum" format="integer" />
        <attr name="starEmpty" format="reference" />
        <attr name="starFill" format="reference" />
        <attr name="starHalf" format="reference" />
        <attr name="clickable" format="boolean" />
        <attr name="halfstart" format="boolean" />
    </declare-styleable>

    <declare-styleable name="PrimaryButton">
        <attr name="android:text" />
        <attr name="android:textAllCaps" />
        <attr name="android:enabled" />
    </declare-styleable>

    <declare-styleable name="SaldoTextView">
        <attr name="text" format="string" />
        <attr name="size" format="enum">
            <enum name="DEFAULT" value="0" />
            <enum name="MINI" value="1" />
            <enum name="LARGE" value="2" />
        </attr>
    </declare-styleable>


    <declare-styleable name="PinCodeView">
        <attr name="lp_empty_pin_dot" format="integer" />
        <attr name="lp_full_pin_dot" format="integer" />
    </declare-styleable>

    <declare-styleable name="KeypadButtonView">
        <attr name="lp_keyboard_button_text" format="string" />
        <attr name="lp_keyboard_button_image" format="integer" />
        <attr name="lp_keyboard_button_ripple_enabled" format="boolean" />
        <attr name="lp_pin_forgot_dialog_title" format="string" />
        <attr name="lp_pin_forgot_dialog_content" format="string" />
        <attr name="lp_pin_forgot_dialog_positive" format="string" />
        <attr name="lp_pin_forgot_dialog_negative" format="string" />
    </declare-styleable>

    <declare-styleable name="NumericKeyboard">
        <attr name="field" format="reference" />
        <attr name="fieldMaxLength" format="integer" />

        <attr name="keyHeight" format="dimension" />
        <attr name="keyTextSize" format="dimension" />
        <attr name="keyTextColor" format="color" />

        <attr name="keySpecial" format="string" />
    </declare-styleable>


    <declare-styleable name="ExpandableLayout">
        <attr name="expDuration" format="integer|reference" />
        <attr name="expWithParentScroll" format="boolean" />
        <attr name="expExpandScrollTogether" format="boolean" />
    </declare-styleable>

    <declare-styleable name="TextViewMo">
        <attr name="textTitle" format="string" />
        <attr name="textStyle" format="integer" />
    </declare-styleable>

    <declare-styleable name="TextViewTagMenu">
        <attr name="textTitleTagMenu" format="string" />
        <attr name="bgTagStyle" format="integer" />
    </declare-styleable>


    <declare-styleable name="CustomButton">
        <attr name="android:layout_height" />
        <attr name="android:layout_width" />
        <attr name="android:padding" />
        <attr name="buttonText" format="string" />
        <attr name="imageSrc" format="reference" />
        <attr name="showIcon" format="boolean" />
        <attr name="iconHeight" format="dimension" />
        <attr name="iconWidth" format="dimension" />
        <!--        <attr name="textSize" format="enum">-->
        <!--            <enum name="DEFAULT" value="0" />-->
        <!--            <enum name="MINI" value="1" />-->
        <!--            <enum name="LARGE" value="2" />-->
        <!--        </attr>-->
    </declare-styleable>
    <declare-styleable name="AppWidgetAttrs">
        <attr name="appWidgetPadding" format="dimension" />
        <attr name="appWidgetInnerRadius" format="dimension" />
        <attr name="appWidgetRadius" format="dimension" />
    </declare-styleable>
    <declare-styleable name="CardViewLifestyle">
        <attr name="style" format="enum">
            <enum name="DEFAULT" value="0" />
            <enum name="EXPEDITION" value="1" />
        </attr>
    </declare-styleable>
    <declare-styleable name="MenuBannerCategoryView">
        <attr name="iconBanner" format="integer"/>
        <attr name="textTitleBanner" format="string"/>
        <attr name="textDescriptionBanner" format="string"/>
        <attr name="textButtonBanner" format="string"/>
        <attr name="android:layout_height"/>
        <attr name="android:layout_width"/>
    </declare-styleable>


    <declare-styleable name="ToolbarRevamp">
        <attr name="toolbarNavBackgroundColor" format="reference" />
        <attr name="toolbarNavigationIcon" format="reference" />
        <attr name="toolbarNavigationIconColor" format="reference" />
        <attr name="toolbarNavigationEndIcon" format="reference" />
        <attr name="toolbarNavigationEndIconColor" format="reference" />
        <attr name="toolbarNavigationCanBack" format="boolean" />
        <attr name="toolbarSubTitleFontStyle" format="reference" />
        <attr name="toolbarTitleFontStyle" format="reference" />
        <attr name="toolbarSubTitleColor" format="reference" />
        <attr name="toolbarTitleColor" format="reference" />
        <attr name="toolbarIsSubtitleCentered" format="boolean" />
        <attr name="toolbarIsTitleCentered" format="boolean" />
        <attr name="toolbarSubTitle" format="string" />
        <attr name="toolbarTitle" format="string" />
        <attr name="toolbarOutlineProvider" format="boolean" />
    </declare-styleable>

    <declare-styleable name="InputPinView">
        <attr name="header_label" format="string"/>
    </declare-styleable>

    <declare-styleable name="CustomButtonPrimaryStyle">
        <attr name="customBackground" format="reference|color"/>
        <attr name="customTextColor" format="reference|color"/>
        <attr name="customTextSize" format="dimension"/>
        <attr name="customTextAllCaps" format="boolean"/>
        <attr name="android:fontFamily"/>
        <attr name="customTextStyle" format="integer">
            <enum name="normal" value="0" />
            <enum name="bold" value="1" />
            <enum name="italic" value="2" />
        </attr>
        <attr name="customElevation" format="dimension"/>
        <attr name="customStateListAnimator" format="reference"/>
    </declare-styleable>

    <declare-styleable name="CustomSwitchButton">
        <attr name="android:layout_height"/>
        <attr name="android:layout_width"/>
    </declare-styleable>

    <declare-styleable name="CurrencyEditText">
        <attr name="currencyPrefix" format="string" />
        <attr name="showCurrencySymbol" format="boolean"/>
        <attr name="decimalDigits" format="integer"/>
        <attr name="allowNegativeValue" format="boolean"/>
        <attr name="allowCopyPaste" format="boolean"/>
    </declare-styleable>

</resources>