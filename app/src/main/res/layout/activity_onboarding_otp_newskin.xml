<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_new_skin_activity_container"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.newskinonboarding.OnboardingOtpNewSkinActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:background="@drawable/bg_new_skin_activity">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:orientation="vertical"
            android:weightSum="1">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="0dp"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/tv_desc_otp"
                    style="@style/BodyText.Large.Regular.BlackNs600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:layout_marginTop="@dimen/space_x3"
                    android:textAlignment="center"
                    tools:text="@tools:sample/lorem[10]" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_box"
                    android:layout_width="@dimen/_280sdp"
                    android:layout_height="@dimen/space_x7"
                    android:layout_gravity="center_horizontal"
                    android:layout_marginTop="@dimen/space_x5"
                    android:orientation="vertical"
                    android:overScrollMode="never"
                    tools:itemCount="6"
                    tools:layoutManager="GridLayoutManager"
                    tools:listitem="@layout/otp_revamp"
                    tools:spanCount="6" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="@dimen/space_x7_half"
                    android:gravity="center_horizontal"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/tv_resend_otp"
                        style="@style/TitleText.Small.SemiBold.BlackNs600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginHorizontal="@dimen/space_half"
                        android:gravity="center"
                        android:text="@string/kirim_ulang_kode" />

                    <TextView
                        android:id="@+id/tv_timer"
                        style="@style/TitleText.Small.SemiBold.BlackNs600"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@string/time00_00"
                        android:textAlignment="center" />
                </LinearLayout>

                <TextView
                    android:id="@+id/tv_counter_otp"
                    style="@style/BodyText.Medium.Regular.BlackNs600"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginHorizontal="@dimen/space_half"
                    android:layout_marginTop="@dimen/space_x1"
                    android:gravity="center"
                    android:text="Tersisa 1 dari 3 kesempatan"
                    android:visibility="gone" />
            </LinearLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/rv_input_otp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_centerInParent="true"
                    android:background="#D0D5DC"
                    android:orientation="vertical"
                    android:paddingHorizontal="10dp"
                    android:paddingVertical="8dp"
                    tools:itemCount="12"
                    tools:layoutManager="GridLayoutManager"
                    tools:listitem="@layout/otp_number_newskin"
                    tools:spanCount="3" />
            </RelativeLayout>
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>