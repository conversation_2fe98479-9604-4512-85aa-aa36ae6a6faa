<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_switch"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/space_x2"
        android:paddingVertical="@dimen/space_x2"
        app:layout_constraintBottom_toTopOf="@id/view"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_name"
            style="@style/Body3SmallText.Medium.NeutralDark40"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:gravity="center_vertical"
            android:layout_marginTop="@dimen/size_2dp"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/iv_status"
                android:layout_width="@dimen/space_x1"
                android:layout_height="@dimen/space_x1"
                android:src="@drawable/tab_indicator_green" />

            <TextView
                android:id="@+id/tv_sub_title"
                style="@style/Caption1SmallText.Medium.NeutralDark20"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/space_half" />
        </LinearLayout>
    </LinearLayout>

    <Switch
        android:id="@+id/sw_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/space_x2"
        android:paddingEnd="@dimen/space_x1_half"
        android:thumb="@drawable/tumb_selector"
        android:track="@drawable/track_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_1dp"
        android:background="@color/neutralLight20"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>