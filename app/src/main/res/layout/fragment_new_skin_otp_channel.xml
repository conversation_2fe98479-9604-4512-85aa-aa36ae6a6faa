<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flRoot"
    style="@style/CustomBottomSheetDialogTheme"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_dialog_newskin"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="@dimen/size_8dp">

        <FrameLayout
            android:id="@+id/fl_pill_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_16dp">

            <View
                android:id="@+id/v_pill"
                android:layout_width="@dimen/size_60dp"
                android:layout_height="@dimen/size_4dp"
                android:layout_gravity="center"
                android:background="@drawable/bottom_sheet_pill_bar" />

        </FrameLayout>

        <LinearLayout
            android:id="@+id/fl_title_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:padding="@dimen/size_16dp">

            <ImageView
                android:id="@+id/iv_title_left"
                android:layout_width="@dimen/size_32dp"
                android:layout_height="@dimen/size_32dp"
                android:contentDescription="@null"
                android:visibility="invisible" />

            <TextView
                android:id="@+id/tv_title"
                style="@style/TitleText.Small.SemiBold.BlackNsMain"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginHorizontal="@dimen/size_12dp"
                android:layout_marginVertical="@dimen/size_4dp"
                android:layout_weight="1"
                android:gravity="center_horizontal"
                android:text="Pilih Metode Terima OTP" />

            <ImageView
                android:id="@+id/iv_title_right"
                android:layout_width="@dimen/size_32dp"
                android:layout_height="@dimen/size_32dp"
                android:contentDescription="@null"
                android:src="@drawable/ic_close_rounded_new_skin"
                android:visibility="visible" />

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tv_msg"
            style="@style/BodyText.Medium.Regular.BlackNs600"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginTop="@dimen/size_16dp"
            android:text="Kami akan mengirim 6 digit kode OTP ke +6281233948797 melalui metode yang dipilih."
            android:textAlignment="center" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rvLanguagesList"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingBottom="@dimen/size_8dp"
            app:layout_constraintBottom_toTopOf="@id/btn_pilih"
            app:layout_constraintTop_toBottomOf="@id/tv_msg"
            tools:itemCount="3"
            tools:listitem="@layout/item_languages_list" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_pilih"
            style="@style/ButtonPrimaryNewSkin"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_56dp"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:layout_marginTop="@dimen/size_16dp"
            android:layout_marginBottom="@dimen/space_x3"
            android:enabled="false"
            android:text="@string/kirim_kode_otp" />

    </LinearLayout>

</FrameLayout>