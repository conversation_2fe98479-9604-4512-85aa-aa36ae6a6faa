<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/background_color"
    tools:context="id.co.bri.brimo.ui.activities.bukarekening.TabunganActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_revamp" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar"
        android:layout_marginBottom="@dimen/margin_from_bottom_layout"
        android:orientation="vertical"
        android:weightSum="2">

        <View
            android:id="@+id/viewBackground"
            android:layout_width="match_parent"
            android:layout_height="383dp"
            android:layout_marginTop="-85dp"
            android:background="@drawable/bg_atas_revamp_new" />

        <RelativeLayout
            android:id="@+id/rlCard"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <id.co.bri.brimo.ui.customviews.NonSwipeableViewPagerOnOff
                android:id="@+id/vp_tabungan"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_above="@+id/dots_indicator"
                android:layout_marginTop="@dimen/space_x1_half" />

            <com.tbuonomo.viewpagerdotsindicator.DotsIndicator
                android:id="@+id/dots_indicator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginTop="18dp"
                app:dotsColor="#88BFEC"
                app:dotsCornerRadius="8dp"
                app:dotsSize="10dp"
                app:dotsSpacing="4dp"
                app:dotsWidthFactor="2.5"
                app:progressMode="false"
                app:selectedDotColor="@color/primary_blue80" />

        </RelativeLayout>


    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/background_color"
        android:orientation="vertical"
        android:visibility="visible">

        <Button
            android:id="@+id/btn_rekening_baru"
            style="@style/BodyMediumText.Bold.White"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x6"
            android:layout_margin="@dimen/space_x2"
            android:background="@drawable/rounded_button_blue"
            android:text="@string/register_pilih_tabungan"
            android:textAllCaps="false"/>

    </LinearLayout>

</RelativeLayout>