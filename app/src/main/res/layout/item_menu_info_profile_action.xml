<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/cl_action"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <ImageView
        android:id="@+id/iv_info_profile"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/space_x1_half"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideLine1"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideLine1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="62dp" />

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Body3SmallText.Medium.NeutralDark40"
        android:textColor="#181C21"
        android:paddingStart="@dimen/space_x2"
        android:paddingVertical="@dimen/space_x1_half"
        app:layout_constraintStart_toEndOf="@+id/guideLine1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/view"/>

    <ImageView
        android:id="@+id/iv_arrow"
        android:layout_width="16dp"
        android:layout_height="16dp"
        android:src="@drawable/ic_arrow_right_blue"
        android:layout_marginVertical="@dimen/space_x1_half"
        android:layout_marginEnd="@dimen/space_x1_half"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"/>

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_1dp"
        android:background="@color/neutralLight20"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>