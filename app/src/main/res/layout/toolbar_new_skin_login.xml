<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tb_login"
    android:layout_width="match_parent"
    android:layout_height="?actionBarSize"
    android:paddingHorizontal="@dimen/size_16dp"
    android:paddingVertical="@dimen/size_10dp">

    <ImageView
        android:layout_width="78dp"
        android:layout_height="@dimen/size_40dp"
        android:layout_alignParentStart="true"
        android:layout_centerVertical="true"
        android:background="@drawable/logo_qitta"
        android:contentDescription="@null" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_alignParentEnd="true"
        android:gravity="center"
        android:orientation="horizontal">

        <ImageView
            android:id="@+id/iv_customer_care"
            android:layout_width="@dimen/size_40dp"
            android:layout_height="@dimen/size_40dp"
            android:layout_marginEnd="@dimen/space_x1_half"
            android:padding="10dp"
            android:visibility="gone"
            android:background="@drawable/bg_custom_switch"
            android:src="@drawable/ic_customer_care" />

        <id.co.bri.brimo.ui.customviews.switchbutton.SwitchLanguageButtonView
            android:id="@+id/switch_button"
            android:layout_width="72dp"
            android:layout_height="40dp"
            android:elevation="1dp" />
    </LinearLayout>
</RelativeLayout>