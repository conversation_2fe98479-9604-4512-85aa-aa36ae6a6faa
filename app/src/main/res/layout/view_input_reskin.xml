<?xml version="1.0" encoding="utf-8"?>
<merge xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:id="@+id/rl_content"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">
            <com.google.android.material.textfield.TextInputLayout
                android:id="@+id/textInputLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:minHeight="64dp"
                app:hintTextColor="@color/text_black_default_ns"
                app:boxStrokeWidth="0dp"
                app:boxStrokeWidthFocused="0dp"
                android:gravity="center_vertical"
                app:boxCollapsedPaddingTop="8dp"
                app:prefixTextColor="@color/black_ns_400"
                android:background="@drawable/selector_input_field_ns"
                app:cursorColor="@color/text_black_default_ns"
                tools:hint="Nama Tersimpan"
                android:paddingHorizontal="16dp">
                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/etInput"
                    android:paddingStart="0dp"
                    android:paddingEnd="0dp"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="textCapCharacters"
                    android:digits="@string/string_allowed_strip"
                    android:background="@android:color/transparent"
                    android:maxLines="1" />
            </com.google.android.material.textfield.TextInputLayout>

            <LinearLayout
                android:id="@+id/endIconContainer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignEnd="@id/textInputLayout"
                android:layout_centerVertical="true"
                android:orientation="horizontal"
                android:gravity="center"/>
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_error"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:textColor="@color/error80"
            android:layout_below="@+id/rl_content"
            android:visibility="gone"
            tools:text="Error" />
    </RelativeLayout>
</merge>
