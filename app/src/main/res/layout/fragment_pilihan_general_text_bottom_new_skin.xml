<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_dialog_newskin"
    tools:context=".ui.fragments.onboarding.PilihanGeneralTextBottomFragment">

    <LinearLayout
        android:id="@+id/layoutPilihan"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:paddingBottom="@dimen/space_x2">

        <View
            android:layout_width="@dimen/space_x5"
            android:layout_height="@dimen/size_6dp"
            android:layout_marginTop="@dimen/space_x1"
            android:layout_marginBottom="@dimen/space_x2"
            android:background="@color/neutral_light20"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tvTitle"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:textSize="16dp"
            android:fontFamily="@font/bri_digital_text_semi_bold"
            android:text="Kode Telepon Negara"
            android:textColor="@color/black"
            android:layout_marginTop="28dp"
            android:textAlignment="center" />

        <View
            android:id="@+id/vw_divider"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:layout_marginBottom="@dimen/space_x2"
            android:background="@color/neutralLight20"
            android:visibility="gone"/>

        <androidx.appcompat.widget.SearchView
            android:id="@+id/searchview"
            android:layout_width="match_parent"
            android:layout_height="44dp"
            android:layout_marginHorizontal="24dp"
            android:layout_marginBottom="@dimen/space_x2"
            android:background="@drawable/rounded_dialog_grey_newskin"
            android:inputType="text|textNoSuggestions|textFilter"
            android:visibility="visible"
            app:iconifiedByDefault="false"
            app:queryBackground="@color/transparent"
            app:queryHint="@string/empty"
            android:layout_marginTop="28dp"
            app:searchIcon="@drawable/ic_search_outline_20dp_newskin" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recylerview"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>
</FrameLayout>