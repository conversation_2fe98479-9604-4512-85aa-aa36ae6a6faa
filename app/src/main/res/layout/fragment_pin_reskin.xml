<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
		xmlns:app="http://schemas.android.com/apk/res-auto"
		xmlns:tools="http://schemas.android.com/tools">
	<androidx.constraintlayout.widget.ConstraintLayout
			android:layout_width="match_parent"
			android:layout_height="match_parent"
			android:background="@color/white">

		<RelativeLayout
				android:id="@+id/rlToolbar"
				android:layout_height="80dp"
				android:layout_width="0dp"
				app:layout_constraintEnd_toEndOf="parent"
				app:layout_constraintStart_toStartOf="parent"
				app:layout_constraintTop_toTopOf="parent">

			<LinearLayout
					android:id="@+id/ll_back"
					android:layout_width="32dp"
					android:layout_height="32dp"
					android:layout_marginStart="16dp"
					android:layout_marginTop="24dp"
					android:background="@drawable/rounded_dialog_grey_newskin"
					android:gravity="center"
					android:orientation="vertical">

				<ImageView
						android:layout_width="20dp"
						android:layout_height="20dp"
						android:src="@drawable/ic_left_ns" />
			</LinearLayout>

			<TextView
					android:layout_width="wrap_content"
					android:layout_height="wrap_content"
					android:fontFamily="@font/bri_digital_text_semi_bold"
					android:text="PIN"
					android:textSize="20sp"
					android:layout_centerInParent="true"
					android:textColor="@color/ns_greytheme" />

		</RelativeLayout>

		<androidx.constraintlayout.widget.ConstraintLayout
				android:layout_width="0dp"
				android:layout_height="0dp"
				app:layout_constraintTop_toBottomOf="@+id/rlToolbar"
				app:layout_constraintStart_toStartOf="parent"
				app:layout_constraintEnd_toEndOf="parent"
				app:layout_constraintBottom_toBottomOf="parent">

			<RelativeLayout
					android:id="@+id/rl_pin"
					android:layout_width="0dp"
					android:layout_height="wrap_content"
					app:layout_constraintTop_toTopOf="parent"
					app:layout_constraintStart_toStartOf="parent"
					app:layout_constraintEnd_toEndOf="parent"
					android:orientation="vertical">

				<TextView
						android:id="@+id/tv_deksripsi"
						android:textColor="@color/ns_greytheme"
						android:fontFamily="@font/bri_digital_text_semibold"
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:layout_marginLeft="@dimen/_16sdp"
						android:layout_centerHorizontal="true"
						android:layout_marginTop="@dimen/space_x3"
						android:textSize="16dp"
						android:layout_marginRight="@dimen/_16sdp"
						android:text="Masukkan PIN" />

				<RelativeLayout
						android:layout_width="wrap_content"
						android:layout_height="wrap_content"
						android:layout_marginTop="24dp"
						android:layout_below="@id/tv_deksripsi">

					<LinearLayout
							android:layout_width="match_parent"
							android:layout_height="wrap_content"
							android:layout_centerInParent="true"
							android:orientation="vertical">

						<RelativeLayout
								android:layout_width="match_parent"
								android:layout_height="match_parent">
							<androidx.recyclerview.widget.RecyclerView
									android:id="@+id/rv_box"
									android:layout_width="wrap_content"
									android:layout_height="16dp"
									android:layout_centerInParent="true"
									tools:listitem="@layout/pin_layout_newskin"
									tools:itemCount="6"
									android:overScrollMode="never" />
						</RelativeLayout>

						<TextView
								android:id="@+id/tvError"
								android:fontFamily="@font/bri_digital_text_regular"
								android:textColor="@color/ns_red"
								android:layout_width="256dp"
								android:layout_marginTop="24dp"
								android:layout_height="wrap_content"
								android:layout_gravity="center_horizontal"
								android:gravity="center_horizontal"
								android:textSize="14sp"
								android:visibility="gone"
								tools:text="@string/lupa_pin_normal"/>

						<TextView
								android:id="@+id/tv_lupa_pin"
								android:fontFamily="@font/bri_digital_text_semibold"
								android:textColor="@color/ns_primary500"
								android:layout_width="wrap_content"
								android:layout_marginTop="24dp"
								android:layout_height="wrap_content"
								android:layout_gravity="center_horizontal"
								android:gravity="center_horizontal"
								android:textSize="14sp"
								android:text="@string/lupa_pin_normal"/>
					</LinearLayout>
				</RelativeLayout>
			</RelativeLayout>

			<RelativeLayout
					android:layout_width="0dp"
					android:layout_height="0dp"
					app:layout_constraintTop_toBottomOf="@+id/rl_pin"
					app:layout_constraintStart_toStartOf="parent"
					app:layout_constraintEnd_toEndOf="parent"
					app:layout_constraintBottom_toBottomOf="parent">

				<androidx.recyclerview.widget.RecyclerView
						android:id="@+id/rv_input"
						android:layout_width="match_parent"
						android:layout_height="wrap_content"
						tools:listitem="@layout/pin_layout_newskin"
						android:layout_alignParentBottom="true"/>

			</RelativeLayout>
		</androidx.constraintlayout.widget.ConstraintLayout>

	</androidx.constraintlayout.widget.ConstraintLayout>
</layout>