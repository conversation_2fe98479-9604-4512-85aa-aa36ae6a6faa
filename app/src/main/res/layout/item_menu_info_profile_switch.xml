<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cl_switch"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <ImageView
        android:id="@+id/iv_info_profile"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_marginStart="@dimen/space_x1_half"
        android:src="@drawable/ic_info_brimo"
        android:scaleType="fitXY"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/guideLine1"/>

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/guideLine1"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintGuide_begin="1dp"
        app:layout_constraintGuide_percent="0.15"/>

    <TextView
        android:id="@+id/tv_name"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/Body3SmallText.Medium.NeutralDark40"
        android:textColor="#181C21"
        android:layout_marginStart="@dimen/space_x1_half"
        android:paddingStart="@dimen/space_x2"
        android:paddingVertical="@dimen/space_x1_half"
        app:layout_constraintStart_toEndOf="@+id/guideLine1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/view"/>

    <Switch
        android:id="@+id/sw_action"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/space_x2"
        android:paddingEnd="@dimen/space_x1_half"
        android:thumb="@drawable/tumb_selector"
        android:track="@drawable/track_selector"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="UseSwitchCompatOrMaterialXml" />

    <View
        android:id="@+id/view"
        android:layout_width="match_parent"
        android:layout_height="@dimen/size_1dp"
        android:background="@color/neutralLight20"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>