<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal"
    android:layout_marginBottom="@dimen/space_x1_half"
    android:weightSum="2">

    <TextView
        android:id="@+id/tv_detail_field"
        style="@style/Body3SmallText.Medium.NeutralDark10"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_weight="1.2"
        android:lineSpacingExtra="4sp"
        android:text="@string/empty" />

    <TextView
        android:id="@+id/tv_detail_value"
        style="@style/Body3SmallText.Medium.NeutralDark40"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentRight="true"
        android:layout_weight="0.8"
        android:lineSpacingExtra="4sp"
        android:text="@string/empty"
        android:textAlignment="viewEnd"/>
</LinearLayout>