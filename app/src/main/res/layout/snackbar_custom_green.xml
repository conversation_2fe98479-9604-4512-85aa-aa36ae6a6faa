<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
		android:orientation="horizontal"
		android:gravity="center_vertical"
		android:layout_width="match_parent"
		android:layout_height="wrap_content"
		android:padding="16dp">
	<ImageView
			android:id="@+id/snackbar_icon"
			android:layout_width="20dp"
			android:layout_height="20dp"
			android:layout_marginEnd="8dp"/>
	<TextView
			android:id="@+id/snackbar_text"
			android:layout_width="0dp"
			android:layout_height="wrap_content"
			android:layout_weight="1"
			android:textColor="@android:color/white"
			android:textSize="14sp"/>
</LinearLayout>