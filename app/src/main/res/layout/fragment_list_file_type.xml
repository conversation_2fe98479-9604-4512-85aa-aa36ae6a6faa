<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:background="@drawable/rounded_dialog">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/space_x1"
        android:paddingBottom="@dimen/space_x2">
        <View
            android:layout_width="@dimen/space_x6"
            android:layout_height="@dimen/space_half"
            android:background="@color/accent3Color"
            android:layout_gravity="center"
            android:layout_marginBottom="@dimen/space_x2"/>

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:fontFamily="@font/bri_text_semibold"
            android:textSize="18dp"
            android:textColor="@color/blackColor"
            android:text="@string/file_type"
            android:gravity="center"
            android:layout_marginBottom="@dimen/space_x2"/>

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_list_file_type"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingHorizontal="@dimen/space_x2"/>

    </LinearLayout>
</FrameLayout>