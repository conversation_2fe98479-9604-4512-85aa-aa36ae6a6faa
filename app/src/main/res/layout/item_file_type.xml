<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/ll_file_type"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:background="@drawable/bg_white_border_circle"
    android:padding="@dimen/space_x2"
    android:layout_marginBottom="@dimen/space_x2">

    <TextView
        android:id="@+id/tv_file_type_name"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/bri_text_bold"
        android:textColor="@color/neutralDark40"
        android:textSize="18dp"
        tools:text="@string/empty" />
</LinearLayout>