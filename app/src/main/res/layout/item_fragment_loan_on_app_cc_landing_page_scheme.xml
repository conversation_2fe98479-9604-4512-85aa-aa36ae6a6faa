<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:paddingVertical="@dimen/space_x2">

    <TextView
        android:id="@+id/tvItemFragmentLoanOnAppCcLandingPageSchemeTenor"
        style="@style/Body3SmallText.SemiBold.NeutralBaseBlack"
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="1"
        android:gravity="center"
        tools:text="3 Bulan" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center">

        <TextView
            android:id="@+id/tvItemFragmentLoanOnAppCcLandingPageSchemePercent"
            style="@style/Caption1SmallText.SemiBold.PrimaryBlue80"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:background="@drawable/bg_rounded_primary_blue_10"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_x2"
            android:paddingVertical="@dimen/space_x1"
            tools:text="0%" />

    </LinearLayout>

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:gravity="center"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tvItemFragmentLoanOnAppCcLandingPageSchemeMinimalInfo"
            style="@style/Caption1SmallText.Medium.NeutralLight80"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            tools:text="1% atau minimal" />

        <TextView
            android:id="@+id/tvItemFragmentLoanOnAppCcLandingPageSchemeMinimalNominalTransaction"
            style="@style/Body3SmallText.Bold.NeutralBaseBlack"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            tools:text="Rp200,000" />
    </LinearLayout>
</LinearLayout>