<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context=".ui.activities.RiplayActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_above="@id/ly_bottom"
        android:layout_below="@+id/toolbar"
        android:scrollbarAlwaysDrawVerticalTrack="true">

        <ScrollView
            android:id="@+id/scrollview"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <WebView
                android:id="@+id/wv_content"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </ScrollView>

        <ImageView
            android:id="@+id/img_skip"
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:contentDescription="img_skip"
            android:src="@drawable/icon_circle_arrow"
            android:visibility="gone"
            app:layout_anchor="@id/scrollview"
            app:layout_anchorGravity="bottom|center" />
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/ly_bottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:orientation="vertical"
        android:paddingHorizontal="@dimen/space_x2"
        android:paddingVertical="@dimen/space_x1_half">

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_setuju"
            style="@style/ButtonPrimaryRevamp"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x1_half"
            android:paddingHorizontal="@dimen/space_x1_half"
            android:paddingVertical="@dimen/space_x1_half"
            android:text="@string/setuju"
            tools:ignore="RelativeOverlap" />

        <TextView
            android:id="@+id/tv_batal"
            style="@style/Body2MediumText.Bold.PrimaryBlue80"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/space_x1_half"
            android:gravity="center"
            android:paddingHorizontal="@dimen/space_x1_half"
            android:paddingVertical="@dimen/space_x1_half"
            android:text="@string/batal2"
            tools:ignore="RelativeOverlap" />
    </LinearLayout>
</RelativeLayout>