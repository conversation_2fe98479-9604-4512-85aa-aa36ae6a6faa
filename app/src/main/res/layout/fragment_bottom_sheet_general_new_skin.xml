<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/flRoot"
    style="@style/CustomBottomSheetDialogTheme"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/rounded_dialog_newskin"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingBottom="@dimen/space_x2_half">


        <FrameLayout
            android:id="@+id/fl_pill_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:padding="@dimen/size_16dp">

            <View
                android:id="@+id/v_pill"
                android:layout_width="@dimen/size_60dp"
                android:layout_height="@dimen/size_4dp"
                android:layout_gravity="center"
                android:background="@drawable/bottom_sheet_pill_bar" />

        </FrameLayout>

        <ImageView
            android:id="@+id/iv_close"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_below="@id/fl_pill_container"
            android:layout_alignParentEnd="true"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="8dp"
            android:src="@drawable/ic_close_rounded_new_skin"
            android:contentDescription="@string/close"
            android:background="?attr/selectableItemBackgroundBorderless"
            android:padding="4dp" />

        <RelativeLayout
            android:id="@+id/layout_image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_below="@id/iv_close"
            android:layout_marginTop="@dimen/size_16dp"
            android:layout_centerHorizontal="true">

            <ImageView
                android:id="@+id/iv_center"
                android:layout_width="@dimen/_200sdp"
                android:layout_height="@dimen/_200sdp"
                android:layout_centerHorizontal="true"
                android:src="@drawable/ic_fail_newskin" />
        </RelativeLayout>

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/layout_image"
            android:gravity="center_horizontal"
            style="@style/TitleText.Small.SemiBold.NeutralBaseBlack"
            android:layout_marginTop="@dimen/space_x1"
            tools:text="Perubahan Gagal Disimpan"/>

        <TextView
            android:id="@+id/tv_desc"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/tv_title"
            android:layout_gravity="center"
            android:layout_marginTop="@dimen/space_x1"
            android:lineSpacingExtra="@dimen/space_half"
            android:layout_marginHorizontal="@dimen/space_x2"
            style="@style/BodyText.Large.Regular.BlackNs600"
            android:gravity="center"
            tools:text="Silahkan coba beberapa saat lagi"
            android:layout_marginBottom="@dimen/space_x3" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/first_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_48sdp"
            style="@style/ButtonPrimaryNewSkin"
            android:layout_below="@id/tv_desc"
            tools:text="Coba Lagi"
            android:layout_marginTop="@dimen/space_x3"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:textAllCaps="false" />

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/second_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/_48sdp"
            style="@style/ButtonPrimaryBorderNewSkin"
            android:layout_below="@id/first_btn"
            android:layout_marginTop="@dimen/space_x1"
            tools:text="Lupa Password"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:textAllCaps="false"/>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/third_btn"
            android:layout_width="match_parent"
            android:layout_height="@dimen/space_x6"
            style="@style/ButtonPrimary.Outline"
            android:layout_below="@id/first_btn"
            android:backgroundTint="@color/white"
            android:layout_marginTop="@dimen/space_x1"
            android:layout_marginHorizontal="@dimen/space_x2"
            android:textAllCaps="false" />
    </RelativeLayout>
</FrameLayout>