<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    tools:context="id.co.bri.brimo.ui.activities.TapBrizziAktivasiActivity">

    <include
        android:id="@+id/tb_briva"
        layout="@layout/toolbar" />

    <LinearLayout
        android:id="@+id/linear"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/tb_briva"
        android:orientation="vertical"
        android:background="@color/white"
        android:weightSum="2">

        <androidx.coordinatorlayout.widget.CoordinatorLayout
            android:id="@+id/content"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1.5"
            >

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="vertical"
                android:layout_centerInParent="true">

                <pl.droidsonroids.gif.GifImageView
                    android:id="@+id/img_cek"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:layout_marginTop="50dp"
                    android:layout_marginStart="30dp"
                    android:layout_marginEnd="30dp"
                    android:src="@drawable/cek_brizzi"
                    android:layout_centerHorizontal="true"
                    />

            </RelativeLayout>



        </androidx.coordinatorlayout.widget.CoordinatorLayout>


        <RelativeLayout
            android:id="@+id/rl_bawah"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="0.5">
            <TextView
                android:id="@+id/mainLabel"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/avenir_next_medium"
                android:textSize="@dimen/size_default_14sp"
                android:layout_marginStart="29dp"
                android:layout_marginEnd="29dp"
                android:textColor="#202020"
                android:layout_centerHorizontal="true"
                android:gravity="center_horizontal"
                android:text="@string/brizzi_tap_keterangan"
                />
        </RelativeLayout>
    </LinearLayout>



</RelativeLayout>