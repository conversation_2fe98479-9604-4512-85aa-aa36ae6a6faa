<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    tools:context="id.co.bri.brimo.ui.activities.DashboardIBActivity">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_above="@id/navigation"
                android:layout_marginBottom="@dimen/space_half">

                <FrameLayout
                    android:id="@+id/fragmentDashboardIB"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </RelativeLayout>


            <com.google.android.material.bottomnavigation.BottomNavigationView
                android:id="@+id/navigation"
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:layout_alignParentBottom="true"
                android:background="@android:color/white"
                app:elevation="0dp"
                app:itemBackground="@android:color/white"
                app:itemTextColor="@color/bnv_tab_item_foreground"
                app:itemIconTint="@color/bnv_tab_item_foreground"
                app:labelVisibilityMode="labeled"
                app:layout_behavior="@string/hide_bottom_view_on_scroll_behavior"
                app:menu="@menu/navigation" />

            <View
                android:id="@+id/view"
                android:layout_width="match_parent"
                android:layout_height="@dimen/space_x2"
                android:layout_above="@id/navigation"
                android:background="@drawable/shadow_bottom_nav"
                android:visibility="gone"/>

            <ImageView
                android:id="@+id/iv_scan"
                android:layout_width="56dp"
                android:layout_height="56dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:layout_marginBottom="23dp"
                android:contentDescription="@string/bottom_sheet_behavior"
                android:src="@drawable/ic_qris_revamp"
                android:background="@drawable/navbar_bottom_circle"
                android:translationZ="0dp" />

        </RelativeLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>
