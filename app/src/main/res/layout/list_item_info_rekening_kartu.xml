<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/cl_currency_sof"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginHorizontal="@dimen/space_x2"
    android:layout_marginVertical="@dimen/space_x1">

    <TextView
        style="@style/Body3SmallText.Medium.NeutralLight80"
        android:id="@+id/tv_title"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:text="@string/empty"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintEnd_toStartOf="@+id/cl_second_data"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_second_data"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintStart_toEndOf="@+id/tv_title"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/iv_currency"
            android:layout_width="@dimen/space_x2"
            android:layout_height="@dimen/space_x2"
            android:scaleType="fitXY"
            android:src="@drawable/currency_idr"
            android:layout_marginRight="@dimen/space_half"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/tv_desc"
            app:layout_constraintTop_toTopOf="parent"
            android:visibility="gone"/>

        <TextView
            android:id="@+id/tv_desc"
            style="@style/Body3SmallText.Medium.NeutralDark40"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/empty"
            android:ellipsize="end"
            android:maxLines="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
