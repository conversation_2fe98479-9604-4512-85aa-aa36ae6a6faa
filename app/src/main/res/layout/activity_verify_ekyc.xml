<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/bg_new_skin_activity_container"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.newskinonboarding.OnboardingVerifyEKYCActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar_new_skin" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_above="@id/layoutButton"
        android:background="@drawable/bg_new_skin_activity"
        android:layout_below="@id/toolbar">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_margin="@dimen/space_x2"
            android:orientation="vertical">

            <ImageView
                android:layout_width="@dimen/space_x15"
                android:layout_height="@dimen/space_x15"
                android:layout_gravity="center"
                android:contentDescription="image"
                android:src="@drawable/ic_smile" />

            <TextView
                android:id="@+id/tv_desc_panduan"
                style="@style/BodyText.Large.Regular.BlackNs600"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/space_x2"
                android:lineSpacingExtra="@dimen/_4sdp"
                android:text="@string/desc_panduan_liveness" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/Body3SmallText.SemiBold.NeutralDark30"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness1" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/ns_graysoft" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/Body3SmallText.SemiBold.NeutralDark30"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness2" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/ns_graysoft" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/Body3SmallText.SemiBold.NeutralDark30"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness3" />
            </LinearLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="1dp"
                android:background="@color/ns_graysoft" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginVertical="@dimen/space_x2"
                android:gravity="center_vertical"
                android:orientation="horizontal">

                <ImageView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:src="@drawable/ic_checklist_green" />

                <TextView
                    style="@style/Body3SmallText.SemiBold.NeutralDark30"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginHorizontal="@dimen/space_x2"
                    android:text="@string/info_panduan_liveness4" />
            </LinearLayout>
        </LinearLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <LinearLayout
        android:id="@+id/layoutButton"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:background="@color/white"
        android:orientation="vertical">

        <View
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_1dp"
            android:background="@color/black_ns_200"/>

        <androidx.appcompat.widget.AppCompatButton
            android:id="@+id/btn_lanjutkan"
            style="@style/ButtonPrimaryNewSkin"
            android:layout_width="match_parent"
            android:layout_height="@dimen/size_56dp"
            android:layout_margin="@dimen/space_x2"
            android:text="@string/lanjutkan" />
    </LinearLayout>
</RelativeLayout>