<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:fitsSystemWindows="true"
    tools:context=".ui.activities.GeneralSyaratActivity">

    <include
        android:id="@+id/toolbar"
        layout="@layout/toolbar" />

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@id/toolbar">

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ScrollView
                android:id="@+id/scrollview"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:focusable="true"
                android:focusableInTouchMode="true"
                android:scrollbars="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:orientation="vertical">

                    <WebView
                        android:id="@+id/wv_syarat"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content" />

                    <Button
                        android:id="@+id/bt_setuju"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_margin="15dp"
                        android:background="@drawable/rounded_button_blue"
                        android:fontFamily="@font/avenir_next_bold"
                        android:text="@string/setuju"
                        android:textAllCaps="false"
                        android:textColor="@android:color/white"
                        android:textSize="16dp" />

                    <Button
                        android:id="@+id/bt_batal"
                        android:layout_width="match_parent"
                        android:layout_height="50dp"
                        android:layout_marginHorizontal="15dp"
                        android:layout_marginBottom="15dp"
                        android:background="@drawable/rounded_button"
                        android:fontFamily="@font/avenir_next_bold"
                        android:text="@string/batal2"
                        android:textAllCaps="false"
                        android:textColor="@color/colorTextBlueBri"
                        android:textSize="16dp" />
                </LinearLayout>
            </ScrollView>

            <Button
                android:id="@+id/bt_setuju_bawah"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_above="@id/bt_batal_bawah"
                android:layout_margin="15dp"
                android:background="@drawable/rounded_button_blue"
                android:fontFamily="@font/avenir_next_bold"
                android:text="@string/setuju"
                android:textAllCaps="false"
                android:textColor="@android:color/white"
                android:textSize="16dp"
                android:visibility="gone" />

            <Button
                android:id="@+id/bt_batal_bawah"
                android:layout_width="match_parent"
                android:layout_height="50dp"
                android:layout_alignParentBottom="true"
                android:layout_marginHorizontal="15dp"
                android:layout_marginBottom="15dp"
                android:background="@drawable/rounded_button"
                android:fontFamily="@font/avenir_next_bold"
                android:text="@string/batal2"
                android:textAllCaps="false"
                android:textColor="@color/colorTextBlueBri"
                android:textSize="16dp"
                android:visibility="gone" />

            <ImageView
                android:id="@+id/img_bawah"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:layout_alignParentBottom="true"
                android:layout_centerHorizontal="true"
                android:src="@drawable/icon_circle_arrow" />
        </RelativeLayout>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>
</RelativeLayout>