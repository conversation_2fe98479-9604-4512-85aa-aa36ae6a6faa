<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/colorButtonGrey" />

    <!--We have top and bottom margins on the TextView so there's more space between
     continuous selected rows. This means that setting an oval background on it when
     a single date is selected will result in a the background not being a perfect circle
     since the view is not square in shape.
     So we set the background on this view which has a margin on all sides instead-->

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="horizontal">


        <View
            android:id="@+id/leftGreyArea"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:background="@color/neutralLight20"/>

        <View
            android:id="@+id/rightGreyArea"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:layout_weight="1"
            android:layout_marginTop="4dp"
            android:layout_marginBottom="4dp"
            android:background="@color/neutralLight20"/>
    </LinearLayout>

    <View
        android:id="@+id/calendarDayView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="4dp" />

    <TextView
        android:id="@+id/tvCalendarDay"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:gravity="center"
        android:layout_gravity="center"
        android:layout_marginTop="4dp"
        android:layout_marginBottom="4dp"
        android:fontFamily="@font/bri_text_medium"
        android:textColor="@color/neutralLight40"
        android:background="@drawable/example_4_single_selected_bg"
        android:textSize="16dp"
        tools:text="22" />
</FrameLayout>
