<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/cv_list_rekening"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="@dimen/space_x1"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:id="@+id/ll_rekening"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingTop="@dimen/space_x2"
            android:paddingBottom="@dimen/space_x1">

            <FrameLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content">

                <ImageView
                    android:id="@+id/iv_rekening"
                    android:layout_width="@dimen/space_x16"
                    android:layout_height="@dimen/space_x16"
                    android:src="@drawable/kartu_brimo_no_logo" />

                <LinearLayout
                    android:id="@+id/ll_bi_fast"
                    android:layout_width="@dimen/space_x6_half"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/space_half"
                    android:layout_marginTop="@dimen/space_half"
                    android:background="@drawable/rounded_grey"
                    android:backgroundTint="@color/successColor"
                    android:visibility="gone">

                    <TextView
                        style="@style/BodySmallText.DemiBold.White"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_margin="@dimen/size_2dp"
                        android:text="@string/bi_fast"
                        android:textAlignment="center" />
                </LinearLayout>
            </FrameLayout>

            <RelativeLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="@dimen/space_x2"
                android:layout_marginTop="@dimen/size_2dp">

                <ImageView
                    android:id="@+id/iv_jenis_rekening"
                    android:layout_width="wrap_content"
                    android:layout_height="18dp"
                    android:layout_marginEnd="@dimen/size_5dp"
                    android:layout_marginBottom="@dimen/space_half"
                    android:src="@drawable/ic_kartu_kredit"
                    android:visibility="gone" />

                <RelativeLayout
                    android:id="@+id/top"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/iv_jenis_rekening"
                    android:layout_marginBottom="@dimen/space_half"
                    android:orientation="horizontal">

                    <ImageView
                        android:id="@+id/iv_rekening_default"
                        android:layout_width="@dimen/size_20dp"
                        android:layout_height="@dimen/size_18dp"
                        android:layout_marginEnd="@dimen/size_10dp"
                        android:src="@drawable/ic_star"
                        android:visibility="visible" />

                    <TextView
                        android:id="@+id/tv_detail_no_rekening"
                        style="@style/BodySmallText.DemiBold.Black"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_toEndOf="@id/iv_rekening_default"
                        android:text="@string/empty" />

                    <ImageView
                        android:id="@+id/icon_more"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_alignParentEnd="true"
                        android:src="@drawable/ic_more_vert_blue"
                        android:visibility="gone" />

                </RelativeLayout>

                <TextView
                    android:id="@+id/tv_nama_pemilik_rekening"
                    style="@style/BodySmallText.Medium.Black"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/top"
                    android:layout_marginBottom="@dimen/space_half"
                    android:text="@string/empty" />

                <RelativeLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_below="@id/tv_nama_pemilik_rekening"
                    android:layout_alignParentBottom="true">

                    <ImageView
                        android:id="@+id/iv_alert_saldo"
                        android:layout_width="@dimen/size_20dp"
                        android:layout_height="@dimen/size_20dp"
                        android:layout_above="@id/layout_detail"
                        android:layout_marginHorizontal="@dimen/space_x1"
                        android:layout_marginBottom="@dimen/space_half"
                        android:layout_toStartOf="@id/tv_rekening_saldo"
                        android:src="@drawable/ic_alert_error"
                        android:visibility="gone" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tv_rekening_saldo"
                        android:layout_width="wrap_content"
                        android:layout_height="@dimen/space_x3"
                        android:minWidth="@dimen/space_x5"
                        android:layout_above="@id/layout_detail"
                        android:layout_alignParentEnd="true"
                        android:fontFamily="@font/avenir_next_bold"
                        android:textColor="@color/blackColor"
                        android:gravity="end"
                        app:autoSizePresetSizes="@array/autosize_text_sizes"
                        app:autoSizeTextType="uniform" />

                    <id.co.bri.brimo.ui.customviews.loading.BlueHorizontalDottedProgress
                        android:id="@+id/pbSaldo"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_above="@id/layout_detail"
                        android:layout_alignParentEnd="true"
                        android:layout_marginTop="@dimen/size_10dp"
                        android:layout_marginBottom="@dimen/space_x1_half"
                        android:visibility="gone" />

                    <LinearLayout
                        android:id="@+id/layout_detail"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:layout_alignParentBottom="true"
                        android:gravity="end"
                        android:orientation="horizontal">

                        <TextView
                            android:id="@+id/tv_detail_rekening"
                            style="@style/BodySmallText.DemiBold.BluePrimary"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:layout_marginEnd="@dimen/space_half"
                            android:text="@string/lihat_detail_underline"
                            android:visibility="gone" />

                        <ImageView
                            android:id="@+id/iv_panah"
                            android:layout_width="@dimen/space_x2"
                            android:layout_height="@dimen/space_x2"
                            android:src="@drawable/ic_arrow_right_blue"
                            android:visibility="gone" />
                    </LinearLayout>
                </RelativeLayout>
            </RelativeLayout>
        </LinearLayout>
    </RelativeLayout>
</androidx.cardview.widget.CardView>