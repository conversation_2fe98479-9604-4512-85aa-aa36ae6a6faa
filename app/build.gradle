apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply plugin: 'kotlin-parcelize'
apply plugin: 'kotlin-kapt'
apply plugin: 'org.jetbrains.dokka'
apply plugin: 'org.jetbrains.kotlin.android'
apply plugin: "org.sonarqube"
apply plugin: 'com.google.gms.google-services'
apply plugin: 'com.google.firebase.crashlytics'
apply plugin: 'com.google.firebase.firebase-perf'
apply plugin: 'com.facebook.react'
apply from: "$project.rootDir/jacoco.gradle"

android {
    namespace 'id.co.bri.brimo'

    testOptions {
        unitTests.all {
            jacoco {
                includeNoLocationClasses = true
            }
        }
    }

    ndkVersion '25.0.8775105'

    compileSdk 34

    defaultConfig {
        applicationId "id.co.bri.brimo"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode 652838
        versionName "2.84.0"
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        renderscriptTargetApi 25
        renderscriptSupportModeEnabled true

        packagingOptions {
            pickFirst 'lib/arm64-v8a/libc++_shared.so'
            pickFirst 'lib/armeabi/libc++_shared.so'
            pickFirst 'lib/armeabi-v7a/libc++_shared.so'
            pickFirst 'lib/arm64-v8a/libmarsxlog.so'
            pickFirst 'lib/armeabi/libmarsxlog.so'
            pickFirst 'lib/armeabi-v7a/libmarsxlog.so'
            pickFirst 'lib/arm64-v8a/libv8jni.so'
            pickFirst 'lib/armeabi-v7a/libc++_shared.so'
            pickFirst 'lib/arm64-v8a/libc++_shared.so'
            pickFirst 'lib/arm64-v8a/libhermes.so'
            pickFirst 'lib/armeabi-v7a/libhermes.so'
        }
    }

    buildTypes {
        debug {
            debuggable true
        }
        release {
            debuggable false
            minifyEnabled true
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            signingConfig signingConfigs.debug
        }
    }

    dokka {
        outputFormat = 'html'
        outputDirectory = "$buildDir/javadoc"
    }

    // Define the endpoint strings
    def endpointFile = file('endpoint.properties')
    def Properties endpointProperties = new Properties()
    endpointProperties.load(new FileInputStream(endpointFile))
    flavorDimensions "api"
    productFlavors {
        development {
            versionNameSuffix '-dev'
            buildConfigField 'String', 'M_WEB_URL', endpointProperties['DEVELOPMENT_URL']
            buildConfigField 'String', 'M_API_URL', endpointProperties['DEVELOPMENT_URL_API']
            buildConfigField 'String', 'M_API_KEY', endpointProperties['DEVELOPMENT_API_KEY']
            buildConfigField 'String', 'M_API_SB_URL', endpointProperties['SANDBOX_URL_API']
            buildConfigField 'String', 'M_SMS_DEST', endpointProperties['SMS_DEST_DEV']
            buildConfigField 'String', 'M_RESET_URL', endpointProperties['DEVELOPMENT_RESET']
            buildConfigField 'String', 'M_DS_URL', endpointProperties['DEVELOPMENT_DS']
            buildConfigField 'String', 'M_REISSUE_KEY', endpointProperties['DEVELOPMENT_REISSUE_KEY']
            resValue "string", "app_name", "BRImo"

            buildConfigField 'String', 'M_ENDPOINT_MINIO', endpointProperties['DEVELOPMENT_ENDPOINT_MINIO']
            buildConfigField 'String', 'M_ACCESS_KEY_MINIO', endpointProperties['DEVELOPMENT_ACCESS_KEY_MINIO']
            buildConfigField 'String', 'M_SECRET_KEY_MINIO', endpointProperties['DEVELOPMENT_SECRET_KEY_MINIO']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_MINIO', endpointProperties['DEVELOPMENT_BUCKET_REGISTRASI_MINIO']
            buildConfigField 'String', 'M_BUCKET_ESTATEMENT_MINIO', endpointProperties['DEVELOPMENT_BUCKET_DOWNLOAD_ESTATEMENT']
            buildConfigField 'String', 'M_BUCKET_ONBOARDING_MINIO', endpointProperties['DEVELOPMENT_BUCKET_ONBOARDING_MINIO']
            buildConfigField 'String', 'M_BUCKET_APPLY_VCC_MINIO', endpointProperties['DEVELOPMENT_BUCKET_APPLY_VCC_MINIO']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_FINANSIAL_MINIO', endpointProperties['DEVELOPMENT_BUCKET_REGISTRASI_FINANSIAL_MINIO']

            buildConfigField 'String', 'M_APPLICATION_ID', endpointProperties['DEVELOPMENT_APPLICATION_ID']
            buildConfigField 'String', 'M_CLIENT_ID', endpointProperties['DEVELOPMENT_CLIENT_ID']
            buildConfigField 'String', 'M_CLIENT_SECRET', endpointProperties['DEVELOPMENT_CLIENT_SECRET']
            buildConfigField 'String', 'M_SALT', endpointProperties['DEVELOPMENT_SALT']
            buildConfigField 'String', 'M_PUBLIC_KEY', endpointProperties['DEVELOPMENT_PUBLIC_KEY']
            buildConfigField 'String', 'M_PRIVATE_KEY', endpointProperties['DEVELOPMENT_PRIVATE_KEY']
            buildConfigField 'String', 'M_PRIVY_PASS_ENV', endpointProperties['DEVELOPMENT_PRIVY_PASS_ENV']

            buildConfigField 'String', 'M_DKL_CLIENT_ID', endpointProperties['DEVELOPMENT_C2_CLIENT_ID']
            buildConfigField 'String', 'M_DKL_CLIENT_SECRET', endpointProperties['DEVELOPMENT_C2_CLIENT_SECRET']
            buildConfigField 'String', 'M_DKL_SCOPE', endpointProperties['DEVELOPMENT_C2_SCOPE']
            buildConfigField 'String', 'M_DKL_ENDPOINT', endpointProperties['DEVELOPMENT_C2_ENDPOINT']
            buildConfigField 'String', 'M_DKL_GRANT_TYPE', endpointProperties['DEVELOPMENT_C2_GRANT_TYPE']

            buildConfigField 'String', 'M_API_KEY_VIDA', endpointProperties['PRODUCTION_API_KEY_VIDA']
            buildConfigField 'String', 'M_LICENSE_KEY_VIDA', endpointProperties['PRODUCTION_LICENSE_KEY_VIDA']
            manifestPlaceholders = [apiKey: "PRODUCTION_ACTIVATION_KEY_VIDA"]
        }
        production {
            buildConfigField 'String', 'M_WEB_URL', endpointProperties['PRODUCTION_URL']
            buildConfigField 'String', 'M_API_URL', endpointProperties['PRODUCTION_URL_API']
            buildConfigField 'String', 'M_API_KEY', endpointProperties['PRODUCTION_API_KEY']
            buildConfigField 'String', 'M_API_SB_URL', endpointProperties['SANDBOX_URL_API']
            buildConfigField 'String', 'M_SMS_DEST', endpointProperties['SMS_DEST_PROD']
            buildConfigField 'String', 'M_RESET_URL', endpointProperties['PRODUCTION_RESET']
            buildConfigField 'String', 'M_DS_URL', endpointProperties['PRODUCTION_DS']
            buildConfigField 'String', 'M_REISSUE_KEY', endpointProperties['PRODUCTION_REISSUE_KEY']
            resValue "string", "app_name", "BRImo"

            buildConfigField 'String', 'M_ENDPOINT_MINIO', endpointProperties['PRODUCTION_ENDPOINT_MINIO']
            buildConfigField 'String', 'M_ACCESS_KEY_MINIO', endpointProperties['PRODUCTION_ACCESS_KEY_MINIO']
            buildConfigField 'String', 'M_SECRET_KEY_MINIO', endpointProperties['PRODUCTION_SECRET_KEY_MINIO']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_MINIO', endpointProperties['PRODUCTION_BUCKET_REGISTRASI_MINIO']
            buildConfigField 'String', 'M_BUCKET_ESTATEMENT_MINIO', endpointProperties['PRODUCTION_BUCKET_DOWNLOAD_ESTATEMENT']
            buildConfigField 'String', 'M_BUCKET_ONBOARDING_MINIO', endpointProperties['PRODUCTION_BUCKET_ONBOARDING_MINIO']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_FINANSIAL_MINIO', endpointProperties['PRODUCTION_BUCKET_REGISTRASI_FINANSIAL_MINIO']
            buildConfigField 'String', 'M_BUCKET_APPLY_VCC_MINIO', endpointProperties['PRODUCTION_BUCKET_APPLY_VCC_MINIO']

            buildConfigField 'String', 'M_APPLICATION_ID', endpointProperties['PRODUCTION_APPLICATION_ID']
            buildConfigField 'String', 'M_CLIENT_ID', endpointProperties['PRODUCTION_CLIENT_ID']
            buildConfigField 'String', 'M_CLIENT_SECRET', endpointProperties['PRODUCTION_CLIENT_SECRET']
            buildConfigField 'String', 'M_SALT', endpointProperties['PRODUCTION_SALT']
            buildConfigField 'String', 'M_PUBLIC_KEY', endpointProperties['PRODUCTION_PUBLIC_KEY']
            buildConfigField 'String', 'M_PRIVATE_KEY', endpointProperties['PRODUCTION_PRIVATE_KEY']
            buildConfigField 'String', 'M_PRIVY_PASS_ENV', endpointProperties['PRODUCTION_PRIVY_PASS_ENV']

            buildConfigField 'String', 'M_DKL_CLIENT_ID', endpointProperties['PRODUCTION_C2_CLIENT_ID']
            buildConfigField 'String', 'M_DKL_CLIENT_SECRET', endpointProperties['PRODUCTION_C2_CLIENT_SECRET']
            buildConfigField 'String', 'M_DKL_SCOPE', endpointProperties['PRODUCTION_C2_SCOPE']
            buildConfigField 'String', 'M_DKL_ENDPOINT', endpointProperties['PRODUCTION_C2_ENDPOINT']
            buildConfigField 'String', 'M_DKL_GRANT_TYPE', endpointProperties['PRODUCTION_C2_GRANT_TYPE']

            buildConfigField 'String', 'M_API_KEY_VIDA', endpointProperties['PRODUCTION_API_KEY_VIDA']
            buildConfigField 'String', 'M_LICENSE_KEY_VIDA', endpointProperties['PRODUCTION_LICENSE_KEY_VIDA']
            manifestPlaceholders = [apiKey: "PRODUCTION_ACTIVATION_KEY_VIDA"]
        }
        sandbox {
            versionNameSuffix '-stagingsandbox'
            buildConfigField 'String', 'M_WEB_URL', endpointProperties['SANDBOX_URL']
            buildConfigField 'String', 'M_API_URL', endpointProperties['SANDBOX_URL_API']
            buildConfigField 'String', 'M_API_KEY', endpointProperties['SANDBOX_API_KEY']
            buildConfigField 'String', 'M_API_SB_URL', endpointProperties['SANDBOX_URL_API']
            buildConfigField 'String', 'M_SMS_DEST', endpointProperties['SMS_DEST_DEV']
            buildConfigField 'String', 'M_RESET_URL', endpointProperties['DEVELOPMENT_RESET']
            buildConfigField 'String', 'M_DS_URL', endpointProperties['SANDBOX_DS']
            buildConfigField 'String', 'M_REISSUE_KEY', endpointProperties['SANDBOX_REISSUE_KEY']
            resValue "string", "app_name", "(Dev)BRImo"

            buildConfigField 'String', 'M_ENDPOINT_MINIO', endpointProperties['DEVELOPMENT_ENDPOINT_MINIO']
            buildConfigField 'String', 'M_ACCESS_KEY_MINIO', endpointProperties['DEVELOPMENT_ACCESS_KEY_MINIO']
            buildConfigField 'String', 'M_SECRET_KEY_MINIO', endpointProperties['DEVELOPMENT_SECRET_KEY_MINIO']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_MINIO', endpointProperties['DEVELOPMENT_BUCKET_REGISTRASI_MINIO']
            buildConfigField 'String', 'M_BUCKET_ESTATEMENT_MINIO', endpointProperties['DEVELOPMENT_BUCKET_DOWNLOAD_ESTATEMENT']
            buildConfigField 'String', 'M_BUCKET_ONBOARDING_MINIO', endpointProperties['DEVELOPMENT_BUCKET_ONBOARDING_MINIO']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_FINANSIAL_MINIO', endpointProperties['DEVELOPMENT_BUCKET_REGISTRASI_FINANSIAL_MINIO']
            buildConfigField 'String', 'M_BUCKET_APPLY_VCC_MINIO', endpointProperties['DEVELOPMENT_BUCKET_APPLY_VCC_MINIO']

            buildConfigField 'String', 'M_APPLICATION_ID', endpointProperties['DEVELOPMENT_APPLICATION_ID']
            buildConfigField 'String', 'M_CLIENT_ID', endpointProperties['DEVELOPMENT_CLIENT_ID']
            buildConfigField 'String', 'M_CLIENT_SECRET', endpointProperties['DEVELOPMENT_CLIENT_SECRET']
            buildConfigField 'String', 'M_SALT', endpointProperties['DEVELOPMENT_SALT']
            buildConfigField 'String', 'M_PUBLIC_KEY', endpointProperties['DEVELOPMENT_PUBLIC_KEY']
            buildConfigField 'String', 'M_PRIVATE_KEY', endpointProperties['DEVELOPMENT_PRIVATE_KEY']
            buildConfigField 'String', 'M_PRIVY_PASS_ENV', endpointProperties['DEVELOPMENT_PRIVY_PASS_ENV']

            buildConfigField 'String', 'M_DKL_CLIENT_ID', endpointProperties['PRODUCTION_C2_CLIENT_ID']
            buildConfigField 'String', 'M_DKL_CLIENT_SECRET', endpointProperties['PRODUCTION_C2_CLIENT_SECRET']
            buildConfigField 'String', 'M_DKL_SCOPE', endpointProperties['PRODUCTION_C2_SCOPE']
            buildConfigField 'String', 'M_DKL_ENDPOINT', endpointProperties['PRODUCTION_C2_ENDPOINT']
            buildConfigField 'String', 'M_DKL_GRANT_TYPE', endpointProperties['PRODUCTION_C2_GRANT_TYPE']

            buildConfigField 'String', 'M_API_KEY_VIDA', endpointProperties['DEVELOPMENT_API_KEY_VIDA']
            buildConfigField 'String', 'M_LICENSE_KEY_VIDA', endpointProperties['DEVELOPMENT_LICENSE_KEY_VIDA']
            manifestPlaceholders = [apiKey: "DEVELOPMENT_ACTIVATION_KEY_VIDA"]
        }

        pentest {
            versionNameSuffix '-pentest'
            buildConfigField 'String', 'M_WEB_URL', endpointProperties['PENTEST_URL']
            buildConfigField 'String', 'M_API_URL', endpointProperties['PENTEST_URL_API']
            buildConfigField 'String', 'M_API_KEY', endpointProperties['PENTEST_API_KEY']
            buildConfigField 'String', 'M_API_SB_URL', endpointProperties['PENTEST_URL_API']
            buildConfigField 'String', 'M_SMS_DEST', endpointProperties['SMS_DEST_DEV']
            buildConfigField 'String', 'M_RESET_URL', endpointProperties['DEVELOPMENT_RESET']
            buildConfigField 'String', 'M_DS_URL', endpointProperties['PENTEST_DS']
            buildConfigField 'String', 'M_REISSUE_KEY', endpointProperties['PENTEST_REISSUE_KEY']
            resValue "string", "app_name", "(pentest)BRImo"

            buildConfigField 'String', 'M_ENDPOINT_MINIO', endpointProperties['DEVELOPMENT_ENDPOINT_MINIO']
            buildConfigField 'String', 'M_ACCESS_KEY_MINIO', endpointProperties['DEVELOPMENT_ACCESS_KEY_MINIO']
            buildConfigField 'String', 'M_SECRET_KEY_MINIO', endpointProperties['DEVELOPMENT_SECRET_KEY_MINIO']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_MINIO', endpointProperties['DEVELOPMENT_BUCKET_REGISTRASI_MINIO']
            buildConfigField 'String', 'M_BUCKET_ESTATEMENT_MINIO', endpointProperties['DEVELOPMENT_BUCKET_DOWNLOAD_ESTATEMENT']
            buildConfigField 'String', 'M_BUCKET_ONBOARDING_MINIO', endpointProperties['DEVELOPMENT_BUCKET_ONBOARDING_MINIO']
            buildConfigField 'String', 'M_BUCKET_APPLY_VCC_MINIO', endpointProperties['DEVELOPMENT_BUCKET_APPLY_VCC_MINIO']


            buildConfigField 'String', 'M_APPLICATION_ID', endpointProperties['DEVELOPMENT_APPLICATION_ID']
            buildConfigField 'String', 'M_CLIENT_ID', endpointProperties['DEVELOPMENT_CLIENT_ID']
            buildConfigField 'String', 'M_CLIENT_SECRET', endpointProperties['DEVELOPMENT_CLIENT_SECRET']
            buildConfigField 'String', 'M_SALT', endpointProperties['DEVELOPMENT_SALT']
            buildConfigField 'String', 'M_PUBLIC_KEY', endpointProperties['DEVELOPMENT_PUBLIC_KEY']
            buildConfigField 'String', 'M_PRIVATE_KEY', endpointProperties['DEVELOPMENT_PRIVATE_KEY']
            buildConfigField 'String', 'M_BUCKET_REGISTRASI_FINANSIAL_MINIO', endpointProperties['DEVELOPMENT_BUCKET_REGISTRASI_FINANSIAL_MINIO']
            buildConfigField 'String', 'M_PRIVY_PASS_ENV', endpointProperties['DEVELOPMENT_PRIVY_PASS_ENV']

            buildConfigField 'String', 'M_DKL_CLIENT_ID', endpointProperties['DEVELOPMENT_C2_CLIENT_ID']
            buildConfigField 'String', 'M_DKL_CLIENT_SECRET', endpointProperties['DEVELOPMENT_C2_CLIENT_SECRET']
            buildConfigField 'String', 'M_DKL_SCOPE', endpointProperties['DEVELOPMENT_C2_SCOPE']
            buildConfigField 'String', 'M_DKL_ENDPOINT', endpointProperties['DEVELOPMENT_C2_ENDPOINT']
            buildConfigField 'String', 'M_DKL_GRANT_TYPE', endpointProperties['DEVELOPMENT_C2_GRANT_TYPE']

            buildConfigField 'String', 'M_API_KEY_VIDA', endpointProperties['DEVELOPMENT_API_KEY_VIDA']
            buildConfigField 'String', 'M_LICENSE_KEY_VIDA', endpointProperties['DEVELOPMENT_LICENSE_KEY_VIDA']
            manifestPlaceholders = [apiKey: "DEVELOPMENT_ACTIVATION_KEY_VIDA"]
        }
    }
    packagingOptions {
        exclude 'META-INF/androidx.localbroadcastmanager_localbroadcastmanager.version'
        exclude 'META-INF/androidx.preference_preference.version'
        pickFirst 'lib/arm64-v8a/libc++_shared.so'
        pickFirst 'lib/armeabi-v7a/libc++_shared.so'
        pickFirst 'lib/x86/libc++_shared.so'
        pickFirst 'lib/x86_64/libc++_shared.so'
        pickFirst 'lib/arm64-v8a/libhermes.so'
        pickFirst 'lib/armeabi-v7a/libhermes.so'
        pickFirst 'lib/x86/libhermes.so'
        pickFirst 'lib/x86_64/libhermes.so'
    }

    buildFeatures {
        dataBinding true
        viewBinding true
        buildConfig true
    }

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }

    kotlinOptions {
        jvmTarget = '17'
    }
}

repositories {
    maven { url 'https://jitpack.io' }
}

dependencies {
    //versioning config
    def dagger_ver = "2.50"
    def butter_ver = "7.0.1"
    def aws_ver = "2.68.0"
    def aws_cog_ver = "2.20.1"
    def room_ver = "2.6.1"
    def retrofit_ver = "2.9.0"
    def camerax_ver = "1.3.4"
    def in_app_update_ver = "2.1.0"
    def firebase_crashlytics_ver = "18.4.0"
    def firebase_analytics_ver = "21.3.0"
    def firebase_bom_ver = "32.2.2"
    def zolozKitVersion = "1.5.4.250430113339"

    // React Native configuration
    def hermesEnabled = true

    implementation fileTree(dir: 'libs', include: ['*.jar'])
    implementation 'androidx.appcompat:appcompat:1.6.1'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'
    implementation 'androidx.recyclerview:recyclerview:1.3.1'
    implementation 'androidx.cardview:cardview:1.0.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.1.4'
    implementation "androidx.core:core-ktx:1.13.1"
    implementation "androidx.fragment:fragment-ktx:1.6.1"
    implementation 'androidx.preference:preference:1.2.0'
    implementation 'com.google.android.material:material:1.10.0'

    //sdp
    implementation 'com.intuit.sdp:sdp-android:1.0.6'
    implementation 'org.chromium.net:cronet-embedded:76.3809.111'
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.3.1'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.3.1'
    implementation 'com.google.ar.sceneform:filament-android:1.17.1'

    //butterknife
    implementation "com.jakewharton:butterknife:$butter_ver"
    kapt "com.jakewharton:butterknife:$butter_ver"
    annotationProcessor "com.jakewharton:butterknife:$butter_ver"

    implementation 'com.github.bmarrdev:android-DecoView-charting:v1.2'

    //Dagger
    implementation "com.google.dagger:dagger:$dagger_ver"
    kapt "com.google.dagger:dagger-compiler:$dagger_ver"
    annotationProcessor "com.google.dagger:dagger-compiler:$dagger_ver"

    implementation "com.google.dagger:dagger-android:$dagger_ver"
    implementation "com.google.dagger:dagger-android-support:$dagger_ver"
    kapt "com.google.dagger:dagger-android-processor:$dagger_ver"
    annotationProcessor "com.google.dagger:dagger-android-processor:$dagger_ver"

    implementation 'javax.inject:javax.inject:1'

    //////////////////////////////  CUSTOM VIEW   /////////////////////////////////

    implementation 'id.co.bri.brimo.stateprogressbar:stateprogressbar:1.0.1' //State Progress Bar
    implementation 'com.github.kizitonwose:CalendarView:0.2.8' //Calendar View
    implementation 'com.github.rasoulmiri:Skeleton:v1.1.4'   //SkeletonVielw
    implementation 'id.co.bri.brimo.skeleton:library:1.0.4'            //SkeletonVielw
    implementation 'io.supercharge:shimmerlayout:2.1.0'     //SkeletonVielw
    implementation 'com.ogaclejapan.smarttablayout:library:1.6.1@aar'
    implementation 'id.co.bri.brimo.loadingdots:loading-dots:1.0.1' // Bouncing dot
    implementation 'info.hoang8f:android-segmented:1.0.6' // Segmented Button
    implementation 'com.github.nisrulz:screenshott:2.0.0' // screenshoott
    implementation 'com.github.bmarrdev:android-DecoView-charting:v1.2' //circle progressView
    implementation 'id.co.bri.brimo.dotprogressbar:dot-progress-bar:1.0.2' //dot progress
    implementation 'id.co.bri.brimo.pageindicatorview:pageindicatorview:1.0.2' //
    implementation 'com.github.mreram:ticketview:1.0.0' //ticket View receipt
    implementation 'com.github.RobertApikyan:SegmentedControl:1.2.0' // segmented button radius
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.5.1'
    implementation 'com.github.yuriy-budiyev:code-scanner:2.3.0' //scanner
    implementation 'com.joooonho:selectableroundedimageview:1.0.1' // full imageView
    implementation 'pl.droidsonroids.gif:android-gif-drawable:1.2.18' //GIF
    implementation 'com.tbuonomo:dotsindicator:5.0'// dot panjang
    implementation 'com.github.delight-im:Android-AdvancedWebView:v3.2.1' // webview
    implementation 'androidx.browser:browser:1.3.0' //chrome tab
    implementation 'com.github.faranjit:currency-edittext:1.0.0'//editext
    implementation 'com.github.CameraKit:camerakit-android:v1.0.0-beta3.11'
    implementation 'com.github.PhilJay:MPAndroidChart:v3.1.0' //chart
    implementation 'com.github.AAChartModel:AAChartCore-Kotlin:7.1.0' //chart
    implementation 'com.github.teresaholfeld:Stories:1.1.4' //story
    implementation 'id.co.bri.brimo.bannerslider:bannerslider:2.0.2'  //bannerslider
    implementation 'id.co.bri.brimo.readmoretextview:readmoretextview:1.0.2' //readmore textview

    ////////////////////////////////////////////////////////////////////////////////

    //Room
    implementation "androidx.room:room-runtime:$room_ver"
    implementation "androidx.room:room-rxjava2:$room_ver"
    kapt "androidx.room:room-compiler:$room_ver"
    annotationProcessor "androidx.room:room-compiler:$room_ver"

    //RXJava
    implementation 'io.reactivex.rxjava2:rxjava:2.2.21'
    implementation 'io.reactivex.rxjava2:rxandroid:2.1.1'

    // retrofit
    implementation "com.squareup.retrofit2:retrofit:$retrofit_ver"
    implementation "com.squareup.retrofit2:adapter-rxjava2:$retrofit_ver"
    implementation "com.squareup.retrofit2:converter-scalars:$retrofit_ver"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_ver"
    implementation 'com.google.code.gson:gson:2.10'

    //OkHTTP
    implementation 'com.squareup.okhttp3:logging-interceptor:4.11.0'
    implementation 'com.squareup.okhttp3:okhttp-urlconnection:4.11.0'
    implementation 'com.squareup.okhttp3:okhttp:4.11.0'

    def lifecycle_ver = "2.2.0"
    implementation "androidx.lifecycle:lifecycle-extensions:$lifecycle_ver"
    kapt "androidx.lifecycle:lifecycle-compiler:$lifecycle_ver"
    annotationProcessor "androidx.lifecycle:lifecycle-compiler:$lifecycle_ver"

    //Threetenabp
    implementation 'com.jakewharton.threetenabp:threetenabp:1.1.1'
    implementation 'com.scottyab:rootbeer-lib:0.0.7'
    implementation "org.jetbrains.kotlin:kotlin-stdlib:$kotlin_version"

    implementation 'androidx.biometric:biometric:1.2.0-alpha05'
    implementation 'com.github.franmontiel:PersistentCookieJar:v1.0.1'
    implementation 'com.github.bumptech.glide:glide:4.12.0'

    androidTestImplementation 'androidx.test:runner:1.3.0-alpha02'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.3.0-alpha02'
    implementation 'androidx.test:monitor:1.5.0'

    testImplementation "org.mockito:mockito-core:3.11.2"
    testImplementation 'org.mockito:mockito-inline:3.11.2'

    implementation 'junit:junit:4.12'
    implementation 'androidx.test:monitor:1.5.0'
    testImplementation "org.mockito:mockito-core:3.11.2"
    testImplementation 'org.mockito:mockito-inline:3.11.2'
    testImplementation "org.jetbrains.kotlin:kotlin-reflect:$kotlin_version"

    //Registrasi BRImo
    implementation 'com.google.android.gms:play-services-vision:18.0.0'
    implementation 'com.github.JesusM:HoloCircleSeekBar:2.2.2'
    implementation 'com.wajahatkarim:EasyFlipView:3.0.3'

    //firebase
    implementation 'com.google.firebase:firebase-core:21.0.0'
    implementation 'com.google.firebase:firebase-messaging:23.0.0'
    implementation "com.google.firebase:firebase-analytics:${firebase_analytics_ver}"
    implementation "com.google.firebase:firebase-crashlytics:${firebase_crashlytics_ver}"
    implementation platform("com.google.firebase:firebase-bom:$firebase_bom_ver")
    implementation("com.google.firebase:firebase-perf")
    // firebase ml vision, used : qrcode scanner
    implementation 'com.google.mlkit:barcode-scanning:17.3.0'

    //SSL Pinning
    implementation 'com.datatheorem.android.trustkit:trustkit:1.1.3'

    //SQLite
    implementation "net.zetetic:android-database-sqlcipher:4.5.2"
    implementation "androidx.sqlite:sqlite:2.0.1"

    implementation project(path: ':sdks:brizzi-debug')

    //exoplayer
    implementation 'com.google.android.exoplayer:exoplayer:2.18.2'

    //cameraX
    implementation "androidx.camera:camera-core:${camerax_ver}"
    implementation "androidx.camera:camera-camera2:${camerax_ver}"
    implementation "androidx.camera:camera-lifecycle:${camerax_ver}"
    implementation "androidx.camera:camera-view:${camerax_ver}"
    implementation "androidx.camera:camera-video:${camerax_ver}"
    implementation "androidx.camera:camera-extensions:${camerax_ver}"

    //Navigation
    implementation 'androidx.navigation:navigation-fragment-ktx:2.5.3'
    implementation 'androidx.navigation:navigation-ui-ktx:2.5.3'

    //video compressor
    implementation 'com.googlecode.mp4parser:isoparser:1.0.6'

    //PDF
    implementation 'com.github.mhiew:android-pdf-viewer:3.2.0-beta.1'

    //chuck
    debugImplementation "id.co.bri.brimo.chucker:library:1.0.0"
    releaseImplementation "id.co.bri.brimo.chucker:library-no-op:1.0.0"
    implementation 'com.github.stealthcopter:AndroidNetworkTools:0.4.5.3'

    // Aws SDK
    implementation "com.amazonaws:aws-android-sdk-core:$aws_ver"
    implementation "com.amazonaws:aws-android-sdk-cognito:$aws_cog_ver"
    implementation "com.amazonaws:aws-android-sdk-s3:$aws_ver"
    implementation "com.amazonaws:aws-android-sdk-ddb:$aws_ver"

    // Appsflyer
    implementation 'com.appsflyer:af-android-sdk:6.9.0'
    implementation "com.android.installreferrer:installreferrer:2.2"

    //Lottie
    implementation 'com.airbnb.android:lottie:6.0.0'

    //Keyboard Numpad
    implementation 'com.github.omadahealth.typefaceview:typefaceview:1.5.0@aar'


    implementation 'io.netty:netty-all:4.1.17.Final'
    implementation 'com.google.android.flexbox:flexbox:3.0.0'
    implementation 'com.github.hrskrs:InstaDotView:1.1'

    //Location
    implementation 'com.google.android.gms:play-services-location:21.0.1'

    implementation 'com.google.android.gms:play-services-maps:18.2.0'
    implementation 'com.google.maps.android:android-maps-utils:2.3.0'

    //In-App Update
    implementation "com.google.android.play:app-update:$in_app_update_ver"
    implementation "com.google.android.play:app-update-ktx:$in_app_update_ver"

    //AI face detection
    implementation 'com.google.mlkit:face-detection:16.1.7'

    implementation(platform("org.jetbrains.kotlin:kotlin-bom:1.8.22"))

    //Video Compressor
    implementation files('libs/lightcompressor/lightcompressor.aar')

    //Liveness SDK
    implementation files('libs/privy/sdkliveness.aar')

    //coil
    implementation("io.coil-kt:coil:2.6.0")

    //work manager
    implementation("androidx.work:work-runtime-ktx:2.9.0")

    // avaya voip
    implementation files('libs/avaya/oceanacustomerwebvoicevideo.aar')

    implementation 'androidx.localbroadcastmanager:localbroadcastmanager:1.0.0'

    implementation project(":nfc_payment")

    // SMS Retriever
    implementation("com.google.android.gms:play-services-auth:21.2.0")
    implementation("com.google.android.gms:play-services-auth-api-phone:18.1.0")

    //ExoPlayer for playing audio
    implementation 'com.google.android.exoplayer:exoplayer:2.17.1'

    //singalarity
    implementation fileTree(dir: 'libs/singalarity', include: ['*.aar'])
    implementation 'androidx.security:security-crypto:1.1.0-alpha03'
    implementation 'com.annimon:stream:1.1.8'
    api("com.google.guava:guava:30.1.1-android")

    // SDK VIDA DEV
//     implementation "id.vida:liveness-sandbox:1.7.6"
    // SDK VIDA PROD
//    debugImplementation "id.vida:liveness-np:1.7.7"
//    releaseImplementation "id.vida:liveness:1.8.2"


    implementation "com.github.skydoves:balloon:1.4.7"


    // SDK Zoloz
    implementation 'com.alibaba:fastjson:2.0.21.android'
    implementation "com.squareup.okio:okio:1.17.4@jar"
    implementation "com.zoloz.android.build:zolozkit:$zolozKitVersion"

    // SDK Privy
//    implementation("id.privy.privypass:privypass-liveness:3.1.1")

    // React Native dependencies
    // implementation 'com.facebook.react:react-android:0.73.11'
    // implementation 'com.facebook.react:hermes-android:0.73.11'
    implementation "com.facebook.react:react-android"
    implementation "com.facebook.react:hermes-android"
}

apply from: file("../../node_modules/@react-native-community/cli-platform-android/native_modules.gradle"); applyNativeModulesAppBuildGradle(project)
